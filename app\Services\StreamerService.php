<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class StreamerService
{
    protected $twitchClient;
    protected $trovoClient;

    public function __construct()
    {
        if (config('app.STREAMER_ENABLED')) {
            $this->twitchClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('app.TWITCH_CLIENT_SECRET'),
                'Client-ID' => config('app.TWITCH_CLIENT_ID'),
            ])->baseUrl('https://api.twitch.tv/helix/');
            \Log::info('Twitch Client ID: ' . config('app.TWITCH_CLIENT_ID'));
            \Log::info('Twitch Token: ' . config('app.TWITCH_CLIENT_SECRET'));


            // Настройка Trovo клиента с правильным токеном
            $this->trovoClient = Http::withHeaders([
                'Client-ID' => config('app.TROVO_CLIENT_ID'),
                'Authorization' => 'Bearer ' . config('app.TROVO_CLIENT_SECRET'), // Правильный Trovo токен
            ])->baseUrl('https://open-api.trovo.live/openplatform/');

        }
    }

    public function getTwitchStreamersData($usernames)
    {
        $streamers = [];

        foreach ($usernames as $username) {
            $streamerUrl = 'streams?user_login=' . $username['twitch_username'];

            // Запрос к Twitch API
            $streamResponse = $this->twitchClient->get($streamerUrl);

            \Log::info('Twitch API Response: ' . json_encode($streamResponse->json()));

            if ($streamResponse->successful()) {
                $responseData = $streamResponse->json();
                if (!empty($responseData['data'])) {
                    $stream = $responseData['data'][0];
                    $streamers[] = [
                        'username' => $username['twitch_username'],
                        'platform' => 'twitch',
                        'viewer_count' => $stream['viewer_count'] ?? 0,
                        'is_online' => true,
                        'hours_streamed' => 0,
                        'video_url' => 'https://player.twitch.tv/?channel=' . $username['twitch_username'] . '&parent=localhost',
                    ];
                } else {
                    // Если данных о стриме нет
                    $streamers[] = [
                        'username' => $username['twitch_username'],
                        'platform' => 'twitch',
                        'viewer_count' => 0,
                        'is_online' => false,
                        'hours_streamed' => 0,
                    ];
                }
            } else {
                // Логирование ошибки, если запрос к API не удался
                \Log::error('Ошибка получения данных от Twitch: ' . $streamResponse->body());
            }
        }

        return $streamers;
    }

    public function getTrovoStreamersData($usernames)
    {
        $streamers = [];

        foreach ($usernames as $username) {
            $trovoUrl = 'https://open-api.trovo.live/openplatform/channels/id'; // Эндпоинт Trovo API

            // Данные для отправки
            $trovoData = [
                'username' => $username['trovo_username'],
            ];

            // Логируем отправляемые данные
            \Log::info('Trovo Request Data: ' . json_encode($trovoData));

            // Запрос к Trovo API
            $response = $this->trovoClient->post($trovoUrl, $trovoData); // Отправляем запрос

            \Log::info('Trovo API Response: ' . $response->body()); // Логируем ответ API

            if ($response->successful()) {
                $responseData = $response->json();

                // Проверка структуры данных
                if (!empty($responseData)) {
                    $streamers[] = [
                        'username' => $username['trovo_username'],
                        'platform' => 'trovo',
                        'viewer_count' => $responseData['current_viewers'] ?? 0,
                        'is_online' => true,
                        'hours_streamed' => 0,
                        'video_url' => 'https://player.trovo.live/embed/player?streamername=' . $username['trovo_username'] . '&autoplay=1',
                    ];
                } else {
                    // Если данных о стриме нет
                    $streamers[] = [
                        'username' => $username['trovo_username'],
                        'platform' => 'trovo',
                        'viewer_count' => 0,
                        'is_online' => false,
                        'hours_streamed' => 0,
                    ];
                }
            } else {
                // Логирование ошибки, если запрос к API не удался
                \Log::error('Ошибка получения данных от Trovo: ' . $response->body());
            }
        }

        return $streamers;
    }


    public function getAllStreamersData($twitchUsernames, $trovoUsernames)
    {
        $twitchStreamers = $this->getTwitchStreamersData($twitchUsernames);
        $trovoStreamers = $this->getTrovoStreamersData($trovoUsernames);

        return array_merge($twitchStreamers, $trovoStreamers);
    }
}
