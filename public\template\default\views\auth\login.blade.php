@extends('template::layouts.auth')
@section('title', __('Sign in'))

@section('content')
    @if(session('status'))
        <div class="alert alert-success">
            <p>{{ session('status') }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif

    <div class="title">{{ __('Sign in') }}</div>

    <form method="POST" action="{{ route('login') }}">
        @csrf
        <div class="form-group">
            <input id="login" class="form-control"
                   type="text"
                   name="login"
                   value="{{ old('login') }}"
                   placeholder="{{ __('Your master account or email') }}" required />
            @foreach ($errors->get('login') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group">
            <div class="password-group">
                <input id="password" class="form-control"
                       type="password"
                       name="password"
                       placeholder="{{ __('Password') }}"
                       required />
                <span class="input-group-text password-toggle" data-target="password" data-state="hidden"></span>
            </div>
            @foreach ($errors->get('password') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group">
            <label class="checkbox-container">
                <input type="checkbox" name="remember" checked>
                <span class="checkmark"></span>
                {{ __('Remember me') }}
            </label>
        </div>
        <div class="form-group login_button">
            <button type="submit" class="btn-primary">
                <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-corner-up-right"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 18v-6a3 3 0 0 1 3 -3h10l-4 -4m0 8l4 -4" /></svg>
                <span>{{ __('Log in') }}</span>
            </button>
        </div>
        <div class="form-group group">
            @if (Route::has('password.request'))
                <a class="btn-gray-no-border" href="{{ route('password.request') }}">
                    {{ __('Forgot your password?') }}
                </a>
            @endif
            <div class="group-text">
                <p>{{ __('Dont have an account?') }} <a href="{{ route('register') }}" class="btn-gray-no-border">{{ __('Sign up') }}</a></p>
            </div>
        </div>
    </form>
@endsection
