<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DonateOrderResource\Pages;
use App\Filament\Resources\DonateOrderResource\RelationManagers;
use App\Models\DonateOrder;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DonateOrderResource extends Resource
{
    protected static ?string $model = DonateOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 1;
    public static function getNavigationLabel(): string
    {
        return __('Donate');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Donate');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $status = DonateOrder::where('status', '=', 1)->sum('count');
        $totalSumUSD = $status * config('app.pay_system.coin_price.USD');

        // $totalCount = DonateOrder::count();

        return $table
            ->query(DonateOrder::with('user'))
            ->columns([
                TextColumn::make('payment_id')
                    ->label('ID'),
                TextColumn::make('user.name')
                    ->label(__('User'))
                    ->sortable()
                    ->searchable(),
                TextColumn::make('count')->label('Кол-во ' . config('app.custom_config.donate_coin_name'))->sortable()->searchable(),
                TextColumn::make('status')->label(__('Status'))->sortable()->searchable()
                ->getStateUsing(fn ($record) => $record->status ? __('Paid') : __('Pending')),
                TextColumn::make('pay_system')->label(__('Payment method'))->sortable()->searchable(),
                TextColumn::make('count')
                    ->label(config('app.custom_config.donate_coin_name'))
                    ->sortable()->searchable(),
                TextColumn::make('created_at')->label('Дата')->sortable()->searchable(),
            ])
            ->headerActions([
                Action::make('summary_usd')
                    ->label(__('Total earned') . ' ' . number_format($totalSumUSD, 2) . ' USD')
                    ->color('secondary'),
            ])
            ->filters([
                Filter::make('user_name')
                    ->form([
                        Forms\Components\TextInput::make('user_name')
                            ->label('User Name')
                            ->placeholder('Search by user name')
                    ])
                    ->query(fn (Builder $query, array $data) => $query
                        ->whereHas('user', fn (Builder $query) => $query
                            ->where('name', 'like', "%{$data['user_name']}%")
                        )
                    ),
                // другие фильтры
            ]);
//            ->bulkActions([
//                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
//                ]),
//            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDonateOrders::route('/'),
            //'create' => Pages\CreateDonateOrder::route('/create'),
            //'view' => Pages\ViewDonateOrder::route('/{record}'),
            //'edit' => Pages\EditDonateOrder::route('/{record}/edit'),
        ];
    }
}
