@section('title', __('Change email'))
@extends('template::layouts.auth')

@section('content')
    <div class="form-group group-text">
        <p>
            {{ __('You can change your email address to a new one.') }}
        </p>
        <p>{{ __('Your current email:') }} {{ auth()->user()->email }}</p>
    </div>

    <form method="POST" action="{{ route('profile.change-email-reset') }}">
        @csrf

        <div class="form-group">
            <label for="email">{{ __('Enter your new email address.') }}</label>

            <div class="password-group">
                <input id="email" class="form-control"
                       type="text"
                       name="email"
                       required autocomplete="email"
                />
            </div>
            @error('email')
            <div class="text-danger">{{ $message }}</div>
            @enderror

        </div>

        <div class="form-group">
            <button class="btn-primary" type="submit"><span>{{ __('Save') }}</span></button>
            @if (session('status') === 'confirmation-sent')
                <div class="alert alert-success">
                    <p>{{ __('The confirmation link has been sent to your current email address.') }}</p>
                    <a href="{{ url()->current() }}" class="close"></a>
                </div>
            @endif

        </div>
    </form>
@endsection
