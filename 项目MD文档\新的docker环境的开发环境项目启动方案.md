# XXTT2 项目 Docker 开发环境启动方案

## 🎯 方案概述

本方案专门解决以下问题：
- ✅ Windows环境下PHP扩展兼容性问题 (ext-pcntl, ext-zip)
- ✅ 避免误操作生产数据库
- ✅ 游戏服务器连接问题的优雅处理
- ✅ 完整的本地开发环境搭建

## 📋 环境要求检查

### 必需软件
- [x] **Docker Desktop** - 已安装 (v28.0.4)
- [x] **Node.js** - 已安装 (v20.18.3)
- [x] **Git** - 已安装 (v2.39.2)

### 可选软件 (本方案中不需要)
- ~~PHP~~ - 将在Docker容器中运行
- ~~Composer~~ - 将在Docker容器中运行

---

## 🚀 完整启动流程

### 第一步：环境准备

```bash
# 1. 进入项目目录
cd C:\Users\<USER>\Desktop\work\flyXxtt2

# 2. 备份当前配置
copy .env .env.production.backup

# 3. 确认Docker Desktop已启动
docker --version
```

### 第二步：配置本地开发环境

创建适合本地开发的`.env`配置：

```env
APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# 本地Docker数据库配置
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

# 游戏服务器配置 (本地开发暂时禁用)
GAME_SERVER_ENABLED=false
L2SERVER_TYPE=local

# Redis配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Docker用户配置
WWWUSER=1000
WWWGROUP=1000

# 邮件配置 (开发环境使用Mailpit)
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="XXTT2 Local Dev"

# 其他配置
LOG_CHANNEL=stack
LOG_LEVEL=debug
BROADCAST_DRIVER=log
FILESYSTEM_DISK=local
SESSION_LIFETIME=120
```

### 第三步：启动Docker服务

```bash
# 启动所有Docker服务
docker-compose up -d

# 等待服务启动完成 (约30-60秒)
# 查看服务状态
docker-compose ps
```

**预期结果**：
```
NAME                IMAGE               STATUS
flyxxtt2-laravel.test-1   sail-8.3/app       Up
flyxxtt2-mysql-1          mysql/mysql-server:8.0   Up
```

### 第四步：在Docker容器中安装PHP依赖

```bash
# 在Linux容器中安装PHP依赖，避免Windows扩展问题
docker-compose exec laravel.test composer install

# 如果遇到内存问题，使用：
docker-compose exec laravel.test composer install --no-dev --optimize-autoloader
```

**预期结果**：
- 显示 "Generating optimized autoload files"
- 创建 `vendor` 目录
- 安装成功，无扩展错误

### 第五步：生成应用密钥

```bash
# 生成Laravel应用密钥
docker-compose exec laravel.test php artisan key:generate
```

**预期结果**：
- 显示 "Application key set successfully"
- `.env` 文件中的 `APP_KEY` 被自动填充

### 第六步：初始化数据库

```bash
# 运行数据库迁移 (创建表结构)
docker-compose exec laravel.test php artisan migrate

# 可选：生成测试数据
docker-compose exec laravel.test php artisan db:seed
```

**预期结果**：
- 显示各个迁移文件的执行状态
- 创建用户表、订单表等
- 显示 "Migration completed"

### 第七步：安装前端依赖

```bash
# 安装Node.js依赖 (在本地运行，不在容器中)
npm install

# 如果遇到问题，尝试：
npm install --legacy-peer-deps
```

### 第八步：启动前端开发服务器

```bash
# 启动Vite开发服务器 (保持运行)
npm run dev
```

**预期结果**：
- 显示 "VITE v5.x.x ready"
- 显示本地服务器地址 (通常是 http://localhost:5173)
- 保持运行状态

### 第九步：创建管理员账户

```bash
# 创建Filament管理员账户
docker-compose exec laravel.test php artisan make:filament-user

# 按提示输入：
# Name: Admin
# Email: <EMAIL>
# Password: 输入密码（至少8位）
```

### 第十步：验证项目启动

访问以下地址验证功能：

- **前台网站**: http://localhost
- **管理后台**: http://localhost/admin
- **邮件预览**: http://localhost:8025 (Mailpit)

---

## 🎛️ 可选服务启动

### 启动队列处理 (如需要)

```bash
# 在新的命令行窗口中启动
docker-compose exec laravel.test php artisan horizon

# 或者使用基础队列
docker-compose exec laravel.test php artisan queue:work
```

### 访问队列监控

- **Horizon监控**: http://localhost/horizon

---

## ✅ 启动成功检查清单

### Docker服务检查
- [ ] `docker-compose ps` 显示所有服务为 "Up" 状态
- [ ] MySQL容器正常运行
- [ ] Laravel应用容器正常运行

### 应用功能检查
- [ ] http://localhost 可以正常访问
- [ ] 页面样式正常显示 (CSS加载成功)
- [ ] http://localhost/admin 可以访问管理后台
- [ ] 可以使用创建的管理员账户登录

### 数据库检查
- [ ] 数据库迁移执行成功
- [ ] 可以注册新用户 (测试数据库写入)
- [ ] 管理后台可以查看用户列表

---

## 🔧 常见问题解决

### 问题1: Docker容器启动失败

**症状**: `docker-compose up -d` 报错

**解决方案**:
```bash
# 检查Docker Desktop是否运行
docker --version

# 清理Docker资源
docker-compose down
docker system prune -f

# 重新启动
docker-compose up -d
```

### 问题2: 端口被占用

**症状**: 启动时提示端口80被占用

**解决方案**:
在 `.env` 文件中修改端口：
```env
APP_PORT=8080
```
然后访问 http://localhost:8080

### 问题3: Composer安装失败

**症状**: `composer install` 在容器中仍然报错

**解决方案**:
```bash
# 使用忽略平台要求的方式安装
docker-compose exec laravel.test composer install --ignore-platform-req=ext-pcntl --ignore-platform-req=ext-zip

# 或者更新composer
docker-compose exec laravel.test composer self-update
docker-compose exec laravel.test composer install
```

### 问题4: 前端编译失败

**症状**: `npm run dev` 报错

**解决方案**:
```bash
# 删除node_modules重新安装
rmdir /s node_modules
npm cache clean --force
npm install

# 或者使用yarn
npm install -g yarn
yarn install
yarn dev
```

### 问题5: 数据库连接失败

**症状**: 网站显示数据库连接错误

**解决方案**:
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 重启MySQL容器
docker-compose restart mysql

# 等待30秒后重试
```

---

## 🎮 游戏服务器连接 (后续配置)

### 当前状态
- 游戏服务器连接已禁用 (`GAME_SERVER_ENABLED=false`)
- 不影响基础Web功能的开发和测试

### 后续启用步骤 (可选)

1. **配置网络访问**
   - 确保可以访问 `************:1433` (PTS服务器)
   - 配置防火墙或VPN

2. **修改.env配置**
   ```env
   GAME_SERVER_ENABLED=true
   L2SERVER_TYPE=pts  # 或 'java'
   ```

3. **测试连接**
   ```bash
   docker-compose exec laravel.test php artisan tinker
   # 在tinker中测试：
   # DB::connection('lin2world')->getPdo();
   ```

---

## 📚 开发环境说明

### 技术栈
- **后端**: Laravel 10 (PHP 8.3)
- **前端**: Vite + Alpine.js + TailwindCSS
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **容器**: Docker + Laravel Sail

### 开发工作流
1. **后端开发**: 在Docker容器中运行PHP代码
2. **前端开发**: 本地运行Vite热重载
3. **数据库**: Docker容器中的MySQL
4. **调试**: 使用Laravel Telescope或日志

### 类比Java开发
- **Laravel Sail** ≈ **Docker Compose for Spring Boot**
- **Artisan命令** ≈ **Maven/Gradle任务**
- **Eloquent ORM** ≈ **JPA/Hibernate**
- **Blade模板** ≈ **Thymeleaf**

---

## 🎯 下一步建议

### 立即执行
1. **按照本方案启动项目**
2. **验证基础功能正常**
3. **熟悉Laravel项目结构**

### 后续学习
1. **了解Laravel路由和控制器**
2. **学习Filament管理面板**
3. **研究游戏数据集成方式**

---

## 📝 重要提醒

### 安全注意事项
- ✅ 本方案使用本地数据库，不会影响生产环境
- ✅ 游戏服务器连接已禁用，避免意外操作
- ✅ 所有敏感配置都在本地环境中

### 开发建议
- 📖 先熟悉Laravel基础概念
- 🔧 逐步启用高级功能
- 🧪 多使用测试数据进行开发

---

*Docker开发环境启动方案 - 创建时间: 2025-07-30*
