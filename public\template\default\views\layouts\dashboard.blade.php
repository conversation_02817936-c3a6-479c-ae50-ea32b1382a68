<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>@yield('title', 'Личный кабинет') | {{ config('app.name') }}</title>

    <link rel="icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>
    <link rel="shortcut icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>

    <link rel="stylesheet" href="{{ base_url('css/lin2web.css') }}"/>
    <script src="{{ base_url('js/app.js') }}"></script>

    <!-- шрифты и иконки -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600;700&subset=cyrillic&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
</head>

<body>

<div class="main-bg"></div>

<div class="main-wrapper">
    <div class="gw-burger navigation__burger">
        <div class="gw-burger__line gw-burger__line_pos_top"></div>
        <div class="gw-burger__line gw-burger__line_pos_middle"></div>
        <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
    </div>
    <aside class="sidebar top_menu">
        @include('template::components.sidebar')
    </aside>

    <main class="main-content">
        <div class="main-wrap">
            <div class="auth_top">
                <nav class="auth_nav_top">
                    <ul>
                        <li>
                            <a href="{{ route('main-page') }}">
                                <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-home"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                                <span>{{ __('Home') }}</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('about') }}">
                                <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-brightness-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" /><path d="M12 5l0 .01" /><path d="M17 7l0 .01" /><path d="M19 12l0 .01" /><path d="M17 17l0 .01" /><path d="M12 19l0 .01" /><path d="M7 17l0 .01" /><path d="M5 12l0 .01" /><path d="M7 7l0 .01" /></svg>
                                <span>{{ __('About') }}</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ config('app.custom_config.telegram_support') }}" target="_blank">
                                <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-message"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
                                <span>{{ __('Community') }}</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('tickets.create') }}">
                                <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-help-square-rounded"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z" /><path d="M12 16v.01" /><path d="M12 13a2 2 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483" /></svg>
                                <span>{{ __('Support') }}</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <div class="auth_top_r">
                    <a href="{{ route('download') }}" class="btn-accent">
                        <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-cloud-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 18.004h-5.343c-2.572 -.004 -4.657 -2.011 -4.657 -4.487c0 -2.475 2.085 -4.482 4.657 -4.482c.393 -1.762 1.794 -3.2 3.675 -3.773c1.88 -.572 3.956 -.193 5.444 1c1.488 1.19 2.162 3.007 1.77 4.769h.99c1.38 0 2.573 .813 3.13 1.99" /><path d="M19 16v6" /><path d="M22 19l-3 3l-3 -3" /></svg>
                        <span>{{ __('Download files') }}</span>
                    </a>
                    @include('template::lang')
                </div>
            </div>

            <div class="auth-content">
                @if(Auth::check())
                    <div class="auth-main-link">
                        <div class="auth-main-top-item left">
                            <div class="auth_donate_coins">
                                <a href="{{ url()->current() }}" class="refresh_link">
                                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-rotate-clockwise-2"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 4.55a8 8 0 0 1 6 14.9m0 -4.45v5h5" /><path d="M5.63 7.16l0 .01" /><path d="M4.06 11l0 .01" /><path d="M4.63 15.1l0 .01" /><path d="M7.16 18.37l0 .01" /><path d="M11 19.94l0 .01" /></svg>
                                </a>
                                <div class="auth_donation_coins">
                                    <p class="tooltip_title">
                                        {{ __('Balance') }}
                                        <span class="info-tooltip">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icon-tabler-info-circle">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M12 12v4" />
                <path d="M12 8h.01" />
                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" />
            </svg>
            <span class="tooltip-text">{{ __('The balance of donate coins is common to all servers.') }}</span>
        </span>
                                    </p>
                                    <div class="auth_donate_title">
                                        <span>{{ auth()->user()->balance }}</span>
                                        <img src="{{ base_url('img/lin2web/coin.png') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="auth_donate_links">
                                <a href="{{ route('pay') }}" class="btn-gray-no-border">{{ __('Buy coins') }}</a>
                                <a href="{{ route('warehouse') }}" class="btn-gray-no-border">{{ __('Send to game') }}</a>
                            </div>
                        </div>
                        <div class="auth-main-top-item right">
                            <div class="auth_donate_coins">
                                <a href="{{ route('referral.index') }}" class="refresh_link">
                                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-users"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" /><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" /><path d="M16 3.13a4 4 0 0 1 0 7.75" /><path d="M21 21v-2a4 4 0 0 0 -3 -3.85" /></svg>
                                </a>
                                <div class="auth_donation_coins">
                                    <p class="tooltip_title">
                                        {{ __('My referrals') }}
                                        <span class="info-tooltip">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icon-tabler-info-circle">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M12 12v4" />
                <path d="M12 8h.01" />
                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" />
            </svg>
<span class="tooltip-text">
    {{ __('referral_summary', [
        'count' => $referralsCount ?? 0,
        'amount' => number_format($totalReferralBonus ?? 0, 0, ',', ' ')
    ]) }}
</span>

        </span>
                                    </p>
                                    <div class="auth_donate_title">
                                        <span>{{ $referralsCount ?? 0 }} / {{ number_format($totalReferralBonus ?? 0, 0, ',', ' ') }}</span>
                                        <img src="{{ base_url('img/lin2web/coin.png') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="auth_donate_links">
                                <a href="{{ route('referral.index') }}" class="btn-gray-no-border">{{ __('Referrals') }}</a>
                            </div>

                        </div>
                    </div>
                @endif
                <div class="auth-content-main">
                    @yield('content')
                </div>
            </div>
        </div>
    </main>
</div>

</body>
</html>
