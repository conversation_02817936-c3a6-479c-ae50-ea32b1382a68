@section('title', __('Personal account'))
@extends('template::style.dashboard')

@section('content')
    <header>
        <p>{{ __('Attention! The character must be offline when purchasing services.') }}</p>
    </header>

    <section id="services">
        <div class="services-all">
            @if(config('app.services.change_name_enabled'))
                <div class="service-item">
                    <div class="service-top-block">
                        <h2>{{ __('Change char name') }}</h2>
                        <p>{{ __('Price of Service')  }}: <span>{{ config('app.services.change_name_price') }} {{ config('app.custom_config.donate_coin_name') }}</span></p>
                        <img src="{{ base_url('img/lin2web/service-1.png') }}">
                    </div>
                    <form action="{{ route('service.change-name') }}" id="change-char-name" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="old_name">{{ __('Select char name') }}</label>
                            <select name="old_name" id="old_name" class="form-control">
                                <option selected>{{ __('Select char name') }}</option>
                                @foreach($game_accounts as $ga)
                                    @foreach($ga->player_list as $char_name)
                                        <option value="{{ $char_name }}">{{ $char_name }}</option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>
                        <div class="replace-icon">
                            <img src="{{ base_url('img/lin2web/replace-icon.png') }}">
                        </div>
                        <div class="form-group">
                            <label for="new_name">{{ __('Enter new name') }}</label>
                            <input name="new_name" type="text" id="new_name" class="form-control" placeholder="{{ __('New name') }}">
                        </div>
                        <div class="form-group">
                            <button class="lineage-link" type="submit"><span>{{ __('Change char name') }}</span></button>
                        </div>
                        @if ($errors->changeNameForm->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->changeNameForm->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                    </form>
                </div>
            @endif

            @if(config('app.services.change_color_name_enabled'))
                <div class="service-item">
                    <div class="service-top-block">
                        <h2>{{ __('Change char name Color') }}</h2>
                        <p>{{ __('Price of Service') }}: <span>{{ config('app.services.change_color_name_price') }} {{ config('app.custom_config.donate_coin_name') }}</span></p>
                        <img src="{{ base_url('img/lin2web/service-2.png') }}">
                    </div>
                    <form action="" class="change-char-color" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="old_name">{{ __('Select char name') }}</label>
                            <select name="old_name" class="old-name form-control">
                                <option selected>{{ __('Select char name') }}</option>
                                @foreach($game_accounts as $ga)
                                    @foreach($ga->player_list as $char_name)
                                        <option value="{{ $char_name }}">{{ $char_name }}</option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>
                        <div class="replace-icon">
                            <img src="{{ base_url('img/lin2web/replace-icon.png') }}">
                        </div>
                        <div class="form-group">
                            <label for="color">{{ __('Select color') }}</label>
                            <div class="color-dropdown">
                                <div class="selected-color"></div>
                                <div class="color-options">
                                    <div class="color-option" data-value="********" style="background-color: #FF0000;"></div>
                                    <div class="color-option" data-value="65280" style="background-color: #00FF00;"></div>
                                    <div class="color-option" data-value="255" style="background-color: #0000FF;"></div>
                                    <div class="color-option" data-value="16776960" style="background-color: #FFFF00;"></div>
                                    <div class="color-option" data-value="65535" style="background-color: #00FFFF;"></div>
                                    <div class="color-option" data-value="16777215" style="background-color: #FFFFFF; border: 1px solid #ccc;"></div>
                                    <div class="color-option" data-value="0" style="background-color: #000000;"></div>
                                </div>
                            </div>
                            <input type="hidden" name="color" class="selected-color-value">
                        </div>
                        <div class="form-group">
                            <button class="lineage-link"><span>{{ __('Change color') }}</span></button>
                        </div>
                    </form>
                </div>
            @endif

            @if(config('app.services.change_gender_enabled'))
                <div class="service-item">
                    <div class="service-top-block">
                        <h2>{{ __('Change Character Gender') }}</h2>
                        <p>{{ __('Price of Service') }}: <span>{{ config('app.services.change_gender') }} {{ config('app.custom_config.donate_coin_name') }}</span></p>
                        <img src="{{ base_url('img/lin2web/service-3.png') }}">
                    </div>
                    <form action="{{ route('service.change-gender') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="char_name">{{ __('Select char name') }}</label>
                            <select name="char_name" id="char_name" class="form-control">
                                <option selected>{{ __('Select char name') }}</option>
                                @foreach($game_accounts as $ga)
                                    @foreach($ga->player_list as $char_name)
                                        <option value="{{ $char_name }}">{{ $char_name }}</option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="lineage-link" type="submit"><span>{{ __('Change Gender') }}</span></button>
                        </div>
                        @if ($errors->changeGenderForm->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->changeGenderForm->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        @if (session('success_change_gender'))
                            <div class="alert alert-success">
                                {{ session('success_change_gender') }}
                            </div>
                        @endif
                    </form>
                </div>
            @endif
        </div>
    </section>


    <style>
        .color-dropdown {
            position: relative;
            width: 100%;
            cursor: pointer;
        }
        .selected-color {
            color: #ffffff;
            background: #2e2726;
            border: 1px solid rgb(255 255 255 / 10%);
            padding: 14px 20px;
            border-radius: 5px;
            width: 100%;
            height: 43px;
        }
        .color-options {
            display: none;
            position: absolute;
            top: 45px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: white;
            z-index: 10;
            overflow: hidden;
        }
        .color-option {
            width: 100%;
            height: 40px;
            cursor: pointer;
        }
        .color-option:hover {
            border: 2px solid #000;
        }
    </style>

    <script>
        document.querySelectorAll(".change-char-color").forEach((form) => {
            const charNameDropdown = form.querySelector(".old-name");
            const selectedColor = form.querySelector(".selected-color");
            const colorOptions = form.querySelector(".color-options");
            const colorInput = form.querySelector(".selected-color-value");
            const colorDropdown = form.querySelector(".color-dropdown");

            // Установка ника в selected-color при выборе из списка
            charNameDropdown.addEventListener("change", () => {
                selectedColor.textContent = charNameDropdown.value.trim() || "Select a nickname";
            });

            // Открытие/закрытие color-options
            colorDropdown.addEventListener("click", (event) => {
                event.stopPropagation(); // Предотвращаем закрытие при клике на dropdown
                const isOpen = colorOptions.style.display === "block";
                document.querySelectorAll(".color-options").forEach(option => option.style.display = "none");
                colorOptions.style.display = isOpen ? "none" : "block";
            });

            // Выбор цвета и закрытие color-options
            colorOptions.addEventListener("click", (event) => {
                const option = event.target.closest(".color-option");
                if (option) {
                    event.stopPropagation(); // Останавливаем всплытие, чтобы не вызвать повторное открытие
                    selectedColor.style.color = option.style.backgroundColor;
                    colorInput.value = option.getAttribute("data-value");
                    colorOptions.style.display = "none"; // Закрываем список
                }
            });


            // Закрытие всех списков при клике вне блока
            document.addEventListener("click", (event) => {
                if (!form.contains(event.target)) {
                    colorOptions.style.display = "none";
                }
            });
        });

    </script>
@endsection
