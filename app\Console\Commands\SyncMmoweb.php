<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncMmoweb extends Command
{
    protected $signature = 'sync:mmoweb';
    protected $description = 'Синхронизировать ma_id из users в accounts по email';
    public function handle()
    {
        $this->info("🔄 Начинаем синхронизацию ma_id...");

        $users = DB::connection('mysql')->table('users')->select('id', 'email')->get();
        $updated = 0;

        foreach ($users as $user) {
            $affected = DB::connection('loginserver')
                ->table('accounts')
                ->where('email', $user->email)
                ->where('ma_id', 0) // Обновляем только если ma_id == 0
                ->update(['ma_id' => $user->id]);

            if ($affected > 0) {
                $message = "✅ ma_id обновлён для email: {$user->email} → ma_id: {$user->id}";
                $this->line($message);
                Log::info($message);
                $updated += $affected;
            }
        }

        $summary = "✅ Синхронизация завершена. Обновлено ma_id: {$updated}";
        $this->info($summary);
        Log::info($summary);

        return 0;
    }
}
