<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'loginserver' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host'      => config('app.loginserver.host'),
            'port'      => config('app.loginserver.port'),
            'database'  => config('app.loginserver.database'),
            'username'  => config('app.loginserver.username'),
            'password'  => config('app.loginserver.password'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'server1' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host'      => config('app.java_servers.server1.host'),
            'port'      => config('app.java_servers.server1.port'),
            'database'  => config('app.java_servers.server1.database'),
            'username'  => config('app.java_servers.server1.username'),
            'password'  => config('app.java_servers.server1.password'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'server2' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host'      => config('app.java_servers.server2.host'),
            'port'      => config('app.java_servers.server2.port'),
            'database'  => config('app.java_servers.server2.database'),
            'username'  => config('app.java_servers.server2.username'),
            'password'  => config('app.java_servers.server2.password'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'src_server' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host'      => config('app.merge_servers.src_server.host'),
            'port'      => config('app.merge_servers.src_server.port'),
            'database'  => config('app.merge_servers.src_server.database'),
            'username'  => config('app.merge_servers.src_server.username'),
            'password'  => config('app.merge_servers.src_server.password'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
                PDO::ATTR_TIMEOUT => 600,
            ]) : [],
        ],

        'dist_server' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host'      => config('app.merge_servers.dist_server.host'),
            'port'      => config('app.merge_servers.dist_server.port'),
            'database'  => config('app.merge_servers.dist_server.database'),
            'username'  => config('app.merge_servers.dist_server.username'),
            'password'  => config('app.merge_servers.dist_server.password'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
                PDO::ATTR_TIMEOUT => 600,
            ]) : [],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'lin2world' => [
            'driver' => 'sqlsrv',
            'host' => config('app.pts_servers.lin2world.host'),
            'port' => config('app.pts_servers.lin2world.port'),
            'database' => config('app.pts_servers.lin2world.database'),
            'username' => config('app.pts_servers.lin2world.username'),
            'password' => config('app.pts_servers.lin2world.password'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'encrypt' => 'no', // Отключение шифрования
            'trust_server_certificate' => true, // Доверие к сертификату сервера
        ],
        'lin2world_2' => [
            'driver' => 'sqlsrv',
            'host' => config('app.pts_servers.lin2world_2.host'),
            'port' => config('app.pts_servers.lin2world_2.port'),
            'database' => config('app.pts_servers.lin2world_2.database'),
            'username' => config('app.pts_servers.lin2world_2.username'),
            'password' => config('app.pts_servers.lin2world_2.password'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'encrypt' => 'no', // Отключение шифрования
            'trust_server_certificate' => true, // Доверие к сертификату сервера
        ],
        'lin2db' => [
            'driver' => 'sqlsrv',
            'host' => config('app.lin2db.host'),
            'port' => config('app.lin2db.port'),
            'database' => config('app.lin2db.database'),
            'username' => config('app.lin2db.username'),
            'password' => config('app.lin2db.password'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'encrypt' => 'no', // Отключение шифрования
            'trust_server_certificate' => true, // Доверие к сертификату сервера
        ],

        'lin2billing' => [
            'driver' => 'sqlsrv',
            'host' => config('app.lin2billing.host'),
            'port' => config('app.lin2billing.port'),
            'database' => config('app.lin2billing.database'),
            'username' => config('app.lin2billing.username'),
            'password' => config('app.lin2billing.password'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'encrypt' => 'no', // Отключение шифрования
            'trust_server_certificate' => true, // Доверие к сертификату сервера
        ],

//        'sqlsrv' => [
//            'driver' => 'sqlsrv',
//            'url' => env('DATABASE_URL'),
//            'host' => '***********',
//            'port' => '1433',
//            'database' => 'lin2world',
//            'username' => 'daniel',
//            'password' => 'PrecisionT5500@',
//            'charset' => 'utf8',
//            'prefix' => '',
//            'prefix_indexes' => true,
//            'encrypt' => 'no',
//            'trust_server_certificate' => true, // Доверять сертификатам сервера
//        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
