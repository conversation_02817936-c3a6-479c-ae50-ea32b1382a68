@extends('template::layouts.dashboard')

@section('content')
    <div class="card">
        <div class="title">
            <h1>{{ __('Create a new ticket') }}</h1>
        </div>
        <form action="{{ route('tickets.store') }}" class="auth-form" method="POST" enctype="multipart/form-data">
            @csrf
            <div class="form-group">
                <input type="text" id="title" name="title" class="form-control" placeholder="{{ __('Title') }}" required>
            </div>
            <div class="form-group">
                <label for="priority">{{ __('Priority') }}</label>
                <select id="priority" name="priority" class="form-control" required>
                    <option value="low">{{ __('Low') }}</option>
                    <option value="medium">{{ __('Medium') }}</option>
                    <option value="high">{{ __('High') }}</option>
                    <option value="critical">{{ __('Critical') }}</option>
                </select>
            </div>
            <div class="form-group">
                <textarea id="message" name="message" class="form-control" rows="5" placeholder="{{ __('Write a message...') }}" required></textarea>
            </div>

            <div class="form-group">
                <label for="attachment">{{ __('Attach file (formats: jpg, png, webp, no larger than 2MB)') }}</label>
                <input type="file" id="attachment" name="attachment" accept=".jpg,.jpeg,.png,.webp" onchange="validateFile()" class="form-control">
            </div>

            <div class="form-group login_button">
                <button type="submit" class="btn-primary">{{ __('Create') }}</button>
            </div>

            <div class="form-group">
                @if ($errors->has('message'))
                    <div class="alert-danger">
                        {{ $errors->first('message') }}
                    </div>
                @endif
            </div>
        </form>

    </div>



    <script>
        function validateFile() {
            const file = document.getElementById('attachment').files[0];
            if (file && file.size > 2 * 1024 * 1024) {
                alert("Размер файла не должен превышать 2MB.");
                document.getElementById('attachment').value = ""; // Сбросить файл
            }
        }
    </script>

@endsection
