<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NewsResource\Pages;
use App\Models\News;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class NewsResource extends Resource
{
    protected static ?string $model = News::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';
    protected static ?int $navigationSort = 3;

    public static function getNavigationLabel(): string
    {
        return __('News');
    }

    public static function getPluralModelLabel(): string
    {
        return __('News');
    }



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->label(__('Title'))
                    ->reactive(),
                Forms\Components\FileUpload::make('image')
                    ->label(__('Image'))
                    ->image()
                    ->disk('public')
                    ->directory('images')
                    ->required(false)
                    ->maxSize(5 * 1024) // Максимальный размер файла в байтах (5 MB)
                    ->enableDownload() // Позволяет загружать изображение
                    ->enableOpen() // Открывает изображение в новой вкладке
                    ->deletable(),
                Forms\Components\RichEditor::make('short_desc')
                    ->label(__('Shortly description'))
                    ->required(), // Сделайте поле обязательным, если необходимо
                Forms\Components\RichEditor::make('description')
                    ->label(__('Description'))
                    ->required(), // Сделайте поле обязательным, если необходимо
                Forms\Components\TextInput::make('forum_link')
                    ->label(__('Link to forum')),
                Forms\Components\DateTimePicker::make('published_at')
                    ->label(__('Date of publication')),

                Forms\Components\Select::make('status')
                    ->options([
                        'draft' => 'Черновик',
                        'published' => 'Опубликовано',
                        'hidden' => 'Скрыто',
                    ])
                    ->default('draft')
                    ->label(__('Status')),
                Forms\Components\Select::make('show_sector')
                    ->options([
                        'default' => 'По умолчанию',
                        'main' => 'Главная',
                        'other' => 'Другое',
                    ])
                    ->default('default')
                    ->label('Показ новости'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')->label(__('Title')),
                TextColumn::make('published_at')->label(__('Date of publication')),
            ])
            ->headerActions([
                Action::make('total_summary')
                    ->label( __('Total News').' ' . News::count())
                    ->color('secondary'),
            ])
            ->filters([
                // Добавьте фильтры при необходимости
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNews::route('/'),
            'create' => Pages\CreateNews::route('/create'),
            'edit' => Pages\EditNews::route('/{record}/edit'),
        ];
    }
}
