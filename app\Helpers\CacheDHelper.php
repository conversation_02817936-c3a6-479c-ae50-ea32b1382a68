<?php

namespace App\Helpers;

class CacheDHelper
{
    private $cachedIp;
    private $cachedPort;
    private $webAdmin;
    private $socketErrors;

    public function __construct()
    {
        $this->cachedIp = '*************';
        $this->cachedPort = 2006;
        $this->webAdmin = 'WebAdmin';
        $this->socketErrors = [
            "1" => true, "01" => false, "02" => false, "03" => false, "04" => false,
            "05" => false, "06" => false, "07" => false, "08" => false, "09" => false,
            "010" => false, "011" => false, "012" => false, "013" => false, "014" => false,
            "015" => false, "016" => false, "017" => false, "018" => false, "019" => false,
            "020" => false, "021" => false, "022" => false, "023" => false, "024" => false,
            "025" => false, "026" => false
        ];
    }

    private function toUnicode(string $string): string
    {
        return mb_convert_encoding($string, "UCS-2LE", "UTF-8") . pack("v", 0);
    }

    private function cacheDInteractive(string $buf)
    {
        $fp = @fsockopen($this->cachedIp, $this->cachedPort, $errno, $errstr, 5);
        if (!$fp) {
            throw new \Exception("Connection failed: $errstr ($errno)");
        }

        $packet = pack("s", strlen($buf) + 2) . $buf;
        fwrite($fp, $packet);
        $len = unpack("v", fread($fp, 2));
        fread($fp, 1); // Skip rid

        $result = "";
        for ($i = 0; $i < (($len[1] - 4) / 4); $i++) {
            $read = unpack("i", fread($fp, 4));
            $result .= $read[1];
        }

        fclose($fp);

        if (!isset($this->socketErrors[$result])) {
            throw new \Exception("Unexpected response: $result");
        }

        return $this->socketErrors[$result];
    }

    public function setCharacterLocation(int $charId, int $xLoc, int $yLoc, int $zLoc): bool
    {
        $buf = pack("c", 2)
            . pack("V", $charId)
            . pack("V", 1)
            . pack("V", $xLoc)
            . pack("V", $yLoc)
            . pack("V", $zLoc)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }


    public function kickCharacter(int $charId): bool
    {
        $buf = pack("cV", 5, $charId) . $this->toUnicode($this->webAdmin);
        return $this->cacheDInteractive($buf);
    }

    public function changeCharacterName(int $charId, string $newCharName): bool
    {
        $buf = pack("cV", 4, $charId)
            . $this->toUnicode($newCharName)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }

    public function addItem(int $charId, int $warehouse, int $itemType, int $itemAmount, int $enchant = 0, int $eroded = 0, int $bless = 0, int $wished = 0): bool
    {
        $qHigh = intval($itemAmount / 0x100000000);
        $qLow = $itemAmount % 0x100000000;

        $buf = pack("c", 55)
            . pack("V", $charId)
            . pack("V", $warehouse)
            . pack("V", $itemType)
            . pack("V", $qLow)
            . pack("V", $qHigh)
            . pack("V", $enchant)
            . pack("V", $eroded)
            . pack("V", $bless)
            . pack("V", $wished)
            . str_repeat(pack("V", 0), 10)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }

    public function deleteItem(int $charId, int $warehouse, int $itemUid, int $amount): bool
    {
        $buf = pack("cVVVVV", 13, $charId, $warehouse, $itemUid, $amount, 0)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }

    public function modifyCharacter(int $charId, int $sp, int $exp, int $karma, int $pk, int $pvp): bool
    {
        $expHigh = intval($exp / 0x100000000);
        $expLow = $exp % 0x100000000;

        $buf = pack("c", 15)
            . pack("V", $charId)
            . pack("V", $sp)
            . pack("V", $expLow)
            . pack("V", $expHigh)
            . pack("V", $karma)
            . pack("V", $pk)
            . pack("V", $pvp)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }

    public function setNameColor(int $charId, int $color, int $changeType): bool
    {
        $buf = pack("cVVV", 122, $charId, $color, $changeType)
            . pack("V", 0)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }

    public function addBan(string $hwid, string $account, string $action, int $time, int $delay, string $comment): bool
    {
        $buf = pack("cV", 0x99, 3)
            . $this->toUnicode($hwid)
            . $this->toUnicode($account)
            . $this->toUnicode($action)
            . pack("VV", $time, $delay)
            . $this->toUnicode($comment);

        return $this->cacheDInteractive($buf);
    }

    public function removeBan(string $hwid, string $account): bool
    {
        $buf = pack("cV", 0x99, 4)
            . $this->toUnicode($hwid)
            . $this->toUnicode($account);

        return $this->cacheDInteractive($buf);
    }

    public function ModChar2Packet(int $charId, int $gender, int $race, int $class, int $face, int $hairShape, int $hairColor): bool
    {
        $buf = pack("c", 16)
            . pack("V", $charId)
            . pack("V", $gender)
            . pack("V", $race)
            . pack("V", $class)
            . pack("V", $face)
            . pack("V", $hairShape)
            . pack("V", $hairColor)
            . $this->toUnicode($this->webAdmin);

        return $this->cacheDInteractive($buf);
    }
}
