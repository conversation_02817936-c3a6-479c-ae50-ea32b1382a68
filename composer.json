{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "carbonphp/carbon-doctrine-types": "^2.1.0", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/horizon": "^5.24", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "midtrans/midtrans-php": "^2.6", "nesbot/carbon": "^2.72", "srmklive/paypal": "^3.0", "stripe/stripe-php": "^16.3"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel-lang/common": "^6.1", "laravel/breeze": "^1.28", "laravel/pint": "^1.0", "laravel/sail": "*", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helper.php", "app/Helpers/CacheDHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}