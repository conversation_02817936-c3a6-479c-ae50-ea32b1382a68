@font-face {
    font-family: 'Montserrat-Regular';
    src: url("../fonts/Montserrat-Regular.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}
@font-face {
    font-family: 'Montserrat-SemiBold';
    src: url("../fonts/Montserrat-SemiBold.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}


/* ===== 🎨 Глобальные переменные ===== */
:root {
    --color-primary: #fcc55b;
    --color-secondary: #e1ce87;
    --color-background: #f5f7fa;
    --color-dark: #1e1e1e;
    --color-light: #ffffff;
    --color-accent: #ff6b6b;
    --accent-bg-color: rgb(149 111 0 / 90%);
    --color-gray: gray;
    --bgc-color: rgb(48 44 76 / 90%);
    --bg-form-head: rgb(209 163 0 / 40%);
    --bg-form-table: rgb(48 46 37 / 90%);
    --bg-color: #20232A;

    --font-regular: Montserrat-Regular;
    --font-bold: Montserrat-SemiBold;


    /* размеры */
    --sidebar-width: 250px;
    --container-padding: 30px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
* {
    outline: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html, body {
    height: 100%;
}
body {
    font-family: var(--font-regular), sans-serif;
}

ul, li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
p {
    margin: 0;
}
a {
    color: #fff;
    text-decoration: none;
}
small {
    color: #bebebe;
}

/*FORM START*/
:focus-visible {
    outline: -webkit-focus-ring-color auto 0;
}
.form-control {
    font-family: var(--font-regular), sans-serif;
    box-sizing: border-box;
    background: #292D34;
    border: 1px solid rgb(255 255 255 / 10%);
    padding: 14px 20px;
    border-radius: 12px;
    width: 100%;
    color: #fff;
    font-size: 16px;
}

.form-control::placeholder {
    font-family: var(--font-regular), sans-serif;
    font-size: 14px;
}
.form-group {
    margin-bottom: 30px;
}
.form-group:last-child {
    margin-bottom: 0;
}
.form-group.group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

::-webkit-input-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
::-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-ms-input-placeholder {
    color: #7b7873;
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:focus::-webkit-input-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus::-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-ms-input-placeholder {
    opacity: 0;
    -ms-transform: translateX(20px);
    transform: translateX(20px);
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/*FORM END*/








.main-bg {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: -10;
    background-image: url("../img/lin2web/bg.jpg");
    background-size: cover;
    background-position: 50%;
    background-repeat: no-repeat;
}
aside.sidebar {
    position: fixed;
    z-index: 20;
    width: 270px;
    height: 100vh;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    background-color: rgba(22, 15, 2, .5);
    border-right: .25rem solid #bbac8b;
}
.main-wrap {
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;
    height: 100%;
    background-size: cover;
    background-position: center;
}
.auth_logo img {
    width: 100%;
    max-width: 160px;
}
.auth_block_top {
    padding-left: 30px;
    padding-right: 20px;
    padding-top: 30px;
}
.auth_header_top {
    margin-top: 30px;
    margin-bottom: 30px;
}

form.logout button:hover:before {
    opacity: 1;
    transform: scaleY(1);
}



.auth_header_title {
    display: flex;
    align-items: center;
    gap: 5px;
}
.auth_header_title svg {
    width: 28px;
    color: #fff;
}
.auth_header_title p {
    font-size: 18px;
    color: var(--color-primary);
}
.auth_nav_top {
    padding: 10px 20px;
}
.auth_nav_top ul {
    display: flex;
    gap: 40px;
}
.auth_top_r {
    display: flex;
    align-items: center;
    gap: 30px;
}
.auth_nav_top a {
    display: flex;
    align-items: center;
    gap: 7px;
    font-size: 18px;
}
.auth_nav_top a:hover {
    color: var(--color-primary);
}
.auth_nav_top svg {
    width: 28px;
}


.btn-accent {
    font-family: var(--font-bold), sans-serif;
    border: 0;
    cursor: pointer;
    background-color: #6400ff;
    height: 42px;
    min-width: 180px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
}
.btn-accent:hover {
    box-shadow: 0 0 8px #6400ff;
}
.btn-accent svg {
    width: 24px;
}
.btn-green {
    border: 0;
    cursor: pointer;
    background-color: #00d084;
    height: 42px;
    min-width: 180px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
}
.btn-green:hover {
    box-shadow: 0 0 8px #00d084;
}
.btn-green svg {
    width: 24px;
}
.btn-primary {
    font-family: var(--font-bold), sans-serif;
    border: 0;
    cursor: pointer;
    background-color: #6c5eff;
    height: 42px;
    min-width: 180px;
    max-width: max-content;
    padding: 0 15px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
    color: #fff;
    font-size: 14px;
}
.btn-primary:hover {
    box-shadow: 0 0 8px #6c5eff;
}
.btn-primary svg {
    width: 24px;
}

/*LANGUAGE*/
.nav__langs {
    position: relative;
    z-index: 1;
}
.nav__langs-item {
    cursor: pointer;
    height: 42px;
    background-color: var(--bgc-color);
    padding: 0 5px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    transition: color 0.3s ease-in-out;
    gap: 10px;
}

.nav__langs-item:hover, .nav__langs-item.active {
    color: #fff;
}
.nav__langs-item img {
    width: 22px;
    aspect-ratio: 34/24;
    border-radius: 2px;
}
.nav__langs-item--current:after {
    content: "";
    width: 12px;
    aspect-ratio: 12 / 6;
    background: url("../img/lin2web/arrow.png");
    background-size: 100% 100%;
    transition: transform 0.3s ease-in-out;
}

.nav__langs-item.active:after {
    transform: scale(1, -1);
}
.nav__langs-stroke {
    list-style-type: none;
    position: absolute;
    z-index: 1;
    top: calc(100% + 3px);
    right: 0;
    display: none;
}
.nav__langs-stroke li:not(:first-child) {
    margin-top: 10px;
}
.nav__links {
    list-style-type: none;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
    column-gap: 30px;
    margin-left: auto;
    position: relative;
    z-index: 2;
}
.nav__links-item {
    font-family: var(--primary-font), sans-serif;
    font-weight: 400;
    font-size: 15px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    column-gap: 6px;
    transition: color 0.3s ease-in-out;
}
.nav__links-item:hover {
    color: var(--brand-color);
}
.nav__links-item img {
    height: 16px;
}
.nav__switch {
    width: 34px;
    height: 34px;
    position: relative;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.15);
    display: none;
    transition: background-color 0.3s ease-in-out, border 0.3s ease-in-out;
    z-index: 3;
    border-radius: 50%;
    margin-left: auto;
}
.nav__switch:hover {
    background-color: var(--brand-color);
}
.nav__switch:hover:after, .nav__switch:hover:before, .nav__switch:hover .nav__switch-item {
    background: #1c2227;
}
.nav__switch:hover .nav__switch-item {
    transform: scale(0.7, 1);
}
.nav__switch.active:after {
    transform: rotate(-135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active:before {
    transform: rotate(135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active .nav__switch-item {
    transform: scale(0, 1);
}
.nav__switch:after {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: 11px;
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch:before {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: calc(100% - 13px);
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch-item {
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    top: calc((100% - 2px) / 2);
    left: 6px;
    transition: all 0.3s ease-in-out;
    transform: scale(0.7, 1);
}
/*LANG END*/


/* Ошибки */
.text-danger {
    color: #ff4d4d;
    font-size: 13px;
    margin-top: 5px;
}

/* Успешно */
.text-success {
    color: #00cc66;
    font-size: 14px;
    margin-top: 10px;
}

/* Показать/Скрыть пароль */
.password-group {
    display: flex;
    align-items: center;
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    cursor: pointer;
    font-size: 16px;
    user-select: none;
    color: #aaa;
}

.password-toggle:hover {
    color: #fff;
}

main {
    height: 100%;
}
.auth_wrapper {
    margin-left: 60px;
    margin-right: 60px;
    height: 100%;
}
.auth_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    position: fixed;
    left: 0;
    right: 0;
    background-color: #21232a;
    padding-left: 60px;
    padding-right: 60px;
}
.auth-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
.content {
    background-color: var(--bg-color);
    min-width: 600px;
    max-width: 700px;
    padding: 30px;
    border-radius: 12px;
}
.title {
    text-align: center;
    font-size: 32px;
    color: #fff;
    margin-bottom: 30px;
}
.btn-gray-no-border {
    cursor: pointer;
    background: transparent;
    border: 1px solid #fff;
    padding: 6px 15px;
    border-radius: 8px;
    font-size: 14px;
    color: #fff;
}
.btn-gray-no-border:hover {
    border: 1px solid #fff;
}
.login_button button {
    width: 100%;
    max-width: 100%;
    height: 50px;
    font-size: 18px;
    position: relative;
}
.login_button a {
    min-width: 100%;
    width: 100%;
}
.login_button svg {
    position: absolute;
    left: 18px;
    width: 30px;
}
.group-text p {
    color: #bebebe;
    font-size: 14px;
}
.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 16px;
    user-select: none;
    color: #bebebe;
}

/* Скрыть дефолтный checkbox */
.checkbox-container input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

/* Кастомный чекбокс */
.checkmark {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 25px;
    width: 25px;
    background-color: #eee;
    border: 2px solid #ccc;
    border-radius: 4px;
    transition: all 0.2s ease;
}

/* Состояние checked */
.checkbox-container input:checked ~ .checkmark {
    background-color: #6c5eff;
    border-color: #6c5eff;
}

.checkmark::after {
    content: "";
    position: absolute;
    display: none;
}

/* Галочка */
.checkbox-container input:checked ~ .checkmark::after {
    display: block;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.text-center {
    text-align: center;
}


.alert-success {
    position: relative;
    display: flex;
    align-items: center;
    margin: 32px 0;
    border-radius: 16px;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
    justify-content: space-between;
    background: #2A7B9B;
    background: linear-gradient(90deg, rgb(10 97 0) 15%, rgb(21 56 0) 100%);
}
.alert-success p {
    position: relative;
    padding-left: 60px;
    font-size: 18px;
    color: #dbff6a;
}
.alert-success p::before {
    content: "";
    position: absolute;
    left: 0;
    background-image: url("../img/lin2web/icon-success.svg");
    width: 40px;
    height: 40px;
    background-position: center center;
    background-size: cover;
    top: -40%;
    bottom: -50%;
}
.alert-danger {
    position: relative;
    display: flex;
    align-items: center;
    margin: 32px 0;
    border-radius: 16px;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
    justify-content: space-between;
    background: gray;
    background: linear-gradient(90deg, rgb(255 87 87) 15%, rgb(255 32 32) 100%);
}
.alert-danger p {
    position: relative;
    padding-left: 60px;
    font-size: 18px;
    color: #ffb8b8;
}
.alert-danger p::before {
    content: "";
    position: absolute;
    left: 0;
    background-image: url("../img/lin2web/icon-danger.svg");
    width: 40px;
    height: 40px;
    background-position: center center;
    background-size: cover;
    top: -40%;
    bottom: -50%;
}
.close {
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s ease;
    background-color: rgb(255 255 255 / 10%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
}
.close::before {
    content: "";
    position: absolute;
    background-image: url("../img/lin2web/close.svg");
    width: 18px;
    height: 18px;
}
.close:hover {
    background-color: rgb(255 255 255 / 20%);
}




@media only screen and (max-width : 675px) {
    .auth_wrapper {
        margin-left: 30px;
        margin-right: 30px;
    }
    .auth_nav_top ul {
        gap: 30px;
    }
    .auth_nav_top a {
        font-size: 14px;
    }
    .auth_top_r {
        gap: 15px;
    }
    .content {
        min-width: 100%;
    }
    .form-group.group {
        gap: 15px;
        justify-content: center;
    }
    .btn-gray-no-border {
        padding: 3px 8px;
        border-radius: 6px;
    }
}

