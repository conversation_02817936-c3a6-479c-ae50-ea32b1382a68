<?php

namespace App\Helpers;

class CacheDAdvExt {

    var $cachedport = 2012;
    var $cachedip = '*************';
    var $webadmin = 'Admin';
    var $connected;

    var $version = null;

    //To have explicit output, use CacheD_chatterbox, better than editing this array
    var $fsockerror = false;
    var $socketerrors = array(
        "1" => True, "01" => False, "02" => False, "03" => False, "04" => False,
        "05" => False, "06" => False, "07" => False, "08" => False, "09" => False,
        "010" => False, "011" => False,	"012" => False,	"013" => False,	"014" => False,
        "015" => False,	"016" => False,	"017" => False,	"018" => False,	"019" => False,
        "020" => False,	"021" => False,	"022" => False,	"023" => False,	"024" => False,
        "025" => False,	"026" => False);



    function tounicode ($string){
        $rs="";
        for($i=0; $i < strlen($string); $i++)	{ $rs .= $string[$i].chr(0); }
        $rs .= chr(0).chr(0);
        return($rs);
    }

    function CacheDInteractive($buf) {
        $fp = fsockopen($this->cachedip, $this->cachedport , $errno, $errstr, 5);
        $rs = '';
        if (!$fp){ $this->connected=false; return $this -> fsockerror;}
        else $this->connected=true;

        $packet = pack("s", (strlen($buf)+2)).$buf;
        fwrite($fp, $packet);
        $len = unpack("v", fread($fp, 2));
        $rid = unpack("c", fread($fp, 1));
        for ($i = 0;$i < (($len[1]-4) / 4);$i++) {
            $read = unpack("i", fread($fp, 4));
            $rs .= $read[1];
        }
        fclose($fp);
        $result = $this -> socketerrors[$rs];
        return($result);
    }

    function CheckCharacterPacket($char_id) {
        $buf = pack("c", 1);
        $buf .= pack("V", $char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetCharacterLocationPacket($char_id,$xloc,$yloc,$zloc) {
        $buf = pack("c", 2);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", 1);
        $buf .= pack("V", $xloc);
        $buf .= pack("V", $yloc);
        $buf .= pack("V", $zloc);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetBuilderCharacterPacket($char_id, $builder_level) {
        $buf = pack("c", 3);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $builder_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangeCharacterNamePacket($char_id,$new_char_name){
        $buf=pack("cV",4,$char_id);
        $buf .= $this -> tounicode($new_char_name);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function KickCharacterPacket($char_id){
        $buf = pack("cV",5,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }
    function SetHero($char_id) {
        $buf=pack("cVV",107,$char_id,2).$this->tounicode("admin");
        return $this -> CacheDInteractive($buf);
    }

    function SetNobless($char_id) {
        $buf=pack("cVV",106,$char_id,1).$this->tounicode("admin");
        return $this -> CacheDInteractive($buf);
    }

    function AddSkillPacket($char_id, $skill_id, $skill_level) {
        $buf=pack("cVVV",6,$char_id,$skill_id,$skill_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelSkillPacket($char_id, $skill_id) {
        $buf = pack("c", 7);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $skill_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModSkillPacket($char_id, $skill_id, $new_skill_level) {
        $buf=pack("cVVV",8,$char_id,$skill_id,$new_skill_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelItemPacket($char_id, $item_warehouse, $item_uid, $amount) {
        $buf = pack("cVVVVV",13,$char_id,$item_warehouse,$item_uid,$amount,0);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModCharPacket($char_id, $new_SP, $new_EXP, $new_Karma, $new_PK, $new_PKP, $new_PVP) {
        $new_EXP2 = intval($new_EXP / 0x100000000);
        if ($new_EXP2 < 0) {
            $new_EXP2 = 0;
        } else {
            $new_EXP = $new_EXP - ($new_EXP2 * 0x100000000);
        }
        $buf = pack("c", 15);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $new_SP);
        $buf .= pack("V", $new_EXP);
        $buf .= pack("V", $new_EXP2);
        $buf .= pack("V", $new_Karma);
        $buf .= pack("V", $new_PK);
        $buf .= pack("V", $new_PKP);
        $buf .= pack("V", $new_PVP);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModChar2Packet($char_id, $gender, $race, $class, $face, $hair_shape, $hair_color) {
        $buf = pack("c", 16);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $gender);
        $buf .= pack("V", $race);
        $buf .= pack("V", $class);
        $buf .= pack("V", $face);
        $buf .= pack("V", $hair_shape);
        $buf .= pack("V", $hair_color);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function PunishCharPacket($char_id,$punish_id,$punish_time) {
        $buf = pack("cVVV",18,$char_id,$punish_id,$punish_time);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetBuilderAccountPacket($account_name, $builder_level) {
        $buf = pack("c", 19);
        $buf .= $this -> tounicode($account_name);
        $buf .= pack("V", $builder_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DisableCharacterPacket($char_id) {
        $buf = pack("c", 20);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", 1);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function EnableCharacterPacket($char_id, $account_id) {
        $buf = pack("c", 21);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $account_id);
        return $this -> CacheDInteractive($buf);
    }

    function ModChar3Packet($char_id, $add_SP, $add_EXP, $add_Karma, $add_PK, $add_PKP, $add_PVP) {
        $buf = pack("c", 29);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $add_SP);
        $buf .= pack("V", $add_EXP);
        $buf .= pack("V", $add_Karma);
        $buf .= pack("V", $add_PK);
        $buf .= pack("V", $add_PKP);
        $buf .= pack("V", $add_PVP);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function MoveCharacterPacket($char_id,$new_account_id,$new_account_name){
        $buf=pack("cVVS",31,$char_id,$new_account_id,$this -> tounicode($new_account_name));
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DeleteCharPacket($char_id) {
        $buf = pack("cV",34,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function PledgeChangeOwnerPacket($pledge_id, $new_leader_id) {
        //$buf = pack("cVV",37,$pledge_id,$new_leader_id);
        $buf = pack("cVV",37,$pledge_id,$new_leader_id);
        //$buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }


    function PledgeDeletePacket($pledge_id) {
        $buf = pack("c", 38);
        $buf .= pack("V", $pledge_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function BanCharPacket($char_id, $hours ) {
        $buf = pack("c", 39);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $hours);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SendHomePacket($char_id) {
        $buf = pack("cV",45,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangePledgeLevelPacket($pledge_id, $new_level) {
        $buf = pack("cVV",46,$pledge_id, $new_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function CreatePledgePacket($pledge_name, $leader_id) {
        $buf = pack("c",47);
        $buf .= $this -> tounicode($pledge_name);
        $buf .= pack("V",$leader_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetSkillAllPacket($char_id) {
        $buf = pack("cV",48,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function RestoreCharToAccount($char_id,$new_char_name,$account_id,$account_name) {
        $buf = pack("c", 49);
        $buf .= pack("V", $char_id);
        $buf .= $this -> tounicode($new_char_name);
        $buf .= pack("V", $account_id);
        $buf .= $this -> tounicode($account_name);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ManIntAnnouncePacket($text,$id,$interval) {
        $buf = pack("c", 51);
        $buf .= pack("V", 1);
        $buf .= pack("V", $interval);
        $buf .= pack("V", $id);
        $buf .= $this->tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelItem2Packet($item_uid, $amount) {
        $buf = pack("cVV", 54, $item_uid, $amount);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function AddItem2Packet($char_id,$warehouse,$item_type,$q,$enchant, $eroded, $bless, $wished) {
        $bless = 0;
        $q_h = intval($q / 0x100000000);
        if ($q_h < 0) {
            $q_h = 0;
        } else {
            $q = $q - ($q_h * 0x100000000);
        }
        $buf = pack("c", 55);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $warehouse);
        $buf .= pack("V", $item_type);
        $buf .= pack("V", $q);
        $buf .= pack("V", $q_h);
        $buf .= pack("V", $enchant);
        $buf .= pack("V", $eroded);
        $buf .= pack("V", $bless);
        $buf .= pack("V", $wished);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= pack("V", 0);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetSociality($char_id,$social) {
        $buf = pack("c", 69);
        $buf .= pack("i", $char_id);
        $buf .= pack("i", $social);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetInstantAnnouncePacket($text) {
        $buf = pack("c", 70);
        $buf .= $this -> tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangeSubJobPacket($char_id, $subjob_id) {
        $buf = pack("c", 89);
        $buf .= pack ("V", $char_id);
        $buf .= pack ("V", $subjob_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function StopCharPacket($char_id, $minutes) {
        $buf = pack("c", 90);
        $buf .= pack ("V", $char_id);
        $buf .= pack ("V", $minutes);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelPledgeEmblemPacket($pledge_id) {
        $buf = pack("c", 98);
        $buf .= pack ("V", $pledge_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SendPrivateAnnouncePacket($char_id, $text) {
        $buf = pack("c", 101);
        $buf .= pack ("V", $char_id);
        $buf .= $this -> tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }


    function EternalBanPacket($char_id) {
        $buf = pack("c", 104);
        $buf .= pack ("V", $char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetNameColor($search_type,$char,$cl_change_type,$color) {
        $buf = pack("c", 122);
        $buf .= pack("V", $char);
        $buf .= pack("V", $color);
        $buf .= pack("V", $cl_change_type);
        $buf .= pack("V", 0);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    public function addBan($hwid,$account,$action,$time,$delay,$comment) {
        $buf = pack("cV", 0x99, 3);
        $buf .= $this->tounicode($hwid);
        $buf .= $this->tounicode($account);
        $buf .= $this->tounicode($action);
        $buf .= pack("VV",$time,$delay);
        $buf .= $this->tounicode($comment);
        return $this->CacheDInteractive($buf);
    }

    public function removeBan($hwid,$account) {
        $buf = pack("cV", 0x99, 4);
        $buf .= $this->tounicode($hwid);
        $buf .= $this->tounicode($account);

        return $this->CacheDInteractive($buf);
    }
}
