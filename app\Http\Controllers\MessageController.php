<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    public function store(Request $request, Ticket $ticket)
    {
        $userId = auth()->id();
        $cacheKey = "user_{$userId}_last_message_time";

        // Проверяем, прошло ли 30 секунд с момента последнего действия
        if (cache()->has($cacheKey)) {
            $lastMessageTime = cache()->get($cacheKey);
            if (now()->diffInSeconds($lastMessageTime) < 30) {
                return redirect()->back()->withErrors(['message' => __('You can send messages no more than once every 30 seconds.')]);
            }
        }

        $request->validate([
            'message' => 'required|string|max:5000',
        ]);

        $ticket->messages()->create([
            'user_id' => $userId,
            'message' => $request->message,
        ]);

        $ticket->touch();

        // Устанавливаем время последнего действия
        cache()->put($cacheKey, now(), 30);

        return redirect()->back()->with('success', 'Сообщение отправлено.');
    }

}
