<?php

namespace App\Services\Payment;

use App\Services\Payment\PaymentProcessingService;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Webhook;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class StripeService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;
        Stripe::setApiKey(config('app.pay_system.stripe_secret_key')); // Установка ключа API Stripe
    }

    public function createPayment($payment_id, $count)
    {
        try {
            $currency = config('app.pay_system.stripe_currency'); // Валюта
            $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
            $sum = (int)ceil($count * $coin_price); // Расчет суммы платежа

            $metadata = [
                'payment_id' => $payment_id,
            ];

            $checkoutSession = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => $currency,
                        'product_data' => [
                            'name' => config('app.custom_config.donate_coin_name'),
                        ],
                        'unit_amount' => $sum * 100, // Stripe использует сумму в центах
                    ],
                    'quantity' => 1,
                ]],
                'payment_intent_data' => [ // Метаданные передаются здесь
                    'metadata' => $metadata,
                ],
                'mode' => 'payment',
                'success_url' => route('donate'),
                'cancel_url' => route('donate'),
            ]);

            return $checkoutSession->url; // Возвращаем URL для оплаты
        } catch (\Exception $e) {
            Log::error('Stripe: Error creating payment', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function handleWebhook(Request $request)
    {
        try {
            $endpointSecret = config('app.pay_system.stripe_webhook_secret'); // Получение секретного ключа вебхука
            $payload = $request->getContent(); // Получение необработанных данных запроса
            $signature = $request->header('Stripe-Signature'); // Получение заголовка Stripe-Signature

            $event = Webhook::constructEvent($payload, $signature, $endpointSecret); // Проверка подписи вебхука

            if ($event->type === 'payment_intent.succeeded') {
                $paymentIntent = $event->data->object; // Получаем объект PaymentIntent
                $metadata = $paymentIntent->metadata;

                $paymentId = $metadata->payment_id ?? null;

                if (!$paymentId) {
                    return response('Missing metadata payment_id', 400);
                }

                $this->paymentProcessingService->processPayment($paymentId); // Обработка платежа
                Log::info('Payment processed successfully', ['payment_id' => $paymentId]);
            } else {
                Log::warning('Unhandled Stripe Webhook Event', ['event_type' => $event->type]);
            }

            return response('Webhook handled', 200);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('Webhook signature verification failed', ['error' => $e->getMessage()]);
            return response('Signature verification failed', 400);
        } catch (\Exception $e) {
            Log::error('Error handling webhook', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }

    // Проверка разрешенных IP для вебхуков (опционально)
    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '************',
        ];

        return in_array($ip, $allowedIps);
    }
}
