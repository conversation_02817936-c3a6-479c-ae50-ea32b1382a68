@font-face {
    font-family: 'TrajanSansPro-Regular';
    src: url("../fonts/TrajanSansPro-Regular.otf") format('opentype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'TrajanSansPro-Semibold';
    src: url("../fonts/TrajanSansPro-Semibold.otf") format('opentype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Montserrat-Regular';
    src: url("../fonts/Montserrat-Regular.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}
@font-face {
    font-family: 'Montserrat-SemiBold';
    src: url("../fonts/Montserrat-SemiBold.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}


/* ===== 🎨 Глобальные переменные ===== */
:root {
    --color-primary: #ffd782;
    --color-secondary: #e1ce87;
    --color-background: #f5f7fa;
    --color-dark: #1e1e1e;
    --color-light: #ffffff;
    --color-accent: #ff6b6b;
    --accent-bg-color: rgb(149 111 0 / 90%);
    --color-gray: gray;
    --bgc-color: rgb(48 44 76 / 90%);
    --bg-form-head: rgb(209 163 0 / 40%);
    --bg-form-table: rgb(48 46 37 / 90%);
    --bg-dark: #20232A;

    --font-accent-regular: TrajanSansPro-Regular;
    --font-accent-bold: TrajanSansPro-Semibold;
    --font-regular: Montserrat-Regular;
    --font-bold: Montserrat-SemiBold;


    /* размеры */
    --sidebar-width: 250px;
    --container-padding: 30px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
* {
    outline: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html, body {
    margin: 0;
    height: 100%;
}
.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}
main {
    flex: 1;
}
body {
    font-family: var(--font-regular), sans-serif;
    background: #120F0D;
}

ul, li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
p {
    margin: 0;
    color: #bebebe;
}
h3 {
    font-family: var(--font-accent-regular);
    color: #fff;
}
a {
    color: #fff;
    text-decoration: none;
}
small {
    color: #bebebe;
}


/*FORM START*/
select.form-control {
    /* Убираем нативный appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    /* Фон, граница и скругления */
    background: #292D34;
    border: 1px solid rgb(255 255 255 / 10%);
    border-radius: 12px;

    /* Внутренние отступы (правый — для стрелки) */
    padding: 10px 36px 10px 12px;

    /* Шрифт и цвет */
    font-size: 14px;
    line-height: 1.4;
    color: #fff;

    /* Курсор */
    cursor: pointer;

    /* Переходы для фокуса */
    transition: border-color 0.2s, box-shadow 0.2s;

    /* Кастомная стрелка через встроенное SVG */
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='10' height='6' viewBox='0 0 10 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0l5 6 5-6z' fill='%23999'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
}
/* Стиль при фокусе */
select.form-control:focus {
    outline: none;
    border-color: #6ca0f6;
    box-shadow: 0 0 0 3px rgba(108, 160, 246, 0.3);
}
/* (Опционально) чуть более читаемые опции */
select.form-control option {
    padding: 8px;
    background-color: #292D34;
    color: #fff;
}
:focus-visible {
    outline: -webkit-focus-ring-color auto 0;
}
.form-control {
    box-sizing: border-box;
    background: #292D34;
    border: 1px solid rgb(255 255 255 / 10%);
    padding: 14px 20px;
    border-radius: 12px;
    width: 100%;
    color: #fff;
}
.form-control::placeholder {
    font-size: 14px;
}
.form-group {
    margin-bottom: 30px;
}
.form-group:last-child {
    margin-bottom: 0;
}
.form-group label {
    color: #bebebe;
    font-size: 14px;
    display: flex;
    margin-bottom: 5px;
}

::-webkit-input-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
::-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-ms-input-placeholder {
    color: #7b7873;
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:focus::-webkit-input-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus::-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-ms-input-placeholder {
    opacity: 0;
    -ms-transform: translateX(20px);
    transform: translateX(20px);
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/*FORM END*/

.other-bg {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: -10;
    background-image: url("../img/bg-other.jpg");
    background-size: cover;
    background-position: 50%;
    background-repeat: no-repeat;
}

.main-bg {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: -10;
    background-image: url("../img/bg.jpg");
    background-size: cover;
    background-position: 50%;
    background-repeat: no-repeat;
}

.wrap {
    width: 1320px;
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}



/*LANGUAGE*/
.nav__langs {
    position: relative;
    z-index: 1;
}
.nav__langs-item {
    cursor: pointer;
    height: 35px;
    background: radial-gradient(128% 100% at 50% 0%, #ff5650 2.6%, #892b27 57.29%, #ff0000 100%);
    box-shadow: 0px 17px 34px 0px rgba(255, 98, 0, 0.18), 0px 11.019px 19.912px 0px rgba(255, 98, 0, 0.14), 0px 6.548px 10.83px 0px rgba(255, 98, 0, 0.11), 0px 3.4px 5.525px 0px rgba(255, 98, 0, 0.09), 0px 1.385px 2.77px 0px rgba(255, 98, 0, 0.07), 0px 0.315px 1.338px 0px rgba(255, 98, 0, 0.04);
    padding: 0 5px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    transition: color 0.3s ease-in-out;
    gap: 10px;
}

.nav__langs-item:hover, .nav__langs-item.active {
    color: #fff;
}
.nav__langs-item img {
    width: 22px;
    aspect-ratio: 34/24;
    border-radius: 2px;
}
.nav__langs-item--current:after {
    content: "";
    width: 12px;
    aspect-ratio: 12 / 6;
    background: url("../img/lin2web/arrow.png");
    background-size: 100% 100%;
    transition: transform 0.3s ease-in-out;
}

.nav__langs-item.active:after {
    transform: scale(1, -1);
}
.nav__langs-stroke {
    list-style-type: none;
    position: absolute;
    z-index: 1;
    top: calc(100% + 3px);
    right: 0;
    display: none;
}
.nav__langs-stroke li:not(:first-child) {
    margin-top: 10px;
}
.nav__links {
    list-style-type: none;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
    column-gap: 30px;
    margin-left: auto;
    position: relative;
    z-index: 2;
}
.nav__links-item {
    font-family: var(--primary-font), sans-serif;
    font-weight: 400;
    font-size: 15px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    column-gap: 6px;
    transition: color 0.3s ease-in-out;
}
.nav__links-item:hover {
    color: var(--brand-color);
}
.nav__links-item img {
    height: 16px;
}
.nav__switch {
    width: 34px;
    height: 34px;
    position: relative;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.15);
    display: none;
    transition: background-color 0.3s ease-in-out, border 0.3s ease-in-out;
    z-index: 3;
    border-radius: 50%;
    margin-left: auto;
}
.nav__switch:hover {
    background-color: var(--brand-color);
}
.nav__switch:hover:after, .nav__switch:hover:before, .nav__switch:hover .nav__switch-item {
    background: #1c2227;
}
.nav__switch:hover .nav__switch-item {
    transform: scale(0.7, 1);
}
.nav__switch.active:after {
    transform: rotate(-135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active:before {
    transform: rotate(135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active .nav__switch-item {
    transform: scale(0, 1);
}
.nav__switch:after {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: 11px;
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch:before {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: calc(100% - 13px);
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch-item {
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    top: calc((100% - 2px) / 2);
    left: 6px;
    transition: all 0.3s ease-in-out;
    transform: scale(0.7, 1);
}
/*LANG END*/

.header .wrap {
    position: relative;
}
.top_line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    position: absolute;
    left: 0;
    right: 0;
}
.gw-burger {
    display: none;
}
.top_menu {
    display: flex;
    gap: 30px;
}

.logo {
    text-align: center;
}
.logo img {
    height: 360px;
}
.main-text {
    margin-top: 80px;
    text-align: center;
    line-height: 1.6;
}
.main-text p {
    font-family: var(--font-accent-regular), sans-serif;
    font-size: 32px;
    color: #fff;
}
h1 {
    font-family: var(--font-accent-bold), sans-serif;
    color: var(--color-primary);
    font-size: 38px;
}



.btn-main {
    position: relative;
    width: fit-content;
    overflow: hidden;
    cursor: pointer;
    padding: 22px 48px;
    border-radius: 16px;
    background: radial-gradient(128% 100% at 50% 0%, #ff5650 2.6%, #892b27 57.29%, #ff0000 100%);
    box-shadow: 0px 17px 34px 0px rgba(255, 98, 0, 0.18), 0px 11.019px 19.912px 0px rgba(255, 98, 0, 0.14), 0px 6.548px 10.83px 0px rgba(255, 98, 0, 0.11), 0px 3.4px 5.525px 0px rgba(255, 98, 0, 0.09), 0px 1.385px 2.77px 0px rgba(255, 98, 0, 0.07), 0px 0.315px 1.338px 0px rgba(255, 98, 0, 0.04);
}
.btn-main::before {
    background: radial-gradient(106.8% 45.71% at 50% 30.71%, #FFDDA8 31.33%, rgba(69, 24, 5, 0.60) 100%);
}
.btn-main::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 12px;
    padding: 2px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 2;
}
.btn-main::after {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    content: '';
    opacity: 0;
    transition: opacity 200ms ease-in-out;
    z-index: 1;
    background: radial-gradient(128% 100% at 50% 0%, #ffa250 2.6%, #892b27 57.29%, #ff8500 100%);
}
.btn-main .text {
    background: linear-gradient(180deg, #FFF0D4 20.5%, rgba(255, 221, 168, 0.60) 28.75%, rgba(255, 240, 212, 0.60) 37.5%), #FFF;
    -webkit-background-clip: text;
}
.btn-main .text {
    font-size: 20px;
    line-height: 140%;
}
.btn-main:hover::after {
    opacity: 1;
}
.btn-main .text {
    font-family: var(--font-accent-bold), sans-serif;
    font-size: 16px;
    line-height: 125%;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    position: relative;
}
.btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 42px;
    text-align: center;
    transition: .4s opacity;
    margin-bottom: 60px;
}



.nav-link {
    font-family: var(--font-accent-bold), sans-serif;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}
.nav-link:hover {
    color: var(--color-primary);
}
.nav-item.active .nav-link {
    color: var(--color-primary);
}
.top_right {
    display: flex;
    gap: 30px;
}

/*ACP PANEL TOP*/
.acp-panel-top-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 6px 10px;
    height: 35px;
    border-radius: 8px;
    /* background: linear-gradient(0deg, rgb(98 60 38) 0%, rgb(179 122 86) 100%); */
    border: 1px solid #ebb686;
    color: #ffe89b;
    font-size: 14px;
    background: radial-gradient(128% 100% at 50% 0%, #ff5650 2.6%, #892b27 57.29%, #ff0000 100%);
    box-shadow: 0px 17px 34px 0px rgba(255, 98, 0, 0.18), 0px 11.019px 19.912px 0px rgba(255, 98, 0, 0.14), 0px 6.548px 10.83px 0px rgba(255, 98, 0, 0.11), 0px 3.4px 5.525px 0px rgba(255, 98, 0, 0.09), 0px 1.385px 2.77px 0px rgba(255, 98, 0, 0.07), 0px 0.315px 1.338px 0px rgba(255, 98, 0, 0.04);
}

.dropdown-menu {
    filter: grayscale(1);
    display: none;
    position: absolute;
    right: 0;
    min-width: 160px;
    margin-top: 4px;
    text-align: right;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    align-items: center;
    padding: 3px 10px;
    border-radius: 6px;
    background: rgb(138, 107, 83);
    background: linear-gradient(0deg, rgb(68 47 30 / 30%) 0%, rgb(130 101 78 / 30%) 100%);
    border: 1px solid rgb(235 182 134 / 30%);
    z-index: 999999;
}

.dropdown-menu a {
    font-size: 14px;
    color: #fff;
    text-decoration: none;
    display: block;
}

.dropdown-menu a:hover {
    opacity: 0.7;
}
.dropdown-menu.show {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: 5px;
}
.arrow-down-acp {
    margin-left: 10px;
    border: solid #ffe89b;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 2px;
    width: 2px;
    height: 2px;
    transform: rotate(45deg);
    transition: transform 0.3s ease;
    transform-origin: 60% 70%;
    margin-top: -5px;
}

.arrow-down-acp.rotate {
    transform: rotate(225deg);
}
/*ACP PANEL TOP END*/


.acp-panel-top {
    display: flex;
    align-items: center;
    gap: 15px;
}
.auth_user_header_balance {
    font-size: 14px;
}

#main {
    height: 100vh;
    padding-top: 60px;
}


#diary {
    padding: 0;
    position: relative;
    max-width: 1920px;
    margin: 0 auto;
    position: relative;
}
#diary video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}
.diary_gradient {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 0 112px;
    width: 100%;
    height: 1080px;
    background: radial-gradient(50% 50% at 50% 50%, rgba(19, 16, 14, 0.00) 0%, #13100E 100%), linear-gradient(180deg, rgba(19, 16, 14, 0.00) 63.5%, #13100E 100%), linear-gradient(180deg, #13100E 0%, rgba(19, 16, 14, 0.00) 62.5%);
}


.footer {
    background: #120F0D;
    padding: 50px 0;
}
.footer_all {
    display: flex;
    justify-content: space-between;
}

.copyright {
    font-family: var(--font-accent-regular), sans-serif;
}
.copyright img {
    height: 150px;
}
.copyright h4 {
    color: #bebebe;
}
.copyright p {
    font-size: 14px;
}
.copyright a {
    color: var(--color-primary);
}
.menu-footer h3 {
    font-family: var(--font-accent-bold), sans-serif;
    margin-bottom: 15px;
    color: #ffd782;
    font-size: 16px;
}
.menu-footer {
    display: flex;
    flex-direction: column;
    gap: 15px;
}
.menu-footer a {
    font-family: var(--font-accent-regular), sans-serif;
}
.menu-footer a:hover {
    color: var(--color-primary);
}

.top--right-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}
.btn-wrapper.btn-full {
    margin-bottom: 10px;
}
.btn-full button {
    border: 0;
    width: 100%;
}
.btn-red {
    width: max-content;
    font-family: var(--font-accent-bold), sans-serif;
    height: 35px;
    background: radial-gradient(128% 100% at 50% 0%, #ff5650 2.6%, #892b27 57.29%, #ff0000 100%);
    box-shadow: 0px 17px 34px 0px rgba(255, 98, 0, 0.18), 0px 11.019px 19.912px 0px rgba(255, 98, 0, 0.14), 0px 6.548px 10.83px 0px rgba(255, 98, 0, 0.11), 0px 3.4px 5.525px 0px rgba(255, 98, 0, 0.09), 0px 1.385px 2.77px 0px rgba(255, 98, 0, 0.07), 0px 0.315px 1.338px 0px rgba(255, 98, 0, 0.04);
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-radius: 8px;
}
.btn-red:hover {
    background: radial-gradient(128% 100% at 50% 0%, #ffa250 2.6%, #892b27 57.29%, #ff8500 100%);
}

.btn-golden {
    width: max-content;
    font-family: var(--font-accent-bold), sans-serif;
    height: 35px;
    background: radial-gradient(128% 100% at 50% 0%, #ffa250 2.6%, #892b27 57.29%, #ff8500 100%);
    box-shadow: 0px 17px 34px 0px rgba(255, 98, 0, 0.18), 0px 11.019px 19.912px 0px rgba(255, 98, 0, 0.14), 0px 6.548px 10.83px 0px rgba(255, 98, 0, 0.11), 0px 3.4px 5.525px 0px rgba(255, 98, 0, 0.09), 0px 1.385px 2.77px 0px rgba(255, 98, 0, 0.07), 0px 0.315px 1.338px 0px rgba(255, 98, 0, 0.04);
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-radius: 8px;
}
.btn-golden:hover {
    background: radial-gradient(128% 100% at 50% 0%, #ff5650 2.6%, #892b27 57.29%, #ff0000 100%);
}

.about_block {
    padding: 60px 0;
}

.block_title {
    font-family: var(--font-accent-regular), sans-serif;
    color: var(--color-primary);
    font-size: 38px;
    line-height: 1.2;
}
.title_center_block {
    text-align: center;
    line-height: 1.6;
}
.title_center_block {
    font-size: 22px;
    font-family: var(--font-accent-regular), sans-serif;
    color: #fff;
}
.server_about_all {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 120px;
    margin-bottom: 60px;
}
.s_about_item {
    text-align: center;
}
.s_about_item h3 {
    font-family: var(--font-accent-regular), sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    color: var(--color-primary);
}
.s_about_item p {
    margin-top: 10px;
}
#diary .btn-wrapper {
    position: absolute;
    bottom: 15%;
    left: 0;
    right: 0;
}
#other {
    padding: 120px 0;
}
.startgame {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
}
.startgame_steps_num {
    color: #bebebe;
    font-size: 20px;
}
.startgame_steps_name {
    color: #fff;
    font-size: 24px;
    font-family: var(--font-accent-regular), sans-serif;
}
.subtitle {
    color: var(--color-primary);
    font-family: var(--font-accent-regular), sans-serif;
    font-size: 26px;
}
.startgame_container p {
    margin: 15px 0;
}





/*PAYMENT START*/
#payment-form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
}
.payment__range.noUi-horizontal .noUi-handle {
    width: 26px;
    height: 26px;
    border-radius: 26px;
    background: var(--color-primary);
    border: none;
    box-shadow: none;
    top: -9px;
    right: -13px;
}
.payment__range.noUi-target {
    background: #1c1718;
    border-radius: 5px;
    border: 1px solid #505050;
    box-shadow: none;
}
.payment__range {
    margin-top: 20px;
}
.payment__range .noUi-value {
    font-size: 10px;
    line-height: 18px;
    font-weight: 400;
    color: #afafaf;
}
.payment__range .noUi-marker-horizontal.noUi-marker-large {
    height: 10px;
}
.payment__range .noUi-marker-horizontal.noUi-marker {
    width: 1px;
}
.payment__range .noUi-marker-large {
    background: #686363;
}
.payment__range .noUi-marker {
    background: #483f41;
}
.payment__range .noUi-pips-horizontal {
    height: 42px;
}
.payment__range.noUi-horizontal .noUi-connect {
    background: #ffbd01;
}
.payment__range .noUi-tooltip {
    border: 1px solid #505050;
    border-radius: 5px;
    background: #1c1718;
    color: #fff;
    font-size: 14px;
    line-height: 1em;
    font-weight: 400;
    padding: 6px 5px 5px;
}
.payment__range.noUi-horizontal {
    height: 10px;
    margin-bottom: 60px;
}
.noUi-handle-lower::before, .noUi-handle-lower::after {
    display: none;
}
.pay_method img {
    display: flex;
    height: 40px;
}
.payment_select {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}
.payment-pay {
    width: 100%;
}
.pay_method {
    border: 2px solid transparent;
    cursor: pointer;
}
.pay_method:hover {
    opacity: 0.7;
    transition: 0.3s;
}
.pay_method.active {
    border: 2px solid var(--color-primary);
    border-radius: 6px;
}
.pay-currency {
    display: flex;
    color: #bebebe;
    gap: 30px;
}
.payment__input {
    display: flex;
    align-items: center;
}
.payment_bonus_price {
    background-color: var(--color-primary);
    height: 45px;
    min-width: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.payment-pay input {
    border-radius: 0;
}
.bonus-item {
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    border-bottom: 1px solid gray;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
.bonus-item p {
    color: #fff;
}
.bonus-item span {
    color: var(--color-primary);
}
.donate-title {
    font-size: 22px;
    color: #fff;
}
.donate-title-block p {
    color: #bebebe;
}
.donate-title-block {
    margin-bottom: 15px;
}
.curr-item .curr_symbol {
    color: var(--color-primary);
}
.payment-pay .form-group {
    margin-bottom: 15px;
}
.payment-pay .form-group:last-child {
    margin-bottom: 0;
}
.payment-pay .login_button {
    margin-top: 30px;
    text-align: center;
}
.total_text {
    color: #bebebe;
    text-align: center;
}
/*PAYMENT END*/


.btn-primary {
    border: 0;
    cursor: pointer;
    background-color: #6c5eff;
    height: 42px;
    min-width: 180px;
    max-width: max-content;
    padding: 0 15px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
    color: #fff;
    font-size: 14px;
}
.btn-primary:hover {
    box-shadow: 0 0 8px #6c5eff;
}
.btn-primary svg {
    width: 24px;
}
.login_button button {
    width: 100%;
    max-width: 100%;
    box-shadow: 0 4px 70px rgba(112, 96, 255, 0.5);
    height: 50px;
    font-size: 18px;
    position: relative;
}
.login_button svg {
    position: absolute;
    left: 18px;
    width: 30px;
}
.text-danger {
    color: #ff4d4d;
    font-size: 13px;
    margin-top: 5px;
}

/* Успешно */
.text-success {
    color: #00cc66;
    font-size: 14px;
    margin-top: 10px;
}

@media only screen and (max-width: 1024px) {
    .wrap {
        width: 100%;
        padding-left: 30px;
        padding-right: 30px;
    }
    .auth_user_header_balance {
        display: none;
    }
    .top_line {
        padding-left: 30px;
        padding-right: 30px;
    }
}

@media only screen and (max-width: 960px) {
    .top_menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 300px;
        height: 100%;
        background-color: #221e1d;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        padding-top: 70px;
        padding-left: 20px;
        box-sizing: border-box;
    }

    ul.top_menu.active {
        transform: translateX(0);
    }

    .navigation__burger {
        display: block;
    }

    .gw-burger {
        position: relative;
        z-index: 999999;
        width: 40px;
        height: 40px;
        cursor: pointer;
        box-sizing: border-box;
    }

    .gw-burger__line {
        width: 20px;
        height: 2px;
        margin: auto;
        position: absolute;
        left: 5px;
        right: 5px;
        background-color: #909090;
        border-radius: 2px;
        -webkit-transition: all .3s;
        transition: all .3s;
    }

    .gw-burger__line_pos_top {
        pointer-events: none;
        top: 32%;
    }

    .gw-burger__line_pos_middle {
        pointer-events: none;
        top: 50%;
        transform: translateY(-50%);
    }

    .gw-burger__line_pos_bottom {
        pointer-events: none;
        bottom: 32%;
    }

    .gw-burger_active .gw-burger__line_pos_top {
        transform: rotate(45deg) translateY(-50%);
        -webkit-transform-origin: center top;
        transform-origin: center top;
        top: 50%;
    }

    .gw-burger_active .gw-burger__line_pos_middle {
        transform: rotate(180deg);
        opacity: 0;
    }

    .gw-burger_active .gw-burger__line_pos_bottom {
        transform: rotate(-45deg) translateY(50%);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        bottom: 50%;
    }

    .footer_all {
        flex-wrap: wrap;
        gap: 30px;
    }
}



@media only screen and (max-width: 675px) {
    .logo img {
        height: 200px;
    }
    .menu-footer {
        width: 48%;
    }
    .menu-footer {
        gap: 10px;
    }
    .menu-footer h3 {
        margin-bottom: 0;
    }
    .footer_all {
        flex-wrap: wrap;
        gap: 0;
    }
    .menu-footer {
        margin-bottom: 50px;
    }
    .startgame {
        grid-template-columns: repeat(1, 1fr);
        gap: 30px;
    }
    #payment-form {
        grid-template-columns: repeat(1, 1fr);
        gap: 30px;
    }
    .server_about_all {
        margin-top: 60px;
    }
    .copyright {
        text-align: center;
        margin: auto;
        padding-bottom: 30px;
    }
}

@media only screen and (max-width: 480px) {
    .block_title {
        font-size: 22px;
    }
    .title_center_block {
        font-size: 16px;
    }
    .s_about_item {
        width: 48%;
    }
    .server_about_all {
        margin-top: 30px;
        margin-bottom: 30px;
    }
    .s_about_item p {
        font-size: 12px;
    }
    #diary .btn-wrapper {
        position: relative;
        bottom: 0;
    }
    #diary .btn-wrapper {
        position: relative;
        bottom: 0;
        margin-top: 0;
    }
    h1 {
        font-size: 32px;
        line-height: 1.2;
    }
    .main-text p {
        font-size: 20px;
    }
    .top--right-menu .nav-item {
        display: none;
    }
    .auth_wrapper {
        margin-left: 15px;
        margin-right: 15px;
    }
}
