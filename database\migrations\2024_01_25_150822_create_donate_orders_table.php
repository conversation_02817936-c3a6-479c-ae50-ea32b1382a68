<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('donate_orders', function (Blueprint $table) {
            $table->id();
            $table->string('payment_id')->unique();
            $table->string('user_id');
            $table->integer('count')->default(0);
            $table->string('status')->default(0);
            $table->string('server_id')->nullable();
            $table->string('pay_system');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donate_orders');
    }
};
