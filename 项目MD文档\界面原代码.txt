<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库连接测试</h1>
            <p>XXTT2 游戏管理系统 - 实时数据库状态监控</p>
        </div>

        <div class="content">
            <div class="connection-grid">
                                <div class="connection-card success">
                    <div class="connection-title">Web CMS数据库 (lin2web_cms)</div>
                    <div class="connection-description">连接名: mysql</div>

                    <span class="status-badge status-success">
                                                    ✅ 连接成功
                                            </span>

                    <div><strong>状态信息:</strong> 连接成功</div>

                                        <div class="connection-info">
                        <strong>连接详情:</strong>
                                                <div class="info-item">
                            <span class="info-label">Driver:</span>
                            <span class="info-value">mysql</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Host:</span>
                            <span class="info-value">mysql</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Port:</span>
                            <span class="info-value">3306</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Database:</span>
                            <span class="info-value">lin2web_cms</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Version:</span>
                            <span class="info-value">8.0.43</span>
                        </div>
                                            </div>

                                        <div style="margin-top: 10px;">
                        <strong>测试结果:</strong> 1
                    </div>
                                    </div>
                                <div class="connection-card success">
                    <div class="connection-title">Redis缓存数据库</div>
                    <div class="connection-description">连接名: redis</div>

                    <span class="status-badge status-success">
                                                    ✅ 连接成功
                                            </span>

                    <div><strong>状态信息:</strong> 连接成功</div>

                                        <div class="connection-info">
                        <strong>连接详情:</strong>
                                                <div class="info-item">
                            <span class="info-label">Host:</span>
                            <span class="info-value">redis</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Port:</span>
                            <span class="info-value">6379</span>
                        </div>
                                                <div class="info-item">
                            <span class="info-label">Test_write_read:</span>
                            <span class="info-value">读写测试通过</span>
                        </div>
                                            </div>

                                        <div style="margin-top: 10px;">
                        <strong>测试结果:</strong> PONG
                    </div>
                                    </div>
                                <div class="connection-card error">
                    <div class="connection-title">游戏世界数据库 (lin2world)</div>
                    <div class="connection-description">连接名: lin2world</div>

                    <span class="status-badge status-error">
                                                    ❌ 连接失败
                                            </span>

                    <div><strong>状态信息:</strong> 连接失败: could not find driver</div>


                                    </div>
                                <div class="connection-card error">
                    <div class="connection-title">用户账户数据库 (lin2db)</div>
                    <div class="connection-description">连接名: lin2db</div>

                    <span class="status-badge status-error">
                                                    ❌ 连接失败
                                            </span>

                    <div><strong>状态信息:</strong> 连接失败: could not find driver</div>


                                    </div>
                            </div>

            <div class="stats-section">
                <h2 class="stats-title">📊 数据库统计信息</h2>
                <div class="stats-grid">
                                        <div class="stats-card">
                        <h3>🌐 Web CMS 数据</h3>
                                                    <div class="stats-item">
                                用户数量: <span class="stats-number">2</span>
                            </div>
                            <div class="stats-item">
                                新闻数量: <span class="stats-number">0</span>
                            </div>
                            <div class="stats-item">
                                订单数量: <span class="stats-number">0</span>
                            </div>
                                            </div>

                                        <div class="stats-card">
                        <h3>🎮 游戏数据</h3>
                                                    <div style="color: #ffcccb;">错误: could not find driver (Connection: lin2world, SQL: select count(*) as aggregate from [user_data])</div>
                                            </div>
                                    </div>
            </div>

            <div class="timestamp">
                最后更新时间: 2025-07-30 17:39:36
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="window.location.reload()">
        🔄 刷新
    </button>
