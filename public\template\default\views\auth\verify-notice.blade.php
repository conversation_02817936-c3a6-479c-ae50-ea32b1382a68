@extends('template::layouts.auth')
@section('title', __('Confirm Your Email'))

@section('content')
    <div class="form-group group-text">
        <p>{{ __('We have sent you a confirmation email. Please check your inbox and follow the instructions to complete your registration.') }}</p>

        @if (session('status') == 'verification-link-sent')
            <div class="alert alert-success">
                <p>{{ __('A new confirmation email has been sent to your email address.') }}</p>
                <a href="{{ url()->current() }}" class="close"></a>
            </div>
        @endif
    </div>

    <div class="form-group">
        <form method="POST" action="{{ route('verification.send') }}">
            @csrf
            <button type="submit" class="btn-green"><span>{{ __('Resend Email') }}</span></button>
        </form>
    </div>

    <div class="form-group login_button">
        <form class="logout" method="POST" action="{{ route('logout') }}">
            @csrf
            <button href="route('logout')" class="btn-primary" onclick="event.preventDefault(); this.closest('form').submit();">
                <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-transition-right"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3" /><path d="M3 18v-12a3 3 0 1 1 6 0v12a3 3 0 0 1 -6 0z" /><path d="M9 12h8" /><path d="M14 15l3 -3l-3 -3" /></svg>
                <span>{{ __('Log Out') }}</span>
            </button>
        </form>
    </div>
@endsection
