# XXTT2 项目数据库配置完整指南

## 项目数据库架构总览

这个Lineage 2游戏管理系统涉及**多个数据库**，类似于Java微服务架构中的多数据源配置。

### 数据库分类

#### 1. **Web应用主数据库** (Laravel主库)
- **数据库名**: `lin2web_cms`
- **用途**: 网站功能数据
- **连接名**: `mysql` (默认连接)
- **位置**: Docker容器内 / 生产环境远程

#### 2. **游戏服务器数据库群** (多个游戏服务器)
根据服务器类型分为两大类：

##### A. PTS服务器 (SQL Server)
- **lin2world**: 游戏世界数据库 (角色、装备、游戏数据)
- **lin2db**: 用户账户数据库 (登录账户)
- **lin2billing**: 计费系统数据库 (充值记录)

##### B. Java服务器 (MySQL)
- **loginserver**: 登录服务器数据库
- **server1**: 游戏服务器1数据库
- **server2**: 游戏服务器2数据库
- **src_server**: 源服务器 (数据合并用)
- **dist_server**: 目标服务器 (数据合并用)

#### 3. **缓存数据库**
- **Redis**: 缓存、会话、队列

---

## 生产环境数据库配置

### 当前生产环境使用的数据库：

#### 主数据库 (Web CMS)
```
服务器: 154.12.22.100:3306
数据库: lin2web_cms
用户名: l2dbuser
密码: GFaosd2312Fasd89As*
```

#### PTS游戏服务器 (SQL Server)
```
服务器: 45.251.11.54:1433
数据库: lin2world, lin2db
用户名: sa
密码: fdjhjdfkJFDJ5165JFDJjdfj!@#
```

---

## 本地开发环境配置方案

### 方案选择说明

作为Java程序员，你可以理解这类似于：
- **生产环境**: 连接远程数据库集群
- **开发环境**: 使用Docker本地数据库

### 推荐方案：Docker本地环境

#### 优势：
- ✅ 安全：不会误操作生产数据
- ✅ 独立：完全隔离的开发环境
- ✅ 快速：本地数据库响应更快
- ✅ 可重置：随时清空重建

---

## 本地数据库配置步骤

### 第一步：修改.env文件

创建本地开发环境配置：

```bash
# 复制生产环境配置作为备份
copy .env .env.production.backup

# 修改.env为本地开发配置
```

**本地开发.env配置**：
```env
APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# 本地Docker数据库配置
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

# Redis配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Docker用户配置
WWWUSER=1000
WWWGROUP=1000
```

### 第二步：启动Docker服务

```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\work\flyXxtt2

# 启动Docker服务
docker-compose up -d
```

这会启动：
- **laravel.test**: Web应用容器
- **mysql**: MySQL数据库容器
- **redis**: Redis缓存容器

### 第三步：初始化数据库

#### 方法A：使用Laravel迁移 (推荐新手)

```bash
# 在Docker容器中运行迁移
docker-compose exec laravel.test php artisan migrate

# 生成测试数据
docker-compose exec laravel.test php artisan db:seed
```

**这相当于Java中的Flyway数据库迁移**

#### 方法B：导入完整生产数据

```bash
# 导入完整的生产数据库
docker-compose exec mysql mysql -u sail -ppassword lin2web_cms < sql/lin2web_cms.sql
```

**注意**: 这会导入真实的用户数据和订单数据

### 第四步：验证数据库连接

```bash
# 测试数据库连接
docker-compose exec laravel.test php artisan tinker

# 在tinker中运行：
# DB::connection()->getPdo();
# User::count();
```

---

## 数据库表结构说明

### Web CMS主要表 (lin2web_cms)

#### 用户相关
- `users` - 网站用户账户
- `password_reset_tokens` - 密码重置令牌
- `personal_access_tokens` - API访问令牌

#### 业务功能
- `donate_orders` - 充值订单
- `warehouses` - 仓库系统
- `news` - 新闻系统
- `tickets` - 工单系统
- `messages` - 消息系统
- `referrals` - 推荐系统
- `referral_bonuses` - 推荐奖励
- `settings` - 系统设置

#### 系统表
- `failed_jobs` - 失败任务
- `migrations` - 迁移记录

### 游戏服务器表 (外部数据库)

这些表在游戏服务器数据库中，Web应用通过配置的连接访问：

#### PTS服务器表 (SQL Server)
- `user_data` - 角色数据
- `user_account` - 账户数据
- `Pledge` - 血盟数据
- `Pledge_Crest` - 血盟徽章

#### Java服务器表 (MySQL)
- `accounts` - 登录账户
- `characters` - 角色数据
- `clan_data` - 血盟数据

---

## 常用数据库操作命令

### Laravel数据库命令 (类似Java的JPA)

```bash
# 查看迁移状态
docker-compose exec laravel.test php artisan migrate:status

# 运行迁移
docker-compose exec laravel.test php artisan migrate

# 回滚迁移
docker-compose exec laravel.test php artisan migrate:rollback

# 重置数据库
docker-compose exec laravel.test php artisan migrate:fresh

# 重置并填充数据
docker-compose exec laravel.test php artisan migrate:fresh --seed
```

### 直接数据库操作

```bash
# 进入MySQL容器
docker-compose exec mysql mysql -u sail -ppassword

# 选择数据库
USE lin2web_cms;

# 查看表
SHOW TABLES;

# 查看表结构
DESCRIBE users;
```

### 数据库备份与恢复

```bash
# 备份数据库
docker-compose exec mysql mysqldump -u sail -ppassword lin2web_cms > backup.sql

# 恢复数据库
docker-compose exec mysql mysql -u sail -ppassword lin2web_cms < backup.sql
```

---

## 多数据库连接配置

### 配置文件位置
- `config/database.php` - 数据库连接配置
- `config/app.php` - 游戏服务器配置

### 在代码中使用不同数据库

```php
// 使用默认连接 (Web CMS)
$users = DB::table('users')->get();

// 使用游戏服务器连接
$characters = DB::connection('server1')->table('characters')->get();

// 在模型中指定连接
class User extends Model {
    protected $connection = 'mysql'; // Web CMS数据库
}

class Character extends Model {
    protected $connection = 'server1'; // 游戏服务器数据库
}
```

---

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查容器状态
docker-compose ps

# 查看数据库日志
docker-compose logs mysql

# 重启数据库容器
docker-compose restart mysql
```

#### 2. 迁移失败
```bash
# 检查迁移状态
docker-compose exec laravel.test php artisan migrate:status

# 手动运行单个迁移
docker-compose exec laravel.test php artisan migrate --path=/database/migrations/2024_01_25_150822_create_donate_orders_table.php
```

#### 3. 权限问题
```bash
# 设置存储目录权限
docker-compose exec laravel.test chmod -R 775 storage bootstrap/cache
```

---

## 完整启动流程

### 推荐的完整启动步骤：

#### 1. 环境准备
```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\work\flyXxtt2

# 备份当前配置
copy .env .env.production.backup
```

#### 2. 修改.env为本地开发配置
```env
APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
REDIS_HOST=redis

WWWUSER=1000
WWWGROUP=1000
```

#### 3. 启动Docker环境
```bash
# 启动所有服务
docker-compose up -d

# 等待服务启动完成 (约30秒)
docker-compose ps
```

#### 4. 安装PHP依赖 (在容器中)
```bash
# 在Docker容器中安装依赖，避免Windows扩展问题
docker-compose exec laravel.test composer install
```

#### 5. 生成应用密钥
```bash
# 生成Laravel应用密钥
docker-compose exec laravel.test php artisan key:generate
```

#### 6. 初始化数据库
```bash
# 运行数据库迁移
docker-compose exec laravel.test php artisan migrate

# 可选：生成测试数据
docker-compose exec laravel.test php artisan db:seed
```

#### 7. 启动前端开发服务器
```bash
# 在新的命令行窗口中启动
npm run dev
```

#### 8. 启动队列处理 (可选)
```bash
# 在新的命令行窗口中启动
docker-compose exec laravel.test php artisan horizon
```

#### 9. 访问应用
- **前台**: http://localhost
- **管理后台**: http://localhost/admin
- **队列监控**: http://localhost/horizon

---

## 数据库选择建议

### 对于Java程序员的建议：

#### 第一次启动 (推荐)
1. 使用**方法A (Laravel迁移)**
2. 创建干净的开发环境
3. 熟悉Laravel的数据库操作

#### 需要完整功能测试时
1. 使用**方法B (导入生产数据)**
2. 注意数据安全性
3. 定期备份本地数据库

#### 类比Java开发
- **Laravel迁移** ≈ **Flyway/Liquibase**
- **Eloquent ORM** ≈ **JPA/Hibernate**
- **多数据源配置** ≈ **Spring Boot多数据源**

---

## 下一步操作

1. **按照完整启动流程操作**
2. **验证基本功能是否正常**
3. **如遇问题，查看故障排除部分**
4. **熟悉后考虑配置游戏服务器连接**

---

*数据库配置指南 - 创建时间: 2025-07-30*
