<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Support\Facades\Auth;

class VerifyEmailController extends Controller
{
    public function __invoke(EmailVerificationRequest $request)
    {
        // Принудительно ищем пользователя в MySQL
        $user = User::on('mysql')->find($request->route('id'));

        if (!$user) {
            abort(404, __('User not found'));
        }

        if ($user->hasVerifiedEmail()) {
            return redirect()->route('login')->with('status', __('Your email has already been verified. Please log in to your account.'));
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        if (Auth::check()) {
            return redirect()->route('dashboard')->with('status', __('Your email has been successfully verified!'));
        }

        return redirect()->route('login')->with('status', __('Your email has been successfully verified! You can now log in.'));
    }

}
