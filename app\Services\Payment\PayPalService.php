<?php

namespace App\Services\Payment;

use Illuminate\Http\Request;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use App\Services\Payment\PaymentProcessingService;
use Illuminate\Support\Facades\Log;

class PayPalService
{
    protected $paymentProcessingService;
    protected $paypal;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;

        // Инициализация PayPal клиента
        $this->paypal = new PayPalClient();
        $this->paypal->setApiCredentials(config('paypal'));
        $this->paypal->getAccessToken();
    }

    public function createPayment($payment_id, $count)
    {
        try {
            $currency = config('app.pay_system.paypal_currency'); // Валюта
            $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
            // Расчет суммы платежа
            $sum = (int)ceil($count * $coin_price);

            $paymentData = [
                'intent' => 'CAPTURE',
                'application_context' => [
                    'return_url' => route('donate'),
                    'cancel_url' => route('donate'),
                ],
                'purchase_units' => [
                    [
                        'reference_id' => $payment_id,
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => $sum,
                        ],
                        'custom_id' => $payment_id,
                    ],
                ],
            ];

            $response = $this->paypal->createOrder($paymentData);

//            if (!isset($response['id']) || !isset($response['links'][1]['href'])) {
//                Log::error('PayPal: Invalid response', $response);
//                throw new \Exception('PayPal: Invalid response structure.');
//            }

            //Log::info($response);

            if (isset($response['id'])) {
                return $response['links'][1]['href']; // Возвращаем ссылку для оплаты
            }

            Log::error('PayPal: Failed to create payment', $response);
            throw new \Exception('Failed to create PayPal payment.');
        } catch (\Exception $e) {
            Log::error('PayPal: Error creating payment', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function handleWebhook(Request $request)
    {
        try {
            $webhookData = $request->all(); // Получаем данные вебхука
            //Log::info($webhookData); // Логируем данные для проверки

            // Извлекаем order ID
            $orderId = $webhookData['resource']['id'] ?? null;

            if (!$orderId) {
                throw new \Exception('Order ID not found in webhook data.');
            }

            // Захватываем платёж
            $captureResponse = $this->paypal->capturePaymentOrder($orderId);

            // Проверяем статус захвата
            if (isset($captureResponse['status']) && $captureResponse['status'] === 'COMPLETED') {
                // Извлекаем reference_id
                $referenceId = $webhookData['resource']['purchase_units'][0]['reference_id'] ?? null;

                if (!$referenceId) {
                    throw new \Exception('Reference ID not found in webhook data.');
                }

                // Платёж завершён
                //Log::info('Payment successfully captured.', ['order_id' => $orderId, 'reference_id' => $referenceId]);

                // Передаём reference_id вместо order_id
                $this->paymentProcessingService->processPayment($referenceId);

                return response()->json(['status' => 'success'], 200);
            } else {
                // Захват не удался
                Log::warning('Payment capture failed.', ['order_id' => $orderId, 'response' => $captureResponse]);
                return response()->json(['status' => 'failed'], 400);
            }
        } catch (\Exception $e) {
            Log::error('PayPal Webhook Error', ['error' => $e->getMessage()]);
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }


    // разрешенные IP для отправки для отправки вебхука
    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '************',
        ];

        return in_array($ip, $allowedIps);
    }
}
