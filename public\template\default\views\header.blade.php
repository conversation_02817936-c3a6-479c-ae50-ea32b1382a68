<header class="header">
<div class="wrap">
    <div class="top_line">
        <div class="gw-burger navigation__burger">
            <div class="gw-burger__line gw-burger__line_pos_top"></div>
            <div class="gw-burger__line gw-burger__line_pos_middle"></div>
            <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
        </div>
        <ul class="top_menu">
            <li class="nav-item {{ request()->routeIs('main-page') ? 'active' : '' }}">
                <a href="{{ route('main-page') }}" class="nav-link">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-home"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                    <span>{{ __('Home') }}</span>
                </a>
            </li>
            <li class="nav-item {{ request()->routeIs('about') ? 'active' : '' }}">
                <a href="{{ route('about') }}" class="nav-link">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-info-circle"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" /><path d="M12 9h.01" /><path d="M11 12h1v4h1" /></svg>
                    <span>{{ __('About') }}</span>
                </a>
            </li>
            <li class="nav-item {{ request()->routeIs('download') ? 'active' : '' }}">
                <a href="{{ route('download') }}" class="nav-link">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-cloud-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 18.004h-5.343c-2.572 -.004 -4.657 -2.011 -4.657 -4.487c0 -2.475 2.085 -4.482 4.657 -4.482c.393 -1.762 1.794 -3.2 3.675 -3.773c1.88 -.572 3.956 -.193 5.444 1c1.488 1.19 2.162 3.007 1.77 4.769h.99c1.38 0 2.573 .813 3.13 1.99" /><path d="M19 16v6" /><path d="M22 19l-3 3l-3 -3" /></svg>
                    <span>{{ __('Files') }}</span>
                </a>
            </li>
            <li class="nav-item {{ request()->routeIs('stats') ? 'active' : '' }}">
                <a href="{{ route('stats') }}" class="nav-link">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-stars"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M17.8 19.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z" /><path d="M6.2 19.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z" /><path d="M12 9.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z" /></svg>
                    <span>{{ __('Statistics') }}</span>
                </a>
            </li>
            <li class="nav-item {{ request()->routeIs('donate') ? 'active' : '' }}">
                <a href="{{ route('donate') }}" class="nav-link">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-moneybag"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9.5 3h5a1.5 1.5 0 0 1 1.5 1.5a3.5 3.5 0 0 1 -3.5 3.5h-1a3.5 3.5 0 0 1 -3.5 -3.5a1.5 1.5 0 0 1 1.5 -1.5" /><path d="M4 17v-1a8 8 0 1 1 16 0v1a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4" /></svg>
                    <span>{{ __('Donate') }}</span>
                </a>
            </li>
        </ul>

        <div class="top_right">
            @if(auth()->check())
                <div class="acp-panel-top">

                    <a href="{{ route('pay') }}" class="auth_user_header_balance">{{ __('Balance') }} {{ auth()->user()->balance }} {{ config('app.custom_config.coin_symbol') }}</a>

                    <div class="acp-panel-top-dropdown">
                        <div class="acp-panel-top-user dropdown-toggle">{{ __('Hello!') }} {{ Auth::user()->name }} <i class="arrow-down-acp"></i></div>
                        <div class="dropdown-menu">
                            <a href="{{ route('dashboard') }}">{{ __('Profile') }}</a>
                            <a href="{{ route('profile.edit') }}">{{ __('Change password') }}</a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                @csrf
                            </form>

                            <a href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                {{ __('Log Out') }}
                            </a>

                        </div>
                    </div>
                </div>

            @else
                <div class="top--right-menu">
                    <li class="nav-item {{ request()->routeIs('register') ? 'active' : '' }}"><a href="{{ route('register') }}" class="nav-link">{{ __('Register') }}</a></li>
                    <a href="{{ route('login') }}" class="btn-red"><span>{{ __('Sign in') }}</span></a>
                </div>
            @endif

            @include('template::lang')
        </div>
    </div>
</div>
</header>
