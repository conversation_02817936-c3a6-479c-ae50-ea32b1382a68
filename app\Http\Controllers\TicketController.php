<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        $isAdmin = str_ends_with($user->email, '@admin.com');

        $query = Ticket::with(['user', 'messages.user']);

        if (!$isAdmin) {
            $query->where('user_id', $user->id);
        }

        // Фильтр по статусу
        if ($request->has('status') && in_array($request->status, ['new', 'in_progress', 'closed'])) {
            $query->where('status', $request->status);
        }

        $tickets = $query
            ->orderByRaw("FIELD(status, 'new', 'in_progress', 'closed') ASC")
            ->orderBy('updated_at', 'desc') // Новые сверху
            ->get()
            ->map(function ($ticket) use ($isAdmin) {
                $ticket->formatted_date = $ticket->updated_at->format('d.m.Y');
                if ($isAdmin) {
                    $ticket->human_readable_date = $ticket->updated_at->diffForHumans();
                    $ticket->username = $ticket->user->name ?? 'Неизвестный';
                }
                return $ticket;
            });

        $activeTicket = null;
        if ($request->has('ticket_id')) {
            $activeTicket = Ticket::where('id', $request->ticket_id)
                ->when(!$isAdmin, function ($query) {
                    return $query->where('user_id', auth()->id());
                })
                ->with('user')
                ->firstOrFail();
        }

        return view('template::dashboard.tickets.index', [
            'tickets' => $tickets,
            'activeTicket' => $activeTicket,
            'isAdmin' => $isAdmin,
            'currentStatus' => $request->status ?? null,
        ]);
    }



    public function updateStatus(Request $request, Ticket $ticket)
    {
        $user = auth()->user();

        // Проверяем, является ли пользователь админом
        if (!str_ends_with($user->email, '@admin.com')) {
            abort(403, 'Unauthorized action.');
        }

        // Валидация статуса
        $request->validate([
            'status' => 'required|in:new,in_progress,closed',
        ]);

        // Обновляем статус тикета
        $ticket->update(['status' => $request->status]);

        return redirect()->route('tickets.index', ['ticket_id' => $ticket->id])
            ->with('success', 'Ticket status updated successfully.');
    }



    public function create()
    {
        return view('template::dashboard.tickets.create');
    }

    public function reply(Request $request, Ticket $ticket)
    {
        if ($ticket->user_id !== auth()->id() && !str_ends_with(auth()->user()->email, '@admin.com')) {
            abort(403, 'Нет доступа к этому тикету.');
        }

        $userId = auth()->id();
        $cacheKey = "user_{$userId}_last_message_time";

        // Проверяем, прошло ли 30 секунд с момента последнего действия
        if (cache()->has($cacheKey)) {
            $lastMessageTime = cache()->get($cacheKey);
            if (now()->diffInSeconds($lastMessageTime) < 30) {
                return redirect()->back()->withErrors(['message' => __('You can send messages no more than once every 30 seconds.')]);
            }
        }

        $request->validate([
            'message' => 'required|string|max:5000',
            'attachment' => 'nullable|mimes:jpg,jpeg,png,webp|max:2048',
        ]);

        $attachmentPath = null;

        if ($request->hasFile('attachment')) {
            $attachmentPath = $request->file('attachment')->store('attachments', 'public');
        }

        $ticket->messages()->create([
            'user_id' => $userId,
            'message' => $request->message,
            'attachment' => $attachmentPath,
        ]);

        $ticket->touch(); // Обновляет updated_at у тикета

        // Устанавливаем время последнего действия
        cache()->put($cacheKey, now(), 30);

        return redirect()->route('tickets.index', ['ticket_id' => $ticket->id])
            ->with('success', 'Сообщение отправлено.');
    }


    public function store(Request $request)
    {
        $userId = auth()->id();
        $cacheKey = "user_{$userId}_last_ticket_time";

        if (cache()->has($cacheKey)) {
            $lastTicketTime = cache()->get($cacheKey);
            if (now()->diffInSeconds($lastTicketTime) < 30) {
                return redirect()->back()->withErrors(['message' => __('You can create tickets no more than once every 30 seconds.')]);
            }
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'priority' => 'required|in:low,medium,high,critical',
            'message' => 'required|string|max:5000',
            'attachment' => 'nullable|mimes:jpg,jpeg,png,webp|max:2048',
        ]);

        $attachmentPath = null;

        if ($request->hasFile('attachment')) {
            $attachmentPath = $request->file('attachment')->store('attachments', 'public');
        }

        $ticket = Ticket::create([
            'title' => $request->title,
            'priority' => $request->priority,
            'user_id' => $userId,
        ]);

        $ticket->messages()->create([
            'user_id' => $userId,
            'message' => $request->message,
            'attachment' => $attachmentPath,
        ]);

        cache()->put($cacheKey, now(), 30);

        return redirect()->route('tickets.index')->with('success', 'Тикет создан.');
    }

}
