<?php
namespace App\Models;

class ModelFactory
{
    public static function l2jModel($option)
    {
        switch ($option) {
            case 'l2jscripts':
                return new L2ScriptsModel;
                break;

            case 'lucera2':
                return new Lucera2Model;
                break;

            case 'vaganth':
                return new VaganthModel;
                break;

            case 'depmax':
                return new DepmaxModel;
                break;

            case 'acis':
                return new AcisModel;
                break;

            case 'fandc':
                return new FandcModel;
                break;

            case 'smeli':
                return new SmeliModel;
                break;

            case 'l2eternity':
                return new L2EternityModel;
                break;

            case 'l2jsunrise':
                return new L2SunriseModel;
                break;

            case 'rusacis':
                return new RusAcisModel;
                break;

            default:
                throw new \InvalidArgumentException("Unknown l2jserver option: {$option}");
        }
    }
}
