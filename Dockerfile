# 基于 webdevops/php-nginx:8.3 镜像，添加 SQL Server 支持
FROM webdevops/php-nginx:8.3

# 设置工作目录
WORKDIR /var/www/html

# 安装系统依赖
USER root
RUN apt-get update && apt-get install -y \
    curl \
    apt-transport-https \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 添加 Microsoft 仓库
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list

# 安装 ODBC 驱动和 SQL Server 工具
RUN apt-get update \
    && ACCEPT_EULA=Y apt-get install -y \
        msodbcsql18 \
        unixodbc-dev \
        mssql-tools18 \
    && rm -rf /var/lib/apt/lists/*

# 安装 PHP SQL Server 扩展
RUN pecl install sqlsrv pdo_sqlsrv \
    && docker-php-ext-enable sqlsrv pdo_sqlsrv

# 添加 SQL Server 工具到 PATH
ENV PATH="$PATH:/opt/mssql-tools18/bin"

# 设置环境变量
ENV WEB_DOCUMENT_ROOT=/var/www/html/public
ENV PHP_MEMORY_LIMIT=512M
ENV PHP_MAX_EXECUTION_TIME=300

# 复制应用代码
COPY . /var/www/html

# 设置权限
RUN chown -R application:application /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# 切换到应用用户
USER application

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["supervisord"]
