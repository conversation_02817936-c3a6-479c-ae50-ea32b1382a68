# XXTT2 - Lineage 2 Web管理系统

## 项目概述

XXTT2是一个基于Laravel框架开发的Lineage 2游戏服务器Web管理系统，支持多种游戏服务器类型（Java和PTS），提供用户管理、充值系统、统计分析等功能。

## 技术栈

### 后端技术
- **PHP**: ^8.1
- **Laravel**: ^10.10
- **Filament**: ^3.2 (管理面板)
- **Laravel Horizon**: ^5.24 (队列管理)
- **Laravel Sanctum**: ^3.3 (API认证)

### 前端技术
- **Vite**: ^5.4.19 (构建工具)
- **TailwindCSS**: ^3.1.0 (CSS框架)
- **Alpine.js**: ^3.4.2 (JavaScript框架)
- **Axios**: ^1.6.4 (HTTP客户端)

### 支付集成
- **PayPal**: srmklive/paypal ^3.0
- **Stripe**: stripe/stripe-php ^16.3
- **Midtrans**: midtrans/midtrans-php ^2.6
- **FreeKassa**: 自定义集成
- **<PERSON><PERSON><PERSON> (前Enot)**: 自定义集成

## 数据库架构

### 主要数据库连接

#### 1. MySQL数据库 (主数据库)
- **用途**: Laravel应用主数据库
- **连接名**: `mysql` (默认)
- **配置**: 通过Docker Compose自动配置
- **表结构**: 
  - `users` - 用户账户
  - `donate_orders` - 充值订单
  - `warehouses` - 仓库系统
  - `news` - 新闻系统
  - `tickets` - 工单系统
  - `referrals` - 推荐系统

#### 2. Lineage 2 游戏服务器数据库

##### PTS服务器 (SQL Server)
- **lin2world**: 游戏世界数据库
  - 服务器: ************:1433
  - 数据库: lin2world
  - 用途: 角色数据、游戏统计
  
- **lin2db**: 用户账户数据库
  - 服务器: ************:1433
  - 数据库: lin2db
  - 用途: 登录账户管理

- **lin2billing**: 计费系统数据库
  - 用途: 充值和计费记录

##### Java服务器 (MySQL)
- **loginserver**: 登录服务器数据库
- **server1**: 游戏服务器1数据库
- **server2**: 游戏服务器2数据库
- **src_server**: 源服务器（用于数据合并）
- **dist_server**: 目标服务器（用于数据合并）

#### 3. Redis缓存
- **用途**: 缓存、会话存储、队列
- **配置**: 支持集群模式
- **数据库分离**:
  - DB 0: 默认缓存
  - DB 1: 专用缓存

### 数据库特性
- **多数据库支持**: 同时连接MySQL和SQL Server
- **连接池管理**: 支持多个游戏服务器连接
- **数据同步**: 支持游戏账户与Web账户同步
- **缓存优化**: Redis缓存提升性能

## 项目启动步骤

### 环境要求
- PHP >= 8.1
- Composer
- Node.js >= 16
- Docker & Docker Compose
- MySQL 8.0
- Redis (可选，用于缓存)

### 1. 克隆项目
```bash
git clone <repository-url>
cd flyXxtt2
```

### 2. 安装PHP依赖
```bash
composer install
```

### 3. 安装前端依赖
```bash
npm install
```

### 4. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 生成应用密钥
php artisan key:generate
```

### 5. 配置环境变量
编辑 `.env` 文件，配置以下关键参数：

```env
# 应用配置
APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

# Redis配置
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# 队列配置
QUEUE_CONNECTION=redis

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
```

### 6. 使用Docker启动服务
```bash
# 启动所有服务
./vendor/bin/sail up -d

# 或者使用docker-compose
docker-compose up -d
```

### 7. 数据库初始化
```bash
# 运行数据库迁移
./vendor/bin/sail artisan migrate

# 导入初始数据（可选）
./vendor/bin/sail artisan db:seed

# 导入游戏数据库结构
mysql -u root -p < sql/lin2web_cms.sql
```

### 8. 编译前端资源
```bash
# 开发模式
npm run dev

# 生产模式
npm run build
```

### 9. 配置队列处理
```bash
# 启动队列工作进程
./vendor/bin/sail artisan horizon

# 或者使用基础队列
./vendor/bin/sail artisan queue:work
```

### 10. 访问应用
- **前台**: http://localhost
- **管理后台**: http://localhost/admin
- **Horizon监控**: http://localhost/horizon

## 核心功能模块

### 1. 用户管理系统
- 用户注册/登录
- 游戏账户绑定
- 邮箱验证
- 密码加密（支持多种算法）

### 2. 充值系统
- 多支付方式集成
- 订单管理
- 自动发货
- 充值记录

### 3. 游戏数据统计
- 在线玩家统计
- 角色排行榜
- 公会/联盟统计
- 服务器状态监控

### 4. 内容管理
- 新闻发布
- 公告管理
- 工单系统
- 推荐系统

### 5. 管理后台
- Filament管理面板
- 用户管理
- 订单管理
- 系统设置

## 配置说明

### 游戏服务器配置
在 `config/app.php` 中配置：
- `l2server_type`: 服务器类型 (java/pts)
- `l2server_version`: 服务器版本
- `password_hash`: 密码加密方式

### 支付系统配置
支持的支付方式：
- FreeKassa
- Morune (前Enot)
- PayPal
- Stripe
- Midtrans
- PrimePay

### 缓存配置
- 统计数据缓存
- 用户资料缓存
- 服务器状态缓存

## 开发指南

### 目录结构
```
├── app/                    # 应用核心代码
│   ├── Console/           # 控制台命令
│   ├── Filament/          # 管理面板
│   ├── Http/              # HTTP控制器
│   ├── Models/            # 数据模型
│   └── Services/          # 业务服务
├── config/                # 配置文件
├── database/              # 数据库文件
│   ├── migrations/        # 数据库迁移
│   └── seeders/          # 数据填充
├── resources/             # 前端资源
├── routes/                # 路由定义
└── sql/                   # SQL脚本
```

### 数据库操作
```bash
# 创建迁移
php artisan make:migration create_table_name

# 运行迁移
php artisan migrate

# 回滚迁移
php artisan migrate:rollback
```

### 队列任务
```bash
# 创建任务
php artisan make:job JobName

# 处理队列
php artisan queue:work
```

## 部署说明

### 生产环境部署
1. 设置 `APP_ENV=production`
2. 关闭调试模式 `APP_DEBUG=false`
3. 配置生产数据库
4. 设置适当的文件权限
5. 配置Web服务器（Nginx/Apache）
6. 设置SSL证书
7. 配置队列监控

### 性能优化
```bash
# 缓存配置
php artisan config:cache

# 缓存路由
php artisan route:cache

# 缓存视图
php artisan view:cache

# 优化自动加载
composer install --optimize-autoloader --no-dev
```

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查数据库配置和服务状态
2. **队列不工作**: 确认Redis连接和Horizon状态
3. **支付回调失败**: 检查支付配置和回调URL
4. **游戏数据不同步**: 验证游戏服务器数据库连接

### 日志查看
```bash
# 查看应用日志
tail -f storage/logs/laravel.log

# 查看队列日志
./vendor/bin/sail artisan horizon:status
```

## 维护指南

### 定期维护任务
- 清理过期会话
- 备份数据库
- 更新统计缓存
- 监控服务器性能

### 数据备份
```bash
# 备份主数据库
mysqldump -u root -p lin2web_cms > backup.sql

# 备份游戏数据库
# 根据具体的游戏服务器类型进行备份
```

## 安全注意事项

1. **定期更新依赖包**
2. **使用HTTPS**
3. **配置防火墙**
4. **定期备份数据**
5. **监控异常访问**
6. **保护敏感配置信息**

## 详细配置说明

### .env 环境变量完整配置示例

```env
# 应用基础配置
APP_NAME=XXTT2
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

# 游戏服务器数据库配置（在config/app.php中配置）
# PTS服务器配置已在app.php中硬编码
# Java服务器需要在app.php中配置相应连接信息

# 缓存配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis配置
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# 广播配置
BROADCAST_DRIVER=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# 文件系统配置
FILESYSTEM_DISK=local

# 日志配置
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Vite配置
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
```

### Docker服务配置详解

#### docker-compose.yml 服务说明
- **laravel.test**: 主应用容器
  - 端口: 80 (HTTP), 5173 (Vite开发服务器)
  - 基于PHP 8.3运行时
  - 支持Xdebug调试

- **mysql**: MySQL 8.0数据库
  - 端口: 3306
  - 数据持久化: sail-mysql卷
  - 自动创建测试数据库

### 游戏服务器集成配置

#### PTS服务器配置 (SQL Server)
在 `config/app.php` 中已配置：
```php
'pts_servers' => [
    'lin2world' => [
        'server_name' => '经典196',
        'host' => '************',
        'port' => '1433',
        'database' => 'lin2world',
        'username' => 'sa',
        'password' => 'your-password',
    ],
],
```

#### Java服务器配置示例
需要在 `config/app.php` 中配置：
```php
'java_servers' => [
    'server1' => [
        'host' => 'your-server-host',
        'port' => '3306',
        'database' => 'l2jdb',
        'username' => 'l2j',
        'password' => 'your-password',
    ],
],
'loginserver' => [
    'host' => 'your-login-server-host',
    'port' => '3306',
    'database' => 'l2jlogin',
    'username' => 'l2j',
    'password' => 'your-password',
],
```

### 支付系统详细配置

#### FreeKassa配置
```php
'freekassa_enable' => true,
'freekassa_currency' => 'RUB',
'freekassa_project_id' => 42389,
'freekassa_secret_key' => 'your-secret-key',
'freekassa_secret_key_2' => 'your-secret-key-2',
```

#### PayPal配置
```php
'paypal_enable' => true,
'paypal_currency' => 'USD',
'paypal_mode' => 'sandbox', // 或 'live'
'paypal_sandbox_client_id' => 'your-sandbox-client-id',
'paypal_sandbox_client_secret' => 'your-sandbox-client-secret',
```

#### Stripe配置
```php
'stripe_enable' => true,
'stripe_currency' => 'USD',
'stripe_secret_key' => 'sk_test_your-secret-key',
'stripe_public_key' => 'pk_test_your-public-key',
'stripe_webhook_secret' => 'whsec_your-webhook-secret',
```

## 高级功能配置

### 推荐系统
- **启用状态**: `referral_system_enabled => true`
- **奖励比例**: `referral_bonus_percent => 10` (10%)
- **推荐码**: 自动生成唯一推荐码

### 统计系统缓存
- **统计缓存**: `stats_cached => 0` (秒，0表示不缓存)
- **用户资料缓存**: `profile_cached_time => 1` (秒)
- **服务器状态缓存**: `server_status_cached_time => 0`

### 游戏账户同步
- **游戏账户系统**: `game_account_system => false`
- **同步游戏账户**: `sync_game_accounts => false`
- **MMOWeb同步**: `sync_mmoweb_enabled => false`
- **封禁时间**: `sync_ban_time => 10` (分钟)

## 监控和日志

### Laravel Horizon队列监控
- 访问地址: `http://localhost/horizon`
- 监控队列状态、失败任务、吞吐量
- 支持队列暂停/继续操作

### 日志文件位置
- **应用日志**: `storage/logs/laravel.log`
- **队列日志**: 通过Horizon界面查看
- **Web服务器日志**: Docker容器日志

### 性能监控命令
```bash
# 查看队列状态
php artisan horizon:status

# 查看队列统计
php artisan horizon:stats

# 清理失败任务
php artisan horizon:clear

# 重启队列工作进程
php artisan horizon:terminate
```

## 数据库维护

### 定期维护脚本
```bash
#!/bin/bash
# 数据库备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"

# 备份主数据库
mysqldump -u root -p lin2web_cms > $BACKUP_DIR/lin2web_cms_$DATE.sql

# 备份游戏数据库（如果是本地MySQL）
# mysqldump -u root -p l2jdb > $BACKUP_DIR/l2jdb_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "数据库备份完成: $DATE"
```

### 数据库优化
```sql
-- 优化表
OPTIMIZE TABLE users, donate_orders, warehouses;

-- 分析表
ANALYZE TABLE users, donate_orders, warehouses;

-- 检查表
CHECK TABLE users, donate_orders, warehouses;
```

## 安全加固

### 文件权限设置
```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /path/to/project
sudo chmod -R 755 /path/to/project
sudo chmod -R 775 storage bootstrap/cache
```

### 防火墙配置
```bash
# 只允许必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3306  # MySQL (仅内网)
sudo ufw enable
```

### SSL证书配置 (Nginx)
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    root /path/to/project/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 故障排除扩展

### 常见错误及解决方案

#### 1. 数据库连接错误
```
SQLSTATE[HY000] [2002] Connection refused
```
**解决方案**:
- 检查MySQL服务状态: `docker-compose ps`
- 验证数据库配置: 检查.env文件
- 重启数据库服务: `docker-compose restart mysql`

#### 2. 队列任务失败
```
Queue worker stopped unexpectedly
```
**解决方案**:
- 检查Redis连接: `redis-cli ping`
- 重启Horizon: `php artisan horizon:terminate`
- 查看失败任务: `php artisan horizon:failed`

#### 3. 支付回调失败
```
Payment callback verification failed
```
**解决方案**:
- 检查支付配置密钥
- 验证回调URL可访问性
- 查看支付系统日志

#### 4. 游戏服务器连接失败
```
SQLSTATE[08001] SQL Server connection failed
```
**解决方案**:
- 检查SQL Server连接配置
- 验证防火墙设置
- 确认SQL Server服务状态

### 调试工具

#### Laravel Telescope (可选安装)
```bash
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

#### 数据库查询调试
```php
// 在AppServiceProvider中启用查询日志
DB::listen(function ($query) {
    Log::info($query->sql, $query->bindings);
});
```

#### 性能分析
```bash
# 安装性能分析工具
composer require barryvdh/laravel-debugbar --dev

# 查看慢查询
php artisan db:monitor --max=1000
```

## 联系支持

如需技术支持或有任何问题，请通过以下方式联系：
- 创建Issue
- 查看文档
- 联系开发团队

### 技术支持清单
在寻求支持时，请提供以下信息：
1. 错误信息和堆栈跟踪
2. 相关日志文件内容
3. 系统环境信息 (`php artisan about`)
4. 复现步骤
5. 预期行为描述

---

*最后更新: 2025-07-29*
