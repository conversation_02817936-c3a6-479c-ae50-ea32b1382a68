@extends('template::layouts.auth')
@section('title', __('Reset Password'))

@section('content')
    <div class="title">{{ __('Reset Password') }}</div>
    <form method="POST" action="{{ route('password.store') }}" class="auth-form">
        @csrf
        <input type="hidden" name="token" value="{{ $request->route('token') }}">
        <div class="form-group">
            <input id="email" class="form-control" type="email" name="email" :value="old('email', $request->email)" required autofocus placeholder="{{ __('Email') }}" />
            @foreach ($errors->get('email') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group">
            <div class="password-group">
                <input id="password" class="form-control" type="password" name="password" required placeholder="{{ __('Password') }}" />
                <span class="input-group-text password-toggle" data-target="password" data-state="hidden"></span>
            </div>
            @foreach ($errors->get('password') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group">
            <div class="password-group">
                <input id="password_confirmation" class="form-control" type="password" name="password_confirmation" placeholder="{{ __('Confirm Password') }}" required />
                <span class="input-group-text password-toggle" data-target="password_confirmation" data-state="hidden"></span>
            </div>
            @foreach ($errors->get('password_confirmation') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group login_button">
            <button type="submit" class="btn-primary">{{ __('Save') }}</button>
        </div>
    </form>
@endsection
