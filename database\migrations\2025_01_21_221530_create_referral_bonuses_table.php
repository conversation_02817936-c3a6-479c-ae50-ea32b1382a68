<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_bonuses', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('referrer_id'); // Кто пригласил
            $table->unsignedInteger('referred_id'); // Кто пополнил баланс
            $table->unsignedInteger('bonus_amount'); // Бонус, полученный реферером (целое число)
            $table->enum('status', ['Pending', 'Success'])->default('Pending');
            $table->timestamps();

            // Связи
            $table->foreign('referrer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('referred_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_bonuses');
    }
};
