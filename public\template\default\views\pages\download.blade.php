@section('title', __('Current client and patch'))
@extends('template::layouts.public')

@section('content')
    <div class="other-bg"></div>

    <section id="other">
        <div class="wrap">
            <h3 class="subtitle">
                只需点击几下就可以开始游戏
            </h3>


            <div class="startgame">
                <div class="startgame_block">
                    <div class="startgame_steps">
                        <span class="startgame_steps_num">第一步</span>
                        <h4 class="startgame_steps_name">
                            账户注册
                        </h4>
                    </div>

                    <div class="divider-small features__divider-small"></div>
                    <div class="startgame_container">
                        <p>
                            开始游戏的第一步是创建一个账号.此帐号将用于登录游戏并访问您的个人面板.
                        </p>

                        <a href="{{ route('register') }}" class="btn-red">
                            <span>创建帐号</span>
                        </a>

                        <p class="text-white select-text">
                            注册帐户后，您可以登录到您的个人面板.
                            在那里，您将能够管理您的帐户设置，购买游戏内货币，控制您的角色，并访问我们项目的其他功能.
                        </p>

                        <a href="{{ route('login') }}" class="btn-golden">
                            <span>登录面板</span>
                        </a>

                        <br>
                        <br>

                        <h3>游戏规则</h3>

                        <p class="primary_dectext">
                            请大家遵守游戏规则.
                            不要诋毁辱骂他人!
                        </p>

                        <div class="text-white underline startgame_links">
                            <a class="footer__link" href="/">协议条款</a>
                            <a class="footer__link" href="/">隐私保护</a>
                        </div>
                    </div>
                </div>

                <div class="startgame_block">
                    <div class="startgame_steps">
                        <span class="startgame_steps_num">第二步</span>
                        <h4 class="startgame_steps_name">
                            文件下载
                        </h4>
                    </div>

                    <div class="divider-small features__divider-small"></div>
                    <div class="startgame_container">
                        <p class=" select-text">
                            要连接到我们的服务器，您需要下载并安装天堂2插曲客户端文件，补丁或更新程序.
                        </p>
                        <div class="startgane_item">
                            <div class="flex items-center primary_names">
                                <h3 class="font-bold text-white">
                                    天堂II 客户端
                                </h3>
                            </div>

                            <p class="primary_dectext">
                                您需要游戏客户端。我们建议使用我们的客户端以获得最佳体验.
                            </p>

                            <div class="startgame_btns">
                                <a href="https://www.123865.com/s/Ommojv-5UvkH" class="btn-golden">
                                    <span>下载客户端</span>
                                </a>
                            </div>
                            <br>
						            	<div class="startgame_btns">
                                <a href="https://www.123684.com/s/Ommojv-5UvkH" class="btn-golden">
                                    <span>下载客户端(备用)</span>
                                </a>
                            </div>
                        </div>
                        <div class="startgane_item">

                            <p class="primary_dectext">
                                将客户端解压缩到游戏的文件夹中.
                            </p>

                            <div class="startgame_btns">
                                <a href="https://www.123865.com/s/Ommojv-qUvkH" class="btn-golden">
                                    <span>下载补丁</span>
                                </a>
                            </div>
                        </div>


                        <br><br>
                        <h3 class="font-bold text-white">
                            更新器
                        </h3>

                        <p class="primary_dectext">
                            自动文件检查和下载.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
