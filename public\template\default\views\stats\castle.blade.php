@section('title', __('Castles'))
@extends('template::layouts.dashboard')

@section('content')
    @include('template::components.nav-stats')
    <div class="lin2Table">
        <table class="rating">
            <thead>
            <tr>
                <th>#</th>
                <th>{{ __('Castle') }}</th>
                <th>{{ __('Controlled') }}</th>
                <th>{{ __('Clan leader') }}</th>
                <th>{{ __('Tax rate') }}</th>
                <th>{{ __('Treasury') }}</th>
                <th>{{ __('Siege date') }}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($castle as $cas)
                <tr>
                    <td>
                        <img src="{{ $cas->castle_img }}" height="99" alt="{{ $cas->castle_name }}">
                    </td>
                    <td>
                        <span class="castle-name">{{ $cas->castle_name }}</span>
                    </td>
                    <td>
                        <div class="clan-crests">
                            @if($cas->ally_crest)
                                <img src="{{ $cas->ally_crest }}" width="8" height="12" alt="Ally Crest">
                            @endif
                            @if($cas->clan_crest)
                                <img src="{{ $cas->clan_crest }}" width="16" height="12" alt="Clan Crest">
                            @endif
                            <span>{{ $cas->clan_name ?? 'NPC' }}</span>
                        </div>
                    </td>
                    <td>{{ $cas->leader ?? '-' }}</td>
                    <td>{{ $cas->tax_percent }}%</td>
                    <td>{{ $cas->treasury }}</td>
                    <td>{{ $cas->siege_date ?? '-' }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
@endsection
