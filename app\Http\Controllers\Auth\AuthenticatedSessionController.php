<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('template::auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        if (config('app.sync_mmoweb_enabled')) {
            $request->validate([
                'login' => 'required|string',
                'password' => 'required|string',
            ]);

            $login = $request->input('login');

            // Найти пользователя по email или имени
            if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
                $user = User::where('email', $login)->first();
            } else {
                $user = User::where('name', $login)->first();
            }

            if ($user) {
                // Если email не подтверждён — отправляем ссылку на сброс пароля
                if (is_null($user->email_verified_at)) {
                    $status = Password::sendResetLink(['email' => $user->email]);

                    if ($status === Password::RESET_LINK_SENT) {
                        return back()->with('status', __('Your account is not verified. A password reset link has been sent to your email.'));
                    }

                    return back()->withErrors(['login' => __($status)]);
                }

                // Попытка авторизации по email
                if (Auth::attempt([
                    'email' => $user->email,
                    'password' => $request->password,
                ], $request->filled('remember'))) {
                    $request->session()->regenerate();

                    return redirect()->intended(RouteServiceProvider::HOME);
                }
            }

            // Ошибка, если пользователь не найден или пароль не подошёл
            throw ValidationException::withMessages([
                'login' => __('Incorrect login or password.'),
            ]);
        }
        else {
            $request->authenticate();

            $request->session()->regenerate();

            return redirect()->intended(RouteServiceProvider::HOME);
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
