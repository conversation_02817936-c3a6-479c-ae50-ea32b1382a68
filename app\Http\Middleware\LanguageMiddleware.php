<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class LanguageMiddleware
{
    public function handle($request, Closure $next): Response
    {
        // Проверяем язык в сессии или куках
        $locale = session('locale') ?? $request->cookie('locale', config('app.locale'));

        // Устанавливаем язык
        App::setLocale($locale);

        return $next($request);
    }
}
