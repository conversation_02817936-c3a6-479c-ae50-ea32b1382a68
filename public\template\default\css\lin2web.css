@font-face {
    font-family: 'TrajanSansPro-Regular';
    src: url("../fonts/TrajanSansPro-Regular.otf") format('opentype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'TrajanSansPro-Semibold';
    src: url("../fonts/TrajanSansPro-Semibold.otf") format('opentype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Montserrat-Regular';
    src: url("../fonts/Montserrat-Regular.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}
@font-face {
    font-family: 'Montserrat-SemiBold';
    src: url("../fonts/Montserrat-SemiBold.ttf") format("truetype");
    font-style: normal;
    font-weight: normal;
}


/* ===== 🎨 Глобальные переменные ===== */
:root {
    --color-primary: #ebca69;
    --color-secondary: #e1ce87;
    --color-background: #f5f7fa;
    --color-dark: #1e1e1e;
    --color-light: #ffffff;
    --color-accent: #ff6b6b;
    --accent-bg-color: rgb(149 111 0 / 90%);
    --color-gray: gray;
    --bgc-color: rgb(48 44 76 / 90%);
    --bg-form-head: rgb(209 163 0 / 40%);
    --bg-form-table: rgb(48 46 37 / 90%);
    --bg-dark: #20232A;

    --font-accent-regular: TrajanSansPro-Regular;
    --font-accent-bold: TrajanSansPro-Semibold;
    --font-regular: Montserrat-Regular;
    --font-bold: Montserrat-SemiBold;


    /* размеры */
    --sidebar-width: 250px;
    --container-padding: 30px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
* {
    outline: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html, body {
    height: 100%;
}
body {
    font-family: var(--font-regular), sans-serif;
}

ul, li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
p {
    margin: 0;
    color: #bebebe;
}
h3 {
    color: #fff;
}
a {
    color: #fff;
    text-decoration: none;
}
small {
    color: #bebebe;
}

/*FORM START*/
select.form-control {
    /* Убираем нативный appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    /* Фон, граница и скругления */
    background: #292D34;
    border: 1px solid rgb(255 255 255 / 10%);
    border-radius: 12px;

    /* Внутренние отступы (правый — для стрелки) */
    padding: 10px 36px 10px 12px;

    /* Шрифт и цвет */
    font-size: 14px;
    line-height: 1.4;
    color: #fff;

    /* Курсор */
    cursor: pointer;

    /* Переходы для фокуса */
    transition: border-color 0.2s, box-shadow 0.2s;

    /* Кастомная стрелка через встроенное SVG */
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='10' height='6' viewBox='0 0 10 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0l5 6 5-6z' fill='%23999'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
}
/* Стиль при фокусе */
select.form-control:focus {
    outline: none;
    border-color: #6ca0f6;
    box-shadow: 0 0 0 3px rgba(108, 160, 246, 0.3);
}
/* (Опционально) чуть более читаемые опции */
select.form-control option {
    padding: 8px;
    background-color: #292D34;
    color: #fff;
}
:focus-visible {
    outline: -webkit-focus-ring-color auto 0;
}
.form-control {
    font-family: var(--font-regular), sans-serif;
    box-sizing: border-box;
    background: #292D34;
    border: 1px solid rgb(255 255 255 / 10%);
    padding: 14px 20px;
    border-radius: 12px;
    width: 100%;
    color: #fff;
}
.form-control::placeholder {
    font-family: var(--font-regular), sans-serif;
    font-size: 14px;
}
.form-group {
    margin-bottom: 30px;
}
.form-group:last-child {
    margin-bottom: 0;
}
.form-group label {
    color: #bebebe;
    font-size: 14px;
    display: flex;
    margin-bottom: 5px;
}
::-webkit-input-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
::-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-moz-placeholder {
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:-ms-input-placeholder {
    color: #7b7873;
    -webkit-transition: opacity .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
:focus::-webkit-input-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus::-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-moz-placeholder {
    opacity: 0;
    transform: translateX(20px);
}
:focus:-ms-input-placeholder {
    opacity: 0;
    -ms-transform: translateX(20px);
    transform: translateX(20px);
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/*FORM END*/

h1 {
    color: #bebebe;
}
#auth-form {
    background-color: var(--bg-form-table);
    padding: 20px;
    border-radius: 12px;
}

.main-bg {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: -10;
    background-image: url("../img/lin2web/bg.jpg");
    background-size: cover;
    background-position: 50%;
    background-repeat: no-repeat;
}
aside.sidebar {
    position: fixed;
    z-index: 20;
    width: 270px;
    height: 100vh;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    background-color: rgba(22, 15, 2, .5);
    border-right: .25rem solid #bbac8b;
}
.main-content {
    background-image: url("../img/lin2web/bg.jpg");
    background-position: center 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    margin-left: auto;
    width: calc(100% - 255px);
    padding: 40px;
    height: 100%;
    min-height: 100vh;
    display: block;
    overflow: auto;
}
.main-wrap {
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;
    height: 100%;
    background-size: cover;
    background-position: center;
}
.auth_logo img {
    width: 100%;
    max-width: 160px;
}
.auth_block_top {
    padding-left: 30px;
    padding-right: 20px;
    padding-top: 30px;
}
.auth_header_top {
    margin-top: 30px;
    margin-bottom: 30px;
}
.auth_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

form.logout button {
    font-family: var(--font-bold), sans-serif;
    width: 100%;
    border: 0;
    background: transparent;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    box-sizing: border-box;
    color: #ff8282;
    font-size: 14px;
    text-transform: uppercase;
    line-height: 1;
    z-index: 0;
    flex-grow: 1;
    max-height: 10rem;
    cursor: pointer;
    padding: 20px 0;
    padding-left: 30px;
    font-stretch: normal;
}
form.logout button:before {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    background-color: rgb(255 54 54 / 10%);
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
    opacity: 0;
    transform: scaleY(.6);
    transform-origin: right center;
    pointer-events: none;
}
form.logout button:hover:before {
    opacity: 1;
    transform: scaleY(1);
}

.sidebar-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.sidebar-menu {
    display: flex;
    flex-direction: column;
}
.sidebar-menu.bottom {
    padding-bottom: 30px;
}
.sidebar-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    box-sizing: border-box;
    color: #e2dccd;
    font-family: var(--font-bold), sans-serif;
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;
    line-height: 1;
    z-index: 0;
    flex-grow: 1;
    max-height: 10rem;
    cursor: pointer;
    padding: 20px 0;
    padding-left: 30px;
}
.sidebar-menu a:hover:before {
    opacity: 1;
    transform: scaleY(1);
}
.sidebar-menu a.active:before {
    opacity: 1;
    transform: scaleY(1);
}
.sidebar-menu a:before {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    background-color: transparent;
    background-image: url("../img/bgdecor.png");
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
    opacity: 0;
    transform: scaleY(.6);
    transform-origin: right center;
    pointer-events: none;
}
.sidebar-menu a.active:after {
    opacity: 1;
    transform: scaleX(1);
}
.sidebar-menu a:after {
    display: block;
    content: "";
    background-color: transparent;
    background-image: url("../img/arrow_1.png");
    background-size: 2.25rem auto;
    background-position: 50%;
    background-repeat: no-repeat;
    width: 2.25rem;
    height: auto;
    position: absolute;
    right: -.8125rem;
    top: 0;
    bottom: 0;
    transition: all .2s;
    opacity: 0;
    transform: scaleX(0);
    filter: grayscale(0) drop-shadow(0 .25rem .75rem rgba(0, 0, 0, .46)) drop-shadow(0 0 .625rem rgba(255, 213, 44, .7));
    pointer-events: none;
}



.auth_header_title {
    display: flex;
    align-items: center;
    gap: 5px;
}
.auth_header_title svg {
    width: 28px;
    color: #fff;
}
.auth_header_title p {
    font-family: var(--font-accent-regular), sans-serif;
    font-size: 18px;
    color: var(--color-primary);
}
.auth_nav_top {
    background-color: rgb(22 15 2 / 60%);
    padding: 10px 20px;
    border-radius: 12px;
}
.auth_nav_top ul {
    display: flex;
    gap: 40px;
}
.auth_top_r {
    display: flex;
    align-items: center;
    gap: 30px;
}
.auth_nav_top a {
    font-family: var(--font-accent-regular), sans-serif;
    display: flex;
    align-items: center;
    gap: 7px;
    font-size: 18px;
}
.auth_nav_top a:hover {
    color: var(--color-primary);
}
.auth_nav_top svg {
    width: 28px;
}


.btn-accent {
    border: 0;
    cursor: pointer;
    background-color: #6400ff;
    height: 42px;
    min-width: 180px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
}
.btn-accent:hover {
    box-shadow: 0 0 8px #6400ff;
}
.btn-accent svg {
    width: 24px;
}
.btn-green {
    border: 0;
    cursor: pointer;
    background-color: #00d084;
    height: 42px;
    min-width: 180px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
    color: #fff;
    font-size: 16px;
}
.btn-green:hover {
    box-shadow: 0 0 8px #00d084;
}
.btn-green svg {
    width: 24px;
}
.btn-primary {
    font-family: var(--font-accent-regular), sans-serif;
    border: 0;
    cursor: pointer;
    background-color: #6c5eff;
    height: 42px;
    min-width: 180px;
    max-width: max-content;
    padding: 0 15px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    gap: 8px;
    color: #fff;
    font-size: 14px;
}
.btn-primary:hover {
    box-shadow: 0 0 8px #6c5eff;
}
.btn-primary svg {
    width: 24px;
}

.master-account-table {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/*LANGUAGE*/
.nav__langs {
    position: relative;
    z-index: 1;
}
.nav__langs-item {
    cursor: pointer;
    height: 42px;
    background-color: var(--bgc-color);
    padding: 0 5px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    transition: color 0.3s ease-in-out;
    gap: 10px;
}

.nav__langs-item:hover, .nav__langs-item.active {
    color: #fff;
}
.nav__langs-item img {
    width: 22px;
    aspect-ratio: 34/24;
    border-radius: 2px;
}
.nav__langs-item--current:after {
    content: "";
    width: 12px;
    aspect-ratio: 12 / 6;
    background: url("../img/lin2web/arrow.png");
    background-size: 100% 100%;
    transition: transform 0.3s ease-in-out;
}

.nav__langs-item.active:after {
    transform: scale(1, -1);
}
.nav__langs-stroke {
    list-style-type: none;
    position: absolute;
    z-index: 1;
    top: calc(100% + 3px);
    right: 0;
    display: none;
}
.nav__langs-stroke li:not(:first-child) {
    margin-top: 10px;
}
.nav__links {
    list-style-type: none;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
    column-gap: 30px;
    margin-left: auto;
    position: relative;
    z-index: 2;
}
.nav__links-item {
    font-family: var(--primary-font), sans-serif;
    font-weight: 400;
    font-size: 15px;
    line-height: 1em;
    color: #fff;
    text-transform: uppercase;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    column-gap: 6px;
    transition: color 0.3s ease-in-out;
}
.nav__links-item:hover {
    color: var(--brand-color);
}
.nav__links-item img {
    height: 16px;
}
.nav__switch {
    width: 34px;
    height: 34px;
    position: relative;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.15);
    display: none;
    transition: background-color 0.3s ease-in-out, border 0.3s ease-in-out;
    z-index: 3;
    border-radius: 50%;
    margin-left: auto;
}
.nav__switch:hover {
    background-color: var(--brand-color);
}
.nav__switch:hover:after, .nav__switch:hover:before, .nav__switch:hover .nav__switch-item {
    background: #1c2227;
}
.nav__switch:hover .nav__switch-item {
    transform: scale(0.7, 1);
}
.nav__switch.active:after {
    transform: rotate(-135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active:before {
    transform: rotate(135deg) scale(0.7, 1);
    top: calc((100% - 2px) / 2);
}
.nav__switch.active .nav__switch-item {
    transform: scale(0, 1);
}
.nav__switch:after {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: 11px;
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch:before {
    content: "";
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    transition: all 0.3s ease-in-out;
    top: calc(100% - 13px);
    left: 6px;
    transform: scale(0.7, 1);
}
.nav__switch-item {
    width: calc(100% - 12px);
    height: 2px;
    background: #cdcfd1;
    position: absolute;
    top: calc((100% - 2px) / 2);
    left: 6px;
    transition: all 0.3s ease-in-out;
    transform: scale(0.7, 1);
}
/*LANG END*/

.auth-main-link {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    background-color: var(--accent-bg-color);
    border-radius: 12px;
    padding: 30px 0;
    margin-bottom: 30px;
}

.auth-main-top-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
}
.auth-main-top-item.left {

}
.auth-main-top-item.left::before {
    content: "";
    position: absolute;
    right: -1%;
    height: 100%;
    width: 1px;
    background: rgb(255 255 255 / 20%);
}
.auth_donate_coins {
    display: flex;
    align-items: center;
    gap: 15px;
}
.auth_donate_links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-end;
}

.alert-success {
    position: relative;
    display: flex;
    align-items: center;
    margin: 32px 0;
    border-radius: 16px;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
    justify-content: space-between;
    background: #2A7B9B;
    background: linear-gradient(90deg, rgb(10 97 0) 15%, rgb(21 56 0) 100%);
}
.alert-success p {
    position: relative;
    padding-left: 60px;
    font-size: 18px;
    color: #dbff6a;
}
.alert-success p::before {
    content: "";
    position: absolute;
    left: 0;
    background-image: url("../img/lin2web/icon-success.svg");
    width: 40px;
    height: 40px;
    background-position: center center;
    background-size: cover;
    top: -40%;
    bottom: -50%;
}
.alert-danger {
    position: relative;
    display: flex;
    align-items: center;
    margin: 32px 0;
    border-radius: 16px;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
    justify-content: space-between;
    background: gray;
    background: linear-gradient(90deg, rgb(255 87 87) 15%, rgb(255 32 32) 100%);
}
.alert-danger p {
    position: relative;
    padding-left: 60px;
    font-size: 18px;
    color: #ffb8b8;
}
.alert-danger p::before {
    content: "";
    position: absolute;
    left: 0;
    background-image: url("../img/lin2web/icon-danger.svg");
    width: 40px;
    height: 40px;
    background-position: center center;
    background-size: cover;
    top: -40%;
    bottom: -50%;
}





.auth-content {
    margin-top: 40px;
}
.refresh_link {
    width: 64px;
    height: 64px;
    border: 1px solid #bfbfbf;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.refresh_link:hover {
    border: 1px solid #fff;
}
.auth_donation_coins {
    color: #fff;
    font-size: 16px;
}
.auth_donate_title {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 32px;
    color: #fff;
}
.auth_donate_title img {
    height: 22px;
}

.btn-gray-no-border {
    cursor: pointer;
    background: transparent;
    border: 1px solid #fff;
    padding: 6px 15px;
    border-radius: 8px;
    font-size: 14px;
    color: #fff;
}
.btn-gray-no-border:hover {
    border: 1px solid #bebebe;
    padding: 6px 15px;
    border-radius: 8px;
    font-size: 14px;
}

.omg-table {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-form-table);
    color: #fff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    box-shadow: 0 0 12px rgba(0,0,0,0.5);
}

.omg-table th, .omg-table td {
    font-family: var(--font-regular), sans-serif;
    font-size: 14px;
    padding: 8px 10px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.omg-table thead {
    background: var(--bg-form-head);
}

.omg-table tbody tr:hover {
    background-color: rgb(127 110 35 / 90%);
    transition: 0.3s ease;
}

.pk-count {
    color: #ff4d4d;
    font-weight: bold;
}


/* Затемнённый фон */
.popup-overlay {
    display: none;
    position: fixed;
    z-index: 9999;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(2px);
    justify-content: center;
    align-items: center;
}

/* Контейнер попапа */
.popup-content {
    background: #1f1f2e;
    color: #fff;
    padding: 30px;
    border-radius: 12px;
    width: 100%;
    max-width: 620px;
    box-shadow: 0 0 25px rgba(0,0,0,0.7);
    position: relative;
    animation: fadeInUp 0.4s ease;
}
.popup-content .text-success {
    text-align: center;
    font-size: 18px;
}
.popup-title {
    font-size: 22px;
}

.popup-title-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-content form {
    margin-top: 30px;
}

/* Закрыть */
.popup-close {
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s ease;
    background-color: rgb(255 255 255 / 10%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
}
.popup-close:hover {
    color: #fff;
}
.popup-close svg {
    width: 18px;
}

/* Группы полей */


/* Ошибки */
.text-danger {
    color: #ff4d4d;
    font-size: 13px;
    margin-top: 5px;
}

/* Успешно */
.text-success {
    color: #00cc66;
    font-size: 14px;
    margin-top: 10px;
}

/* Показать/Скрыть пароль */
.password-group {
    display: flex;
    align-items: center;
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    cursor: pointer;
    font-size: 16px;
    user-select: none;
    color: #aaa;
}

.password-toggle:hover {
    color: #fff;
}

.login_button button {
    width: 100%;
    max-width: 100%;
    box-shadow: 0 4px 70px rgba(112, 96, 255, 0.5);
    height: 50px;
    font-size: 18px;
    position: relative;
}
.login_button svg {
    position: absolute;
    left: 18px;
    width: 30px;
}

/* Анимация */
@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0px);
        opacity: 1;
    }
}

.change-game-password-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(22, 15, 2, .5);
    padding: 20px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}


.create_game_account {
    background-color: var(--bgc-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
}
.create_game_account p {
    font-family: var(--font-accent-regular), sans-serif;
    font-size: 20px;
    color: #fff;
}


.master-name-plus {
    display: flex;
    color: #fff;
    font-size: 20px;
}
.master-name-plus p {
    color: #fff;
}

/* плюсик*/
.plus-toggle {
    width: 15px;
    height: 15px;
    cursor: pointer;
}
.plus-toggle::before {
    content: '+';
    transition: transform 0.3s ease;
}

.plus-toggle.active::before {
    content: '-';
    transform: rotate(180deg);
}

.master-acc-item .lin2Table {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease;
}

.lin2Table.active {
    max-height: 1000px;
}
/* плюсик*/

.tooltip_title {
    display: flex;
    align-items: center;
    gap: 2px;
}
.tooltip_title svg {
    width: 16px;
}
.info-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
    margin-left: 5px;
}
.info-tooltip .tooltip-text {
    visibility: hidden;
    opacity: 0;
    width: max-content;
    background-color: var(--bgc-color);
    color: #fff;
    text-align: left;
    border-radius: 4px;
    padding: 6px 10px;
    position: absolute;
    z-index: 10;
    top: 125%; /* немного ниже */
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    transition: opacity 0.3s ease;
    font-size: 12px;
}
.info-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}



.close {
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s ease;
    background-color: rgb(255 255 255 / 10%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
}
.close::before {
    content: "";
    position: absolute;
    background-image: url("../img/lin2web/close.svg");
    width: 18px;
    height: 18px;
}
.close:hover {
    background-color: rgb(255 255 255 / 20%);
}

.card {
    background-color: var(--bg-dark);
    padding: 30px;
    border-radius: 12px;
}
.send-to-game {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
}
.send-to-game .form-group {
    margin-bottom: 0;
    width: 100%;
}
.send-to-game .form-control {
    height: 50px;
}
#send-to-game .alert {
    margin-bottom: 0;
}
.title {
    font-size: 18px;
    margin-bottom: 20px;
    color: #bebebe;
}
.title h1 {
    font-family: var(--font-accent-regular), sans-serif;
}
#warehouse {
    margin-top: 20px;
}
#sendItemsForm {
    color: #bebebe;
}
.warehouse-items-all {
    margin-top: 30px;
}
.warehouse-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 8px 0;
}



.ticket-section {
    border-radius: 12px;
    background-color: var(--bgc-color);
    padding: 20px;
}
.ticket-block {
    min-height: 720px;
    max-height: 720px;
    display: flex;
    justify-content: space-between;
}
.ticket-sidebar {
    width: 20%;
}
.ticket-messages {
    padding: 0 30px;
    position: relative;
    width: 80%;
    display: flex;
    flex-direction: column;
    overflow: auto;
}
.ticket-item.active {
    background-color: rgb(98 0 248 / 30%);
}

.ticket_title_status {
    display: flex;
    justify-content: space-between;
}
.tickets-nav {
    margin-top: 20px;
    background-color: rgb(255 255 255 / 10%);
}
.ticket-no-active {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #bebebe;
    font-size: 18px;
}
.ticket-sidebar .btn-primary {
    max-width: 100%;
    min-width: 100%;
}
.ticket-link {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-bottom: 1px solid gray;
}
.ticket-top-block h2 {
    color: #fff;
}
.ticket-top-block p {
    color: #bebebe;
}
.message-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 15px;
}
.message-container.right {
    justify-content: flex-end;
}
.message p {
    color: #e8e8e8;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    font-size: 14px;
    line-height: 1.6;
}
.ticket_admin_status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 550px;
    color: #fff;
    gap: 10px;
    margin: 20px 0;
}
.status_text {
    font-size: 12px;
}
.status_text.status-closed {
    color: #ff6060;
}
.status_text.status-in-progress {
    color: #17ff00;
}
.ticket_date_username {
    font-size: 12px;
}
.ticket-title {
    font-size: 14px;
}
.ticket-filter {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
}
.ticket-filter a {
    border: 1px solid palegoldenrod;
    font-size: 14px;
    padding: 3px 8px;
    border-radius: 6px;
}
.ticket_msg_date {
    color: #bebebe;
    font-size: 12px;
}
#fileToUpload {
    display: none;
}
.file_upload svg {
    cursor: pointer;
}
.file_upload {
    display: flex;
    align-items: center;
    justify-content: center;
}
.area-for-response-text {
    min-height: 20px;
    vertical-align: middle;
    word-wrap: break-word;
    font-size: 16px;
    line-height: 20px;
    color: #060614f5;
    width: 100%;
    border: none;
    resize: none;
    overflow: hidden;
    height: inherit;
    max-height: none;
}

.status-new {
    background-color: green;
    font-size: 12px;
    display: flex;
    height: 100%;
    padding: 2px;
    border-radius: 2px;
}

.ticket-reply {
    width: 100%;
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 20px;
}
.ticket-reply .form_group {
    position: relative;
    width: 100%;
    max-height: 120px;
    box-sizing: initial;
    padding: 0 24px 4px 16px;
    margin-top: 6px;
    margin-right: 16px;
}
.ticket-reply .btn-green {
    max-width: max-content;
    min-width: max-content;
    padding: 0 30px;
    color: #fff;
}

#preview-container {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    position: relative;
}
#preview-container p {
    color: gray;
}
.preview-wrapper {
    position: relative;
    display: inline-block;
}

#preview-image {
    max-width: 80px;
    max-height: 80px;
    border-radius: 5px;
}

.remove-file {
    position: absolute;
    top: -8px;
    right: -8px;
    background: red;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    border: none;
}
.ticket_is_admin {
    color: #ff8e8e;
}
.omg-table.error tr {
    background-color: rgb(127 110 35 / 90%);
}
.s-pack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    flex-wrap: wrap;
    gap: 20px;
}
.s-pack-item {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    padding: 15px;
    background-color: var(--bg-form-table);
}
.s-pack-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}
.s-pack-title img {
    height: 54px;
}
.s-pack-title span {
    color: #fff;
    font-size: 15px;
    margin-top: 10px;
}
.s-item-i {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #bebebe;
}
.s-item-count {
    font-size: 12px;
    color: #ffacac;
}
.s-pack-prices {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    margin-top: 30px;
    margin-top: auto;
}
.s-pack-prices button {
    min-width: 100%;
    margin-top: 15px;
}
.s-pack-price {
    font-size: 20px;
    color: var(--color-primary);
    text-decoration: line-through;
}
.s-pack-discount {
    color: #bebebe;
}
.s-item-title {
    font-size: 14px;
}

/*PAYMENT START*/
#payment-form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
}
.payment__range.noUi-horizontal .noUi-handle {
    width: 26px;
    height: 26px;
    border-radius: 26px;
    background: var(--color-primary);
    border: none;
    box-shadow: none;
    top: -9px;
    right: -13px;
}
.payment__range.noUi-target {
    background: #1c1718;
    border-radius: 5px;
    border: 1px solid #505050;
    box-shadow: none;
}
.payment__range {
    margin-top: 20px;
}
.payment__range .noUi-value {
    font-size: 10px;
    line-height: 18px;
    font-weight: 400;
    color: #afafaf;
}
.payment__range .noUi-marker-horizontal.noUi-marker-large {
    height: 10px;
}
.payment__range .noUi-marker-horizontal.noUi-marker {
    width: 1px;
}
.payment__range .noUi-marker-large {
    background: #686363;
}
.payment__range .noUi-marker {
    background: #483f41;
}
.payment__range .noUi-pips-horizontal {
    height: 42px;
}
.payment__range.noUi-horizontal .noUi-connect {
    background: #ffbd01;
}
.payment__range .noUi-tooltip {
    border: 1px solid #505050;
    border-radius: 5px;
    background: #1c1718;
    color: #fff;
    font-size: 14px;
    line-height: 1em;
    font-weight: 400;
    padding: 6px 5px 5px;
}
.payment__range.noUi-horizontal {
    height: 10px;
    margin-bottom: 60px;
}
.noUi-handle-lower::before, .noUi-handle-lower::after {
    display: none;
}
.pay_method img {
    display: flex;
    height: 40px;
}
.payment_select {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}
.pay_method {
    border: 2px solid transparent;
    cursor: pointer;
}
.pay_method:hover {
    opacity: 0.7;
    transition: 0.3s;
}
.pay_method.active {
    border: 2px solid var(--color-primary);
    border-radius: 6px;
}
.pay-currency {
    display: flex;
    color: #bebebe;
    gap: 30px;
}
.payment__input {
    display: flex;
    align-items: center;
}
.payment_bonus_price {
    background-color: var(--color-primary);
    height: 45px;
    min-width: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.payment-pay input {
    border-radius: 0;
}
.bonus-item {
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    border-bottom: 1px solid gray;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
.bonus-item p {
    font-family: var(--font-accent-regular), sans-serif;
    color: #fff;
}
.bonus-item span {
    color: var(--color-primary);
}
.donate-title {
    font-family: var(--font-accent-regular), sans-serif;
    font-size: 22px;
    color: #fff;
}
.donate-title-block p {
    color: #bebebe;
}
.donate-title-block {
    margin-bottom: 15px;
}
.curr-item .curr_symbol {
    color: var(--color-primary);
}
.payment-pay .form-group {
    margin-bottom: 15px;
}
.payment-pay .form-group:last-child {
    margin-bottom: 0;
}
.payment-pay .login_button {
    margin-top: 30px;
    text-align: center;
}
.total_text {
    font-family: var(--font-accent-regular), sans-serif;
    text-align: center;
    margin-top: 15px;
    color: #bebebe;
}
/*PAYMENT END*/


.referral_block {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}
.referral_block .btn-green {
    width: max-content;
    height: 45px;
}
.referral_copy_link {
    position: relative;
}
.referral_copy_link button {
    height: 45px;
}
.copy-message {
    position: absolute;
    opacity: 0;
    font-size: 12px;
    color: #bebebe;
}
.referral_form {
    max-width: 470px;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 20px;
}
.referral_form input {
    border-radius: 8px;
    width: 70%;
}
.referrals_text {
    line-height: 1.6;
}
table.rating.index td:first-child {
    text-align: left;
}
table.rating.index td:last-child {
    text-align: right;
}

table.rating {
    width: 100%;
    border-collapse: collapse;
    background: #1e1e1e;
    color: #d9c97f;
    box-shadow: 0 0 10px #000;
}

table.rating thead th {
    background-color: #2c2c2c;
    font-size: 16px;
    text-align: center;
    padding: 10px;
    border-bottom: 2px solid #d9c97f;
    font-family: var(--font-accent-regular), sans-serif;
}

table.rating th {
    background-color: #2c2c2c;
    font-size: 16px;
    padding: 10px;
    border-bottom: 2px solid #d9c97f;
    text-align: center;
}

table.rating td {
    padding: 10px;
    border-bottom: 1px solid #444;
    text-align: center;
    font-size: 13px;
}

table.rating tr:hover {
    background-color: #2a2a2a;
}

table.rating tr:hover {
    background-color: #2f2f2f;
}

table.rating td:last-child {
    font-family: var(--font-bold), sans-serif;
    color: #ffd700;
}
.bar-container {
    height: 18px;
    background-color: #333;
    border: 1px solid #555;
    width: 100%;
    border-radius: 3px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(to right, #ffc107, #ff9800);
    transition: width 0.4s ease;
}

.bar-fill.female {
    background: linear-gradient(to right, #d48fff, #a24cff);
}

.stats-menu {
    display: flex;
    justify-content: space-between;
}
.stats-menu a {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #1e1e1e;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}
.stats-menu .active a {
    background-color: #6c5eff;
}

.char-name {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.clan-crests img {
    display: inline-block;
    vertical-align: middle;
}



.server-switcher {
    position: relative;
    display: inline-block;
    z-index: 10;
    margin: 20px 0;
}

.server-switcher__current {
    background: linear-gradient(to right, #2c2c2c, #1a1a1a);
    color: #d9c97f;
    padding: 10px 15px;
    border: 1px solid #444;
    border-radius: 5px;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background 0.3s;
}

.server-switcher__current:hover {
    background: #3a3a3a;
}

.server-switcher__current .arrow-down {
    border: solid #d9c97f;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 5px;
    transform: rotate(45deg);
    transition: transform 0.3s ease;
}

.server-switcher__list {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    background: #1e1e1e;
    border: 1px solid #444;
    border-radius: 5px;
    padding: 5px 0;
    list-style: none;
    min-width: 180px;
    box-shadow: 0 0 10px #000;
}

.server-switcher__list li {
    text-align: left;
}

.server-switcher__item {
    display: block;
    padding: 10px 15px;
    color: #d9c97f;
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
}

.server-switcher__item:hover {
    background: #2a2a2a;
    color: #ffd700;
}

.gw-burger {
    display: none;
}


@media only screen and (max-width: 960px) {
    aside.sidebar.top_menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 300px;
        height: 100%;
        background-color: #221e1d;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
    }

    aside.sidebar.top_menu.active {
        transform: translateX(0);
    }

    .navigation__burger {
        display: block;
    }

    .gw-burger {
        background-color: var(--bg-form-table);
        position: relative;
        z-index: 999999;
        width: 40px;
        height: 40px;
        cursor: pointer;
        box-sizing: border-box;
    }

    .gw-burger__line {
        width: 20px;
        height: 2px;
        margin: auto;
        position: absolute;
        left: 5px;
        right: 5px;
        background-color: #909090;
        border-radius: 2px;
        -webkit-transition: all .3s;
        transition: all .3s;
    }

    .gw-burger__line_pos_top {
        pointer-events: none;
        top: 32%;
    }

    .gw-burger__line_pos_middle {
        pointer-events: none;
        top: 50%;
        transform: translateY(-50%);
    }

    .gw-burger__line_pos_bottom {
        pointer-events: none;
        bottom: 32%;
    }

    .gw-burger_active .gw-burger__line_pos_top {
        transform: rotate(45deg) translateY(-50%);
        -webkit-transform-origin: center top;
        transform-origin: center top;
        top: 50%;
    }

    .gw-burger_active .gw-burger__line_pos_middle {
        transform: rotate(180deg);
        opacity: 0;
    }

    .gw-burger_active .gw-burger__line_pos_bottom {
        transform: rotate(-45deg) translateY(50%);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        bottom: 50%;
    }

    .main-content {
        width: 100%;
        padding-top: 0;
    }
    .auth_top {
        justify-content: center;
        gap: 15px;
    }
    .auth-main-link {
        padding: 15px 0;
    }
    .auth-main-top-item {
        flex-wrap: wrap;
        gap: 15px;
    }
    .stats-menu a {
        padding: 10px 15px;
        gap: 5px;
    }
}

@media only screen and (max-width : 675px) {
    .auth_nav_top {
        display: none;
    }
    .auth-main-link {
        grid-template-columns: repeat(1, 1fr);
        gap: 30px;
    }
    .auth-main-top-item.right {
        display: none;
    }
    .s-pack {
        grid-template-columns: repeat(1, 1fr);
    }
    #payment-form {
        grid-template-columns: repeat(1, 1fr);
        gap: 30px;
    }
    .auth-content {
        padding-bottom: 40px;
    }
    .card {
        padding: 15px;
    }
    .bonus-item {
        font-size: 16px;
    }
    .referral_form {
        max-width: 100%;
        flex-wrap: wrap;
        gap: 15px;
    }
    .referral_form input {
        width: 100%;
    }
    .referral_block {
        flex-wrap: wrap;
        gap: 15px;
    }
    .referral_copy_link {
        width: 100%;
    }
    .referral_copy_link button {
        width: 100%;
        max-width: 100%;
    }
    .referral_block button {
        min-width: 100%;
    }
    .stats-menu {
        flex-wrap: wrap;
    }
    .ticket-sidebar {
        width: 100%;
    }
    .ticket-block {
        max-width: 100%;
        min-width: 100%;
        flex-wrap: wrap;
    }
    .ticket-messages {
        width: 100%;
        padding: 0 15px;
    }
    .ticket-section {
        padding: 15px;
    }
    .title h1 {
        font-size: 22px;
    }
    .send-to-game {
        flex-wrap: wrap;
    }
    .create_game_account p {
        display: none;
    }
    .create_game_account button {
        min-width: 100%;
    }
    .popup-content {
        margin-left: 15px;
        margin-right: 15px;
    }
    .lin2Table {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch; /* плавный скролл на iOS */
    }
    .omg-table {
        width: max-content;
    }
    table.rating thead th, table.rating td {
        font-size: 12px;
    }
    aside.sidebar.top_menu {
        width: 80%;
    }
    .sidebar-left {
        overflow-x: hidden;
        overflow-y: auto;
    }
    .auth_header_top {
        margin-top: 15px;
        margin-bottom: 15px;
    }
}

