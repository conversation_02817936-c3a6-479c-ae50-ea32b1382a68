@extends('template::layouts.dashboard')

@section('content')
    <div class="ticket-section">

        <div class="title">
            <h1>{{ __('My Tickets') }}</h1>
        </div>
        @if($isAdmin)
            <div class="ticket-filter">
                <a href="{{ route('tickets.index') }}" class="default-link {{ request('status') === null ? 'active' : '' }}">
                    {{ __('All') }}
                </a>
                <a href="{{ route('tickets.index', ['status' => 'new']) }}" class="default-link {{ request('status') === 'new' ? 'active' : '' }}">
                    {{ __('Newest first') }}
                </a>
                <a href="{{ route('tickets.index', ['status' => 'in_progress']) }}" class="default-link {{ request('status') === 'in_progress' ? 'active' : '' }}">
                    {{ __('In progress') }}
                </a>
                <a href="{{ route('tickets.index', ['status' => 'closed']) }}" class="default-link {{ request('status') === 'closed' ? 'active' : '' }}">
                    {{ __('Closed') }}
                </a>
            </div>
        @endif

        <div class="ticket-block">
            <div class="ticket-sidebar">
                <a href="{{ route('tickets.create') }}" class="btn-primary">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-plus"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
                    <span>{{ __('Create a new ticket') }}</span>
                </a>
                <ul class="tickets-nav">
                    @foreach ($tickets as $ticket)
                        <li class="ticket-item @if(isset($activeTicket) && $activeTicket->id === $ticket->id) active @endif">
                            <a href="{{ route('tickets.index', ['ticket_id' => $ticket->id]) }}" class="ticket-link">

                                <span class="ticket_title_status">
                                    <div class="ticket-title">{{ $ticket->title }}</div>

                                @if ($ticket->status === 'new')
                                        <span class="status-new">{{ __('New') }}</span>
{{--                                        <div class="status-icon new"></div>--}}
                                    @elseif ($ticket->status === 'in_progress')
                                        <span class="status-in-progress">{{ __('Accepted for processing') }}</span>
{{--                                        <div class="status-icon in-progress"></div>--}}
                                    @elseif ($ticket->status === 'closed')
                                        <span class="status-closed">{{ __('Closed') }}</span>
{{--                                        <div class="status-icon closed"></div>--}}
                                    @else
                                        <span class="status-unknown">Неизвестный статус</span>
{{--                                        <div class="status-icon unknown"></div>--}}
                                    @endif
                                </span>

                                <span class="ticket_date_username">
                                    @if ($isAdmin)
                                        <strong>{{ $ticket->username }}</strong>
                                    @endif
                                <span class="ticket-date">
                                    @if ($isAdmin)
                                        {{ $ticket->human_readable_date }}
                                    @else
                                        {{ $ticket->formatted_date }}
                                    @endif
                                </span>
                                </span>


                            </a>
                        </li>
                    @endforeach
                </ul>

            </div>

            <div class="ticket-messages">
                @if ($activeTicket)
                    <div class="ticket-top-block">
                        <h2>{{ $activeTicket->title }}</h2>
                        <p>{{ __('Priority') }}: {{ $activeTicket->priority }}</p>
                        <p>{{ __('Created')  }}: {{ $activeTicket->created_at->format('d.m.Y') }}</p>

                        @if ($isAdmin)
                            <form action="{{ route('tickets.updateStatus', $activeTicket->id) }}" class="ticket_admin_status" method="POST">
                                @csrf
                                @method('PATCH')

                                <label for="ticket-status">{{ __('Status') }}:</label>
                                <select class="form-control" name="status" id="ticket-status">
                                    <option value="new" {{ $activeTicket->status === 'new' ? 'selected' : '' }}>{{ __('New') }}</option>
                                    <option value="in_progress" {{ $activeTicket->status === 'in_progress' ? 'selected' : '' }}>{{ __('Accepted for processing') }}</option>
                                    <option value="closed" {{ $activeTicket->status === 'closed' ? 'selected' : '' }}>{{ __('Closed') }}</option>
                                </select>

                                <button type="submit" class="btn-primary">{{ __('Save') }}</button>
                            </form>
                        @endif
                    </div>

                    <div class="ticket-message">
                        @foreach ($activeTicket->messages as $message)
                            @php
                                $isCurrentUser = $message->user_id === auth()->id();
                                $isAdmin = str_ends_with($message->user->email ?? '', '@admin.com');
                            @endphp

                            <div class="message-container {{ $isCurrentUser ? 'right' : 'left' }}">
                                <div class="message {{ $isCurrentUser ? 'right' : 'left' }}">
                                    <p>{!! nl2br(e($message->message)) !!}</p>
                                    @if ($message->attachment)
                                        <a href="{{ asset('storage/' . $message->attachment) }}" target="_blank">
                                            <img src="{{ asset('storage/' . $message->attachment) }}" alt="Attachment" class="message-attachment">
                                        </a>
                                    @endif
                                    <div class="ticket_msg_date">
                                        {{ $message->created_at->format('d.m.Y H:i') }}
                                        <strong>
                                            {{ $message->user->name ?? 'Admin' }}
                                            @if ($isAdmin)
                                                <span class="ticket_is_admin">Support</span>
                                            @endif
                                        </strong>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        @if ($errors->has('message'))
                            <div class="alert-danger">
                                {{ $errors->first('message') }}
                            </div>
                        @endif
                    </div>



                    @if ($activeTicket && $activeTicket->status !== 'closed')
                        <!-- Контейнер для превью загруженного файла -->
                        <div id="preview-container" style="display: none;">
                            <p>Вы прикрепили файл:</p>
                            <div class="preview-wrapper">
                                <img id="preview-image" src="" alt="Превью изображения">
                                <span class="remove-file" onclick="removeFile()">×</span>
                            </div>
                        </div>

                        <form action="{{ route('tickets.reply', $activeTicket->id) }}" class="ticket-reply" method="POST" enctype="multipart/form-data">
                            <div class="file_upload">
                                @csrf
                                <!-- Скрываем поле ввода -->
                                <input type="file" name="attachment" id="fileToUpload" accept=".jpg,.jpeg,.png,.webp" onchange="validateFile()">
                                <!-- SVG, по клику на который открывается диалог выбора файла -->
                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20.7651 6.33636C22.4501 4.63154 25.1571 4.52628 26.8142 6.20283C28.4664 7.8744 28.3639 10.6 26.6822 12.3014L13.9116 25.2219C11.5965 27.5641 7.87362 27.6959 5.61809 25.4139C3.40247 23.1723 3.49268 19.4999 5.70158 17.1548C5.72766 17.1114 5.75925 17.0705 5.79635 17.033L14.9806 7.74104C15.2324 7.48626 15.6431 7.48387 15.8979 7.73569C16.1527 7.98752 16.155 8.39821 15.9032 8.653L6.82109 17.8416C6.79584 17.8824 6.76565 17.921 6.73051 17.9566C4.87422 19.8346 4.83256 22.7737 6.54074 24.5019C8.244 26.2252 11.136 26.1846 12.9889 24.3099L25.7596 11.3895C26.9858 10.1489 27.0013 8.23755 25.8916 7.11478C24.7868 5.99699 22.9106 6.01111 21.6877 7.24832L9.71674 19.3597C9.08653 19.9973 9.10231 20.9643 9.63888 21.5072C10.1775 22.0521 11.1207 22.0551 11.7402 21.4284L19.4341 13.6442C19.6859 13.3895 20.0966 13.3871 20.3514 13.6389C20.6062 13.8907 20.6085 14.3014 20.3567 14.5562L12.6628 22.3403C11.586 23.4298 9.81174 23.5275 8.71623 22.4191C7.64166 21.3319 7.70376 19.5509 8.79409 18.4477L20.7651 6.33636Z" fill="#1C1C28"></path>
                                </svg>
                            </div>
                            <div class="form_group">
                                <textarea name="message" id="response-area" class="area-for-response-text" autofocus="" autocomplete="false" rows="1" placeholder="{{ __('Write a message...') }}" required></textarea>
                            </div>
                            <button type="submit" class="btn-green">{{ __('Send') }}</button>
                        </form>
                    @else
                        @if ($activeTicket && $activeTicket->status === 'closed')
                            <p class="ticket-closed-message">{{ __('This ticket is closed. You cannot leave messages.') }}</p>
                        @endif
                    @endif

                @else
                    <div class="ticket-no-active">
                        <img src="{{ base_url('img/lin2web/msg-book.svg') }}" alt="">
                        <p>{{ __('Select a ticket or create a new one.') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const textarea = document.getElementById("response-area");

            function adjustHeight() {
                textarea.style.height = "auto"; // Сбрасываем высоту, чтобы вычислить новую
                textarea.style.height = textarea.scrollHeight + "px"; // Устанавливаем новую высоту
            }

            textarea.addEventListener("input", adjustHeight);

            textarea.addEventListener("keydown", function (event) {
                if (event.key === "Enter" && !event.shiftKey) {
                    event.preventDefault(); // Предотвращаем стандартный перенос строки
                    textarea.value += "\n"; // Принудительно добавляем новую строку
                    adjustHeight(); // Увеличиваем высоту поля
                }
            });

            // Устанавливаем начальную высоту
            adjustHeight();
        });


        // Клик по SVG открывает скрытое поле input[type="file"]
        document.querySelector('.file_upload svg').addEventListener('click', function () {
            document.getElementById('fileToUpload').click();
        });

        // Проверка размера файла
        function validateFile() {
            const file = document.getElementById('fileToUpload').files[0];
            if (file && file.size > 2 * 1024 * 1024) {
                alert("Размер файла не должен превышать 2MB.");
                document.getElementById('fileToUpload').value = ""; // Сбросить файл
            }
        }

        document.addEventListener("DOMContentLoaded", function () {
            const fileInput = document.getElementById("fileToUpload");
            const previewContainer = document.getElementById("preview-container");
            const previewImage = document.getElementById("preview-image");

            fileInput.addEventListener("change", function () {
                const file = this.files[0];

                if (file) {
                    const reader = new FileReader();

                    reader.onload = function (event) {
                        previewImage.src = event.target.result; // Устанавливаем картинку
                        previewContainer.style.display = "block"; // Показываем контейнер с миниатюрой
                    };

                    reader.readAsDataURL(file); // Читаем файл как DataURL
                }
            });
        });

        // Функция удаления файла
        function removeFile() {
            document.getElementById("fileToUpload").value = ""; // Очищаем инпут
            document.getElementById("preview-container").style.display = "none"; // Скрываем превью
        }


    </script>

@endsection
