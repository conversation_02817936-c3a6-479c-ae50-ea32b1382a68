@section('title', __('Statistics'))
@extends('template::layouts.dashboard')

@section('content')
    @include('template::components.nav-stats')
    <div class="stats_table">
        <table class="rating index">
            <thead>
            <tr>
                <th colspan="2">{{ __('Overall statistics') }}</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>{{ __('Accounts') }}:</td>
                <td>{{ $list->accounts_count }}</td>
            </tr>
            <tr>
                <td>{{ __('Characters') }}:</td>
                <td>{{ $list->characters_count }}</td>
            </tr>
            @if(isset($list->nobles_count))
                <tr>
                    <td>Дворян:</td>
                    <td>{{ $list->nobles_count }}</td>
                </tr>
            @endif
            @if(isset($list->heroes_count))
                <tr>
                    <td>Героев:</td>
                    <td>{{ $list->heroes_count }}</td>
                </tr>
            @endif
            @if(isset($list->gm_count))
                <tr>
                    <td>Гейм мастеров:</td>
                    <td>{{ $list->gm_count }}</td>
                </tr>
            @endif
            @if(isset($list->clans_count))
                <tr>
                    <td>{{ __('Clans') }}:</td>
                    <td>{{ $list->clans_count }}</td>
                </tr>
            @endif
            @if(isset($list->alliance_count))
                <tr>
                    <td>{{ __('Alliances') }}:</td>
                    <td>{{ $list->alliance_count }}</td>
                </tr>
            @endif
            </tbody>
        </table>

    </div>

    <div class="stats_table">
        <table class="rating index">
            <thead>
            <tr>
                <th colspan="4">{{ __('Gender ratio') }}</th>
            </tr>
            </thead>
            <tbody>
            @if(isset($list->sexman))
                <tr>
                    <td>{{ __('Male') }}:</td>
                    <td>{{ $list->sexman }}</td>
                    <td>
                        <div class="bar-container">
                            <div class="bar-fill" style="width: {{ $list->sexman_percent }}%"></div>
                        </div>
                    </td>
                    <td>{{ $list->sexman_percent }}%</td>
                </tr>
            @endif

            @if(isset($list->sexwoman))
                <tr>
                    <td>{{ __('Female') }}:</td>
                    <td>{{ $list->sexwoman }}</td>
                    <td>
                        <div class="bar-container">
                            <div class="bar-fill female" style="width: {{ $list->sexwoman_percent }}%"></div>
                        </div>
                    </td>
                    <td>{{ $list->sexwoman_percent }}%</td>
                </tr>
            @endif
            </tbody>
        </table>
    </div>


    @if(config('app.statistics.races_stats_enabled'))
        <div class="stats_table">
            <table class="rating index">
                <thead>
                <tr>
                    <th colspan="4">{{ __('Percentage ratio') }}</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($raceCounts as $race => $data)
                    <tr>
                        <td>{{ $race }}</td>
                        <td>{{ $data['count'] }}</td>
                        <td>
                            <div class="bar-container">
                                <div class="bar-fill" style="width: {{ $data['percentage'] }}%"></div>
                            </div>
                        </td>
                        <td>{{ $data['percentage'] }}%</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    @endif


@endsection
