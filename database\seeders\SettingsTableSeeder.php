<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsTableSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            'general_settings' => config('app'),
            'gameservers_db' => config('app.gameservers_db'),
            'loginserver' => config('app.loginserver'),
            'statistics' => config('app.statistics'),
            'pay_system' => config('app.pay_system'),
            'unstuck' => config('app.unstuck'),
            'custom_config' => config('app.custom_config'),
            // 'bonus_system' => config('app.bonus_system'),
        ];

        foreach ($settings as $key => $value) {
            Setting::create([
                'group' => ucfirst($key),  // Используем ключ в качестве группы
                'key' => $key,
                'value' => $value,  // Нет необходимости в json_encode, данные из config уже могут быть массивами
                'description' => "Настройки для {$key}",
            ]);
        }



    }
}
