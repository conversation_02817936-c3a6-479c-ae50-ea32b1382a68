<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('starterpack_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id'); // ✅ если users.id — UUID
            $table->unsignedInteger('starterpack_id');
            $table->timestamps();

            $table->unique(['user_id', 'starterpack_id']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('starterpack_user');
    }
};
