@section('title', __('Epic bosses'))
@extends('template::style.stats')

@section('content')
<div class="castle-block">
    @foreach($epic as $e)
    @if($e->epic_title == 'Core')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b class="epic_name">{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Baium')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Antharas')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Valakas')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Zaken')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Frintezza')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}<br>с 20:30 по 21:30</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Orfen')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @elseif($e->epic_title == 'Queen Ant')
    <table class="castle">
        <tbody>
            <tr>
                <td rowspan="7" align="left" valign="top">
                    <b>{{ $e->epic_title }}</b>&nbsp;
                    @if($e->respawn === 0)
                    <img src="{{ $e->epic_img }}" width="149" height="99">
                    @else
                    <img class="eb_dead" src="{{ $e->epic_img }}" width="149" height="99">
                    @endif
                </td>

                <td width="150" align="left">Уровень:</td>
                @if($e->level)
                <td width="300" align="left">
                    {{ $e->level }} lvl
                </td>
                @endif
            </tr>
            <tr>
                <td align="left">Статус:</td>
                <td align="left">
                    @if($e->respawn === 0)
                    <div class="eb_status"><div class="eb_live"></div> {{ __('Live') }}</div>
                    @else
                    <div class="eb_status eb_offline"><div class="eb_dead"></div> {{ __('Dead') }}</div>
                    @endif
                </td>
            </tr>

            @if($e->respawn != 0)
            <tr>
                <td align="left">Возрождение:</td>
                <td align="left">
                    <span> - {{ $e->respawn }}</span>
                </td>
            </tr>
            @endif
        </tbody>
    </table>
    @endif
    @endforeach




</div>



@endsection
