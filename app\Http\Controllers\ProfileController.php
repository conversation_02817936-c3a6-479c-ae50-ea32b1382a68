<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Models\ModelFactory;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Carbon\CarbonInterval;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

class ProfileController extends Controller
{
    public function index() {
        try {
            if (Auth::check()) {
                $userId = auth()->user()->id;
                $cacheKeyPlayerList = "player_list_user_{$userId}";
                $cacheKeyGameAccounts = "game_accounts_user_{$userId}";

                // Кэшируем список персонажей
                $player_list = Cache::remember($cacheKeyPlayerList, config('app.profile_cached_time'), function () use ($userId) {
                    $login = ModelFactory::l2jModel(config('app.l2server_version'))->getGameLoginByUserId($userId);
                    if ($login) {
                        $player_list = ModelFactory::l2jModel(config('app.l2server_version'))->getCharacters($login->login);
                        foreach ($player_list as $item) {
                            $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                            $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                            $item->class_id = class_id($item->class_id);
                            // $item->onlinetime = CarbonInterval::seconds($item->onlinetime)->cascade()->forHumans();
                        }
                        return $player_list;
                    }
                    return collect();
                });

                // Кэшируем игровые аккаунты с их персонажами
                $game_accounts = Cache::remember($cacheKeyGameAccounts, config('app.profile_cached_time'), function () use ($userId) {
                    $game_accounts = ModelFactory::l2jModel(config('app.l2server_version'))->getGameAccounts($userId);
                    foreach ($game_accounts as $ga) {
                        $ga->player_list = ModelFactory::l2jModel(config('app.l2server_version'))->getCharacters($ga->login);
                        foreach ($ga->player_list as $pl) {
                            $pl->allyCrest = crest('ally', $pl->ally_id, $pl->allyCrest);
                            $pl->clanCrest = crest('clan', $pl->clan_id, $pl->clanCrest);
                            $pl->class_id = class_id($pl->class_id);
                            // $pl->onlinetime = CarbonInterval::seconds($pl->onlinetime)->cascade()->forHumans();
                        }
                    }
                    return $game_accounts;
                });
            }
        } catch (\Throwable $e) {
            \Log::error('Ошибка в методе index контроллера: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return view('template::dashboard.index', [
                'game_accounts' => collect(),
                'player_list' => collect(),
                'error_message' => '发生错误,稍后再试.'
            ]);
        }

        return view('template::dashboard.index', [
            'game_accounts' => $game_accounts ?? collect(),
            'player_list' => $player_list ?? collect(),
            'error_message' => null,
        ]);
    }



    public function createGameAccount(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'name' => ['required', 'string', 'min:5', 'max:16'],
                'password' => ['required', 'min:6', 'max:16', 'confirmed'],
            ]);

            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            // Проверка наличия аккаунта в таблице 'accounts'
            if ($model->checkAccountExist($request->name)) {
                return response()->json(['errors' => ['name' => [__('An account with that name already exists.')]]], 422);
            }

            $password = hashPassword($request->password);

            $model->createGameAccount($request->name, $password, auth()->id());

            // Очищаем кэш, чтобы обновить список
            $userId = auth()->id();
            Cache::forget("player_list_user_{$userId}");
            Cache::forget("game_accounts_user_{$userId}");

            return response()->json(['message' => __('The game account has been successfully registered')]);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        }
    }

    public function changeGamePassword(Request $request)
    {
        try {
            // Валидация входящих данных
            $request->validate([
                'game_login' => ['required', 'string', 'min:5', 'max:16'],
                'password' => ['required', 'string', 'confirmed', 'min:5', 'max:16'],
            ]);

            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            // Проверка наличия аккаунта и ma_id
            if (!$model->checkAccountMaIdExist($request->game_login, auth()->user()->id)) {
                return response()->json(['errors' => ['game_login' => [__('An account with that name already exists.')]]], 422);
            }

            // Обновление пароля
            $password = hashPassword($request->password);
            $model->changeGamePassword($request->game_login, $password);

            // Возвращаем успешный ответ
            return response()->json(['message' => __('Your password has been successfully changed.')]);
        } catch (ValidationException $e) {
            // \Log::error('Validation Error:', $e->errors());
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            // Логируем ошибку для отладки
            \Log::error('General Error:', ['error' => $e->getMessage()]);

            // Возвращаем единое сообщение об ошибке
            return response()->json(['errors' => ['message' => __('Server error. Please try again later.')]], 500);
        }
    }


    public function syncAccountIndex()
    {
        return view('template::pages.sync-account');
    }

    public function syncGameAccount(Request $request)
    {
        // Retrieve the current failed login attempts from the session or initialize it
        $failedAttempts = session()->get('failed_attempts_' . $request->name, 0);
        $lockoutTime = session()->get('lockout_time_' . $request->name);

        // Check if the user is locked out
        if ($lockoutTime && now()->lessThan($lockoutTime)) {
            $remainingTime = $lockoutTime->diffInMinutes(now());
            return back()->withErrors(['name' => __('Your account has been blocked for :minutes minutes.', ['minutes' => $remainingTime])]);
        }

        // Validate the request
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'password' => ['required', 'confirmed'],
        ]);

        // Check if the account has already been synchronized
        $existingMaId = DB::connection('loginserver')
            ->table('accounts')
            ->where('ma_id', '>', 0)
            ->where('login', $request->name) // make sure the check is for the correct account
            ->first();

        if ($existingMaId) {
            return back()->withErrors(['name' => __('Этот аккаунт уже был синхронизирован')]);
        }

        // Check if the account exists in the 'accounts' table
        $existingAccount = DB::connection('loginserver')
            ->table('accounts')
            ->where('login', $request->name)
            ->first();

        if ($existingAccount) {
            // Verify the password
            if (base64_encode(hash('whirlpool', $request->password, true)) !== $existingAccount->password) {
                // Increment the failed attempts
                $failedAttempts++;
                session()->put('failed_attempts_' . $request->name, $failedAttempts);

                // Lock the user for 10 minutes if they have 3 failed attempts
                if ($failedAttempts >= 3) {
                    $lockoutTime = now()->addMinutes(config('app.sync_ban_time'));
                    session()->put('lockout_time_' . $request->name, $lockoutTime);
                    return back()->withErrors(['name' => __('Your account has been blocked for 10 minutes.')]);
                }

                return back()->withErrors(['password' => __('Invalid username or password. Attempts left: :attempts', ['attempts' => 3 - $failedAttempts])]);
            }

            // If login is successful, reset failed attempts and update ma_id
            session()->forget('failed_attempts_' . $request->name);
            session()->forget('lockout_time_' . $request->name);

            DB::connection('loginserver')
                ->table('accounts')
                ->where('login', $request->name)
                ->update(['ma_id' => auth()->id()]);

            return back()->with('message', __('Your account has been successfully synchronized'));
        }

        return back()->withErrors(['name' => __('An account with this username has not been found')]);
    }

    public function unstuck(Request $request) {

        $obj_Id = $request->input('char_id');
        $char_name = ModelFactory::l2jModel(config('app.l2server_version'))->getCharNameByCharId($obj_Id);
        $currentTime = now();
        $expirationTime = Auth::user()->last_unstuck_expired;

        if ($expirationTime && $currentTime < $expirationTime) {
            // Пользователь не может снова использовать unstuck до истечения часа
            $remainingTime = $currentTime->diffInMinutes($expirationTime);
            return redirect()->back()->withErrors(['unstuck' => __('You can use unstuck via') . " $remainingTime " . __('min.')]);
        }

        Auth::user()->update(['last_unstuck_expired' => $currentTime->addMinutes(config('app.unstuck.unstuck_expire_time'))]);

        $x = config('app.unstuck.x');
        $y = config('app.unstuck.y');
        $z = config('app.unstuck.z');

        ModelFactory::l2jModel(config('app.l2server_version'))->unstuck($obj_Id, $x, $y, $z);

        return redirect()->back()->withErrors(['unstuck' => __('Your player') .' '. $char_name .' '. __('teleported to') .' '. config('app.unstuck.town')]);
    }

    public function deleteHwid(Request $request) {
        $obj_Id = $request->input('char_id');

        $hwidFound = ModelFactory::l2jModel(config('app.l2server_version'))->deleteHwid($obj_Id);

        if (!$hwidFound) {
            return redirect()->back()->withErrors(['hwid' => "Этот персонаж уже отвязан от HWID"]);
        }

        return redirect()->back()->with(['hwid' => 'Ваш HWID успешно отвязан']);
    }

    public function deleteAntiscam(Request $request) {
        $obj_Id = $request->input('char_id');

        $hwidFound = ModelFactory::l2jModel(config('app.l2server_version'))->getDeleteAntiscam($obj_Id);

        if (!$hwidFound) {
            return redirect()->back()->withErrors(['antiscam' => "Этот персонаж уже отвязан от Antiscam"]);
        }

        return redirect()->back()->with(['antiscam' => 'Ваш пароль Antiscam успешно отвязан']);
    }

    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): View
    {
        return view('template::auth.update-password-form', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }

    public function changeEmailIndex()
    {
        return view('template::profile.change-email');
    }

    public function changeEmail(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email', 'unique:users,email'],
        ]);

        $user = auth()->user();
        $token = Str::random(64);

        // Сохраняем запрос в БД (временная таблица)
        DB::table('email_changes')->updateOrInsert(
            ['user_id' => $user->id],
            [
                'new_email' => $request->email,
                'token' => $token,
                'created_at' => now(),
            ]
        );

        Mail::send('template::profile.confirm-change-email', [
            'user' => $user,
            'newEmail' => $request->email,
            'confirmUrl' => route('profile.confirm-email', $token),
        ], function ($message) use ($user) {
            $message->to($user->email)
                ->subject(__('Email change confirmation.'));
        });


        return back()->with('status', 'confirmation-sent');
    }

    public function confirmChangeEmail($token)
    {
        $record = DB::table('email_changes')->where('token', $token)->first();

        if (!$record) {
            abort(404, 'Token Error');
        }

        $user = User::find($record->user_id);

        if (!$user) {
            abort(404, 'User Not Found');
        }

        $user->email = $record->new_email;
        $user->save();

        DB::table('email_changes')->where('token', $token)->delete();

        Auth::logout(); // Разлогиниваем пользователя

        return redirect()->route('login')->with('status', __('Your email has been successfully updated. Please log in again.'));
    }
}
