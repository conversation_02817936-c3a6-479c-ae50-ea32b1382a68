@section('title', __('Top PK'))
@extends('template::layouts.dashboard')

@section('content')
    @include('template::components.nav-stats')
    <div class="lin2Table">
        <table class="rating">
            <thead>
            <tr>
                <th>№</th>
                <th>{{ __('Player') }}</th>
                <th>{{ __('Class') }}</th>
                <th>{{ __('Gender') }}</th>
                <th>{{ __('Clan') }}</th>
                <th>{{ __('PK') }}</th>
            </tr>
            </thead>
            <tbody>
            @php $index = 1; @endphp
            @foreach($pk as $p)
                <tr>
                    <td>{{ $index++ }}</td>
                    <td>
                        <div class="char-name">
                            @if($p->ally_crest || $p->clan_crest)
                                <span class="clan-crests">
                                @if($p->ally_crest)
                                        <img src="{{ $p->ally_crest }}" width="8" height="12" alt="Ally">
                                    @endif
                                    @if($p->clan_crest)
                                        <img src="{{ $p->clan_crest }}" width="16" height="12" alt="Clan">
                                    @endif
                            </span>
                            @endif
                            {{ $p->char_name }}
                        </div>
                    </td>
                    <td>{{ $p->class_id }}</td>
                    <td>{{ $p->sex }}</td>
                    <td>{{ $p->clan_name }}</td>
                    <td style="color:red">{{ $p->pkkills }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>

    </div>
@endsection
