<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;

class RusAcisModel extends Model
{
    use HasFactory;

    public function getAccounts($ma_id, $include_chars) {
        $data = [
            'ma_id' => $ma_id
        ];

        $return_data = [];

        $result = DB::connection('loginserver')
            ->table('accounts')
            ->select("login")
            ->where('ma_id', $data)
            ->get();

        if ($include_chars) {
            foreach ($result as $row) {
                $return_data[$row->login] = $this->getCharacters($row->login);
            }
        } else {
            foreach ($result as $row) {
                $return_data[] = $row->login;
            }
        }

        return $return_data;
    }

    public function getGameLoginByUserId($user_id) {
        return DB::connection('loginserver')
            ->table('accounts')
            ->where('ma_id', $user_id)
            ->select('login')
            ->first();
    }

    public function getCharacters($account) {
        return DB::connection(gameservers())
            ->table('characters as c')
            ->leftJoin('clan_data as cd', 'c.clanId', '=', 'cd.clan_id')
            ->leftJoin('character_subclasses as cb', 'cb.char_obj_id', '=', 'c.obj_Id')
            ->leftJoin('clan_subpledges as cs', 'cs.clan_id', '=', 'cd.clan_id')
            ->where('c.account_name', '=', $account)
            ->select(
                'cd.ally_crest_id as allyCrest',
                'cd.crest_id as clanCrest',
                'cd.clan_id',
                'cd.ally_id',
                'c.obj_Id',
                'c.account_name',
                'c.char_name',
                'c.level',
                'c.classid as class_id',
                'c.onlinetime',
                'c.pvpkills',
                'c.pkkills',
                'c.karma',
                'c.online',
                'cs.name AS clan_name'
            )
            ->get();
    }

    public function getGameAccounts($ma_id)
    {
        return DB::connection('loginserver')
            ->table('accounts')
            ->where('ma_id', '=', $ma_id)
            ->select('login', 'ma_id')
            ->get();
    }

    public function getCountAccounts() {
        return DB::connection('loginserver')
            ->table('accounts')
            ->count();
    }

    public function getCountCharacters() {
        return DB::connection(gameservers())
            ->table('characters')
            ->count();
    }

    public function getCountNobles() {
        return DB::connection(gameservers())
            ->table('olympiad_nobles')
            ->count();
    }

    public function getCountHeroes() {
        $result = DB::connection(gameservers())
            ->selectOne('SELECT COUNT(*) AS hero_count FROM olympiad_heroes');

        return $result->hero_count;
    }

    public function getCountGameMasters() {
        return DB::connection(gameservers())
            ->table('characters')
            ->where('accesslevel', '>', 0)
            ->count();
    }

    public function getCountClans() {
        return DB::connection(gameservers())
            ->table('clan_data')
            ->count();
    }

    public function getCountAllyance() {
        return DB::connection(gameservers())
            ->table('clan_data')
            ->where('ally_id', '>', 0)
            ->count();
    }

    public function getSexMan() {
        return DB::connection(gameservers())
            ->table('characters')
            ->where('sex', '=', 1)
            ->count();
    }

    public function getSexWoman() {
        return DB::connection(gameservers())
            ->table('characters')
            ->where('sex', '=', 0)
            ->count();
    }

    public function countRaces()
    {
        // Подключение к базе данных gameservers
        $subclasses = DB::connection(gameservers())
            ->table('characters')
            ->get();

        // Массив для хранения количества персонажей по расам
        $raceCounts = array_fill_keys(['Human', 'Elf', 'Dark elf', 'Orc', 'Dwarf', 'Kamael'], 0);

        // Проход по всем записям из базы данных
        foreach ($subclasses as $subclass) {
            // Получаем данные класса через хелпер class_list
            $classList = class_list();
            $classData = $classList[$subclass->classid]; // Получаем данные класса по class_id

            // Получаем имя расы
            $raceName = race_name($classData['race']);

            // Увеличиваем счетчик для этой расы
            if (isset($raceCounts[$raceName])) {
                $raceCounts[$raceName]++;
            }
        }

        // Считаем общее количество персонажей
        $totalCharacters = array_sum($raceCounts);

        // Итоговый массив с процентным соотношением и проверкой на 0
        $finalRaceCounts = [];

        foreach ($raceCounts as $race => $count) {
            if ($count > 0) {
                $percentage = $totalCharacters > 0 ? round(($count / $totalCharacters) * 100, 2) : 0;
                $finalRaceCounts[$race] = [
                    'count' => $count,
                    'percentage' => ceil($percentage),
                ];
            }
        }

        return $finalRaceCounts;
    }

    public function getTopPvp()
    {
        try {
            return DB::connection(gameservers())
                ->table('characters')
                ->select(DB::raw('characters.char_name, characters.sex, characters.pvpkills, characters.pkkills'))
                ->where('characters.pvpkills', '>', 0)
                ->orderBy('characters.pvpkills', 'DESC')
                ->groupBy('characters.pvpkills')
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            Log::error('Ошибка подключения к базе данных в getTopPvp: ' . $e->getMessage());
            abort(403, 'Ошибка подключения к базе данных');
        }
    }

    public function getTopPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, character_subclasses.class_id, character_subclasses.level, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('characters.pvpkills', '>', 0)
            ->orderBy('characters.onlinetime', 'DESC')
            ->groupBy('characters.onlinetime')
            ->limit(config('app.statistics.top_pvp_limit'))
            ->get();
    }

    public function getPvpPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->select(DB::raw('clan_data.ally_crest_id as allyCrest, clan_data.crest_id as clanCrest, clan_data.clan_id, clan_data.ally_id, characters.char_name, characters.classid as class_id, characters.sex, clan_data.clan_name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('characters.pvpkills', '>', 0)
            ->orderBy('characters.pvpkills', 'DESC')
            ->groupBy('characters.pvpkills')
            ->limit(20)
            ->get();
    }

    public function getPkPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->select(DB::raw('clan_data.ally_crest_id as allyCrest, clan_data.crest_id as clanCrest, clan_data.clan_id, clan_data.ally_id, characters.char_name, characters.classid as class_id, characters.sex, clan_data.clan_name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->orderBy('characters.pkkills', 'DESC')
            ->groupBy('characters.pkkills')
            ->limit(20)
            ->get();
    }

    public function getTopPK()
    {
        try {
            return DB::connection(gameservers())
                ->table('characters')
                ->select(DB::raw('characters.char_name, characters.sex, characters.pvpkills, characters.pkkills'))
                ->where('characters.pkkills', '>', 0)
                ->orderBy('characters.pkkills', 'DESC')
                ->groupBy('characters.pkkills')
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            Log::error('Ошибка подключения к базе данных в getTopPK: ' . $e->getMessage());
            abort(403, 'Ошибка подключения к базе данных');
        }
    }

    public function getTopClans() {
        $clans = DB::connection(gameservers())
            ->table('characters as c')
            ->select([
                'cd.ally_crest_id as allyCrest',
                'cd.crest_id as clanCrest',
                'cd.clan_id',
                'cd.ally_id',
                'cd.hasCastle',
                'cd.clan_name as clan_name',
                'cd.clan_level',
                'cd.ally_name as ally_name',
                'cd.ally_name as clanhall_name',
                'c.char_name as clan_leader',
                'cd.reputation_score',
                DB::raw('(SELECT COUNT(0) FROM `characters` WHERE clanid = cd.clan_id) as clan_players'),
                'cd.ally_id'
            ])
            ->leftJoin('clan_data as cd', 'cd.clan_id', '=', 'c.clanid')
            ->leftJoin('clanhall as c2', 'c2.id', '=', 'cd.hasCastle')
            ->leftJoin('castle as c3', function ($join) {
                $join->on('c3.id', '=', 'cd.hasCastle')
                    ->where('cd.clan_id', '=', DB::raw('c.clanid'));
            })
            ->where('c.obj_Id', '=', DB::raw('cd.leader_id'))
            ->groupBy('c.clanid')
            ->orderByDesc('cd.reputation_score')
            ->limit(20)
            ->get();

        $clans->transform(function ($clan) {
            $clan->castle_name = getCastleNameById($clan->hasCastle);
            return $clan;
        });

        return $clans;
    }

    public function getTopAlly()
    {
        return DB::connection(gameservers())
            ->table('characters as c')
            ->select([
                'cd.ally_crest_id as allyCrest',
                'cd.ally_id',
                'cd.ally_name',
                'c.char_name',
                DB::raw('COUNT(cd.ally_id) as ally_players'),
            ])
            ->leftJoin('clan_data as cd', 'cd.leader_id', '=', 'c.obj_Id')
            ->whereNotNull('cd.ally_id') // можно добавить фильтр если нужно
            ->groupBy('cd.ally_id', 'cd.ally_name', 'cd.ally_crest_id', 'c.char_name')
            ->orderByDesc('ally_players')
            ->limit(20)
            ->get();
    }

    public function getCastle()
    {
        $castles = DB::connection(gameservers())
            ->table('castle as c3')
            ->select(
                'cd.ally_crest_id as allyCrest',
                'cd.crest_id as clanCrest',
                'cd.clan_id',
                'cd.ally_id',
                'c.char_name as leader',
                'cd.clan_name as clan_name',
                'c3.id as castle_id',
                'c3.currentTaxPercent as tax_percent',
                'c3.treasury',
                'c3.siegeDate as siege_date',
                'c3.treasury as reward_count'
            )
            ->leftJoin('clan_data as cd', 'cd.hasCastle', '=', 'c3.id')
            ->leftJoin('clan_subpledges as cs', 'cs.clan_id', '=', 'cd.clan_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->orderByDesc('cd.reputation_score')
            ->get();

        // Добавим название замка для каждого объекта
        $castles->transform(function ($castle) {
            $castle->castle_name = getCastleNameById($castle->castle_id);
            return $castle;
        });

        return $castles;
    }


    public function getClanSkills($clan_id) {
        return DB::connection(gameservers())
            ->table('clan_skills')
            ->where('clan_id', $clan_id)
            ->select(
                'clan_id',
                'skill_id',
                'skill_level'
            )
            ->get();
    }


    public function getClanHall() {
        return DB::connection(gameservers())
            ->table('clanhall as ch')
            ->leftJoin('clan_data as cd', 'cd.hasHideout', '=', 'ch.id')
            ->leftJoin('clan_subpledges as cs', function ($join) {
                $join->on('cs.clan_id', '=', 'cd.clan_id')
                    ->where('cs.type', '=', 0);
            })
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->select(
                'ad.ally_id',
                'cd.clan_id',
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'ch.name as clanhall',
                'c.char_name as leader',
                'cs.name as clan_name',
                DB::raw('FROM_UNIXTIME(ch.siege_date / 1000,"%d-%M-%Y %H:%i:%s") as siege_date')
            )
            ->orderBy('ch.own_date', 'DESC')
            ->get();
    }

    public function getEpicBosses() {
        return DB::connection(gameservers())
            ->table('epic_boss_spawn')
            ->select(DB::raw('bossId, respawnDate, state'))
            ->whereIn('bossId', [29020, 29068, 29028, 29045])
            ->get();
    }

    public function getBelethStatus() {
        return DB::connection(gameservers())
            ->table('server_variables')
            ->select('name', 'value')
            ->where('name', 'BelethKillTime')
            ->first();
    }







//    public function getTopItems() {
//        $seconds = 3600;
//        Cache::remember('gameserver', $seconds, function () {
//            return DB::connection(gameservers())
//                ->select('SELECT c.char_name as name,i.item_type, i.enchant
//FROM items i
//LEFT JOIN `characters` c ON c.obj_Id = i.owner_id
//WHERE i.item_type IN (6656,6657,6658,6659,6660,6661,6662) LIMIT 20');
//        });
//    }


    public function getTopSkills() {
        return DB::connection(gameservers())
            ->table('character_skills_save')
            ->leftJoin('characters', 'characters.obj_Id', '=', 'character_skills_save.char_obj_id')
            ->select(DB::raw('characters.char_name as name, character_skills_save.skill_id, character_skills_save.skill_level'))
            ->whereIn('character_skills_save.skill_id', explode(',', config('app.top_skill_list')))
            ->paginate(10);
    }

    public function getTopItems() {
        return DB::connection(gameservers())
            ->table('items')
            ->leftJoin('characters', 'characters.obj_Id', '=', 'items.owner_id')
            ->select(DB::raw('characters.char_name as name, items.item_id, items.enchant_level'))
            ->whereIn('item_id', explode(',', config('app.top_item_list')))
            ->orderByDesc('items.enchant_level')
            ->paginate(10);
    }

    public function getBestPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('characters.onlinetime', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getHpPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id,
            ally_data.ally_id, characters.char_name, character_subclasses.curHp, character_subclasses.class_id,
            characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('character_subclasses.curHp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getMpPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest,
            clan_data.clan_id, ally_data.ally_id, characters.char_name, MAX(character_subclasses.curMp) as mp,
            character_subclasses.class_id, characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->groupBy('character_subclasses.curMp')
            ->orderBy('character_subclasses.curMp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getRichPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->leftJoin('items', 'characters.obj_Id', '=', 'items.owner_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest,
            clan_data.clan_id, ally_data.ally_id, characters.char_name, items.count,
            character_subclasses.class_id, characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('items.count', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getHeroPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('oly_heroes', 'characters.obj_Id', 'oly_heroes.char_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id,
            ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex,
            clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->where('oly_heroes.active', '=', 1)
            ->limit(20)
            ->get();
    }

    public function getBaronPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, characters.curHp, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('characters.curHp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getRaidBosses() {
        return DB::connection(gameservers())
            ->table('raidboss_status')
            ->select(DB::raw('id, respawn_delay'))
            ->whereIn('id', [29001, 29006, 29014])
            ->get();
    }

    public function getPlayersList($ma_id) {
        $accountNames = DB::connection('loginserver')
        ->table('accounts')
            ->where('ma_id', '=', $ma_id)
            ->pluck('login');

        // Если нет аккаунтов, возвращаем пустую коллекцию
        if ($accountNames->isEmpty()) {
            return collect();
        }

        // Теперь делаем запрос к 'gameserver' для получения персонажей, связанных с этими аккаунтами
        return DB::connection(gameservers())
            ->table('characters')
            ->select('obj_Id as char_id', 'char_name')
            ->whereIn('account_name', $accountNames)
            ->get();
    }

    public function sendCoins($owner_id, $item_id, $count) {
        try {
            $char_name = $this->getCharNameByCharId($owner_id);
            // $item_id, $item_count, $char_name, $reason = 'CpTransferToGame'
            // Использование соединения с игровой базой данных
            $gameServerDB = DB::connection(gameservers());

            // Вставка данных в таблицу user_item_delivery
            return $gameServerDB->table('user_item_delivery')->insert([
                'item_id' => $item_id,
                'item_count' => $count,
                'char_name' => $char_name,
                'status' => 0, // Статус по умолчанию
                'reason' => 'Donate'
            ]);
        } catch (\Exception $e) {
            // Логирование ошибки
            Log::error("Error in sendCoins: " . $e->getMessage());
            // Выбрасывание исключения
            throw $e;
        }
    }


//    public function sendCoins($owner_id, $item_id, $count) {
//        try {
//            // Использование соединения с игровым сервером
//            $gameServerDB = DB::connection(gameservers());
//
//            // Вставка данных в таблицу
//            return $gameServerDB->table('items_delayed')->insert([
//                'owner_id' => $owner_id,
//                'item_id' => $item_id,
//                'count' => $count,
//                'payment_status' => '0',
//                'description' => 'CpTransferToGame'
//            ]);
//        } catch (\Exception $e) {
//            // Здесь можно логировать ошибку или обрабатывать исключение
//            Log::error("Error in sendCoins: " . $e->getMessage());
//            // Выбрасывание исключения для обработки на более высоком уровне
//            throw $e;
//        }
//    }


    public function getPlayerInventory($char_id) {
        return DB::connection(gameservers())
            ->table('characters as c')
            ->leftJoin('items as i', 'i.owner_id', '=', 'c.obj_Id')
            ->select(DB::raw('item_type, amount, enchant'))
            ->where('i.owner_id', '=', $char_id)
            ->get();
    }

    public function checkPlayer($char_name, $selected_server)
    {
        $player = DB::connection($selected_server)
            ->table('characters')
            ->where('char_name', $char_name)
            ->first();

        if (!$player) {
            throw new \Exception('Player not found');
        }

        return $player;
    }

    public function addItem($char_id, $count, $selected_server) {
        $add_item = DB::connection($selected_server)
            ->table('items_delayed')
            ->insert([
                'owner_id'     => $char_id,
                'item_id'     => config('app.pay_system.item_id'),
                'count'       => $count,
                'description' => 'No auth donate',
            ]);

        if(!$add_item) {
            throw new Exception('Не удалось добавить итем в таблицу items_delayed');
        }

        return $add_item;
    }

    public function addItemBonus($char_id, $item_id, $count, $enchant_level, $selected_server) {
        $add_item = DB::connection($selected_server)
            ->table('items_delayed')
            ->insert([
                'owner_id'     => $char_id,
                'item_id'     => $item_id,
                'count'       => $count,
                'enchant_level'       => $enchant_level,
                'description' => 'Bonus Item',
            ]);

        if(!$add_item) {
            throw new Exception('Не удалось добавить bonus item в таблицу items_delayed');
        }

        return $add_item;
    }


    public function getCharIdByCharName($char_name, $selected_server) {
        $char_id = DB::connection($selected_server)
            ->table('characters')
            ->where('char_name', $char_name)
            ->select('obj_Id')
            ->first();

        if(!$char_id) {
            throw new \Exception('Char Id not found');
        }

        return $char_id;
    }

    public function getCharNameByCharId($char_id) {
        $char = DB::connection(gameservers())
            ->table('characters')
            ->where('obj_Id', $char_id)
            ->select('char_name')
            ->first();

        if(!$char) {
            throw new \Exception('Char Name not found');
        }

        return $char->char_name; // Возвращаем строку, а не объект
    }

    public function getLoginByEmail($email) {
        $email = DB::table('users')
            ->where('email', $email)
            ->first();

        if(!$email) {
            throw new \Exception('Email not found');
        }

        return $email->name;
    }

    public function checkAccountMaIdExist($login, $ma_id): bool
    {
        return DB::connection('loginserver')
            ->table('accounts')
            ->where('login', $login)
            ->where('ma_id', $ma_id)
            ->exists();
    }

    public function checkAccountExist($login): bool
    {
        return DB::connection('loginserver')
            ->table('accounts')
            ->where('login', $login)
            ->exists();
    }

    public function createGameAccount($login, $password, $ma_id): void
    {
        DB::connection('loginserver')
            ->table('accounts')
            ->insert([
                'login' => $login,
                'password' => $password,
                'ma_id' => $ma_id,
            ]);
    }

    public function changeGamePassword($login, $password): void
    {
        // Обновление пароля в базе данных
        DB::connection('loginserver')
            ->table('accounts')
            ->where('login', $login)
            ->update([
                'password' => $password,
            ]);
    }


    public function unstuck($char_id, $x, $y, $z) {
        return DB::connection(gameservers())
            ->table('characters')
            ->where('obj_Id', $char_id)
            ->update([
                'x' => $x,
                'y' => $y,
                'z' => $z,
            ]);
    }

    public function getPvpPlayersMainPage() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('characters.pvpkills', '>', 0)
            ->orderBy('characters.pvpkills', 'DESC')
            ->groupBy('characters.pvpkills')
            ->limit(20)
            ->get();
    }

    public function getPkPlayersMainPage() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->orderBy('characters.pkkills', 'DESC')
            ->groupBy('characters.pkkills')
            ->limit(20)
            ->get();
    }

    public function getTopClansMainPage() {
        return DB::connection(gameservers())
            ->table('characters as c')
            ->select([
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'cd.clan_id',
                'cd.ally_id',
                'cs.name as clan_name',
                'cd.clan_level',
                'c.char_name as clan_leader',
                'cd.reputation_score',
                'c3.name as castle_name',
                'c2.name as clanhall_name',
                DB::raw('(SELECT COUNT(0) FROM `characters` WHERE clanid = cd.clan_id) as clan_players'),
                'ad.ally_name'
            ])
            ->leftJoin('clan_subpledges as cs', 'c.clanid', '=', 'cs.clan_id')
            ->leftJoin('clan_data as cd', 'cd.clan_id', '=', 'cs.clan_id')
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('clanhall as c2', 'c2.id', '=', 'cd.hasHideout')
            ->leftJoin('castle as c3', function ($join) {
                $join->on('c3.id', '=', 'cd.hasCastle')
                    ->where('cd.clan_id', '=', DB::raw('cs.clan_id'));
            })
            ->where('c.obj_Id', '=', DB::raw('cs.leader_id'))
            ->groupBy('c.clanid')
            ->orderByDesc('cd.reputation_score')
            ->limit(20)
            ->get();
    }

    public function getCastleMainPage()
    {
        return DB::connection(gameservers())
            ->table('castle as c3')
            ->select(
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'cd.clan_id',
                'ad.ally_id',
                'c.char_name as leader',
                'cs.name as clan_name',
                'c3.id as castle_id',
                'c3.name as castle_name',
                'c3.tax_percent',
                'c3.treasury',
                'c3.last_siege_date',
                'c3.own_date as ownDate',
                'c3.siege_date',
                'c3.treasury as reward_count'
            )
            ->leftJoin('clan_data as cd', 'cd.hasCastle', '=', 'c3.id')
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('clan_subpledges as cs', 'cs.clan_id', '=', 'cd.clan_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->where(function ($query) {
                $query->where('cs.type', '!=', -1)
                    ->orWhereNull('cs.type');
            })
            ->where(function ($query) {
                $query->where('cs.type', '!=', 100)
                    ->orWhereNull('cs.type');
            })
            ->where(function ($query) {
                $query->where('cs.type', '!=', 200)
                    ->orWhereNull('cs.type');
            })
            ->where(function ($query) {
                $query->where('cs.type', '!=', 1001)
                    ->orWhereNull('cs.type');
            })
            ->where(function ($query) {
                $query->where('cs.type', '!=', 1002)
                    ->orWhereNull('cs.type');
            })
            ->orderByDesc('cd.reputation_score')
            ->get();
    }

    public function getOnline()
    {
        $servers = config('app.java_servers'); // Получаем список серверов из конфига
        $onlineData = [];

        foreach ($servers as $key => $server) {
            try {
                $online = DB::connection($key) // Устанавливаем подключение к текущему серверу
                ->table('characters')
                    ->where('online', '=', 1)
                    ->count();

                $onlineData[$server['server_name']] = $online; // Добавляем в массив результатов
            } catch (\Exception $e) {
                $onlineData[$server['server_name']] = 'Ошибка подключения';
            }
        }

        return $onlineData;
    }
}
