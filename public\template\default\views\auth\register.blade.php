@extends('template::layouts.auth')
@section('title', __('Master account registration'))

@section('content')
    <div class="title">{{ __('Create a new account') }}</div>

    <form method="POST" action="{{ route('register') }}">
        @csrf
        <div class="form-group">
            <input id="name" class="form-control" type="text" name="name" value="{{ old('name') }}" required autofocus autocomplete="name" placeholder="{{ __('Your login') }}" />
            @if ($errors->has('name'))
                <p class="text-danger">{{ $errors->first('name') }}</p>
            @endif
        </div>
        <div class="form-group">
            <input id="email" class="form-control" type="email" name="email" value="{{ old('email') }}" placeholder="{{ __('Your Email') }}" required autocomplete="username" />
            @if ($errors->has('email'))
                <p class="text-danger">{{ $errors->first('email') }}</p>
            @endif
        </div>
        <div class="form-group">
            <div class="password-group">
                <input id="password" class="form-control" type="password" name="password" required autocomplete="new-password" placeholder="{{ __('Password 6 to 14 symbols') }}" />
                <span class="input-group-text password-toggle" data-target="password" data-state="hidden"></span>
            </div>
            @if ($errors->has('password'))
                <p class="text-danger">{{ $errors->first('password') }}</p>
            @endif
        </div>
        <div class="form-group">
            <div class="password-group">
                <input id="password_confirmation" class="form-control" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="{{ __('Confirm Password') }}" />
                <span class="input-group-text password-toggle" data-target="password_confirmation" data-state="hidden"></span>
            </div>
            @if ($errors->has('password_confirmation'))
                <p class="text-danger">{{ $errors->first('password_confirmation') }}</p>
            @endif
        </div>
        <div class="form-group login_button">
            <button type="submit" class="btn-green"><span>{{ __('Register') }}</span></button>
        </div>
        <div class="form-group text-center">
            <a class="btn-gray-no-border" href="{{ route('login') }}">
                {{ __('Already registered?') }}
            </a>
        </div>
    </form>
@endsection
