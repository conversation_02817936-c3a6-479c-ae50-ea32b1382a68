<?php

namespace App\Services\Payment;

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Midtrans\Config;
use Midtrans\Snap;
use Midtrans\Notification;

class MidTransService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;

        // Используем конфиг для ключей
        Config::$serverKey = config('app.pay_system.midtrans_server_key');
        Config::$isProduction = config('app.pay_system.midtrans_is_production');
        Config::$isSanitized = true;
        Config::$is3ds = true;
    }

    public function createPayment($payment_id, $count)
    {
        try {
            $currency = config('app.pay_system.midtrans_currency');
            if (!$currency) {
                throw new \Exception('Currency is not set in configuration');
            }

            // Прямо в коде добавляем цену для IDR
            $coinPrices = [
                'IDR' => 15000, // Цена за монету в рупиях
            ];

            if (!isset($coinPrices[$currency])) {
                throw new \Exception("Undefined currency key: {$currency}");
            }

            $coin_price = $coinPrices[$currency];
            $sum = (int) ceil($count * $coin_price);

            $transactionDetails = [
                'order_id' => $payment_id,
                'gross_amount' => $sum,
            ];

            $itemDetails = [
                [
                    'id' => 'coin_purchase',
                    'price' => $sum,
                    'quantity' => 1,
                    'name' => config('app.custom_config.donate_coin_name'),
                ]
            ];

            $customerDetails = [
                'first_name' => 'Customer',
                'last_name' => 'Name',
                'email' => '<EMAIL>',
                'phone' => '08123456789',
            ];

            $payload = [
                'transaction_details' => $transactionDetails,
                'item_details' => $itemDetails,
                'customer_details' => $customerDetails,
            ];

            $isProduction = config('app.pay_system.midtrans_is_production');
            $baseUrl = $isProduction ? "https://app.midtrans.com/snap/v2/vtweb/" : "https://app.sandbox.midtrans.com/snap/v2/vtweb/";
            $snapToken = Snap::getSnapToken($payload);
            return $baseUrl . $snapToken;
        } catch (\Exception $e) {
            Log::error('Midtrans: Error creating payment', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function handleWebhook(Request $request)
    {
        try {
            $notification = new Notification();
            $transactionStatus = $notification->transaction_status;
            $paymentId = $notification->order_id;

            if (!$paymentId) {
                return response('Missing order_id', 400);
            }

            //Log::info('req ' . $request);

            if (in_array($transactionStatus, ['capture', 'settlement'])) {
                $this->paymentProcessingService->processPayment($paymentId);
                //Log::info('Midtrans: Payment successful', ['payment_id' => $paymentId]);
            } else {
                Log::warning('Midtrans: Unhandled transaction status', ['status' => $transactionStatus]);
            }

            return response('Webhook handled', 200);
        } catch (\Exception $e) {
            Log::error('Midtrans: Webhook handling error', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }
}
