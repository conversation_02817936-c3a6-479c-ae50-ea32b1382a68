@section('title', __('Starter packs'))
@extends('template::layouts.dashboard')

@section('content')

<div class="swiper-streamers">
    <div class="swiper-wrapper">
        @foreach($streamers as $streamer)
            @if($streamer['is_online'])
                <div class="swiper-slide">
                    @if($streamer['is_online'])
                        <div class="stream-video">
                            <iframe src="{{ $streamer['video_url'] }}" frameborder="0" allowfullscreen></iframe>
                        </div>
                    @endif
                    <div class="streamer-info">
                        @if(ucfirst($streamer['platform']) == 'Twitch')
                            <img src="{{ base_url('img/lin2web/twitch-emblem.svg') }}">
                        @elseif(ucfirst($streamer['platform']) == 'Trovo')
                            <img src="{{ base_url('img/lin2web/trovo-emblem.svg') }}">
                        @endif

                        {{--                    <p class="stream_live">{{ $streamer['is_online'] ? 'Live' : 'Offline' }}</p>--}}
                        <p class="stream_user">{{ $streamer['username'] }}</p>
                        <p>Зрителей: {{ $streamer['viewer_count'] }}</p>
                        {{--                    <p>Hours Streamed: {{ $streamer['hours_streamed'] }}</p>--}}
                    </div>
                </div>
            @else
                <div class="swiper-slide">
                    <div class="streamer_offline">
                        <div class="streamer_offline_block">
                            Стример оффлайн
                        </div>
                        <div class="streamer-info">
                            @if(ucfirst($streamer['platform']) == 'Twitch')
                                <img src="{{ base_url('img/lin2web/twitch-emblem.svg') }}">
                            @elseif(ucfirst($streamer['platform']) == 'Trovo')
                                <img src="{{ base_url('img/lin2web/trovo-emblem.svg') }}">
                            @endif

                            {{--                    <p class="stream_live">{{ $streamer['is_online'] ? 'Live' : 'Offline' }}</p>--}}
                            <p class="stream_user">{{ $streamer['username'] }}</p>
                            <p>Зрителей: {{ $streamer['viewer_count'] }}</p>
                            {{--                    <p>Hours Streamed: {{ $streamer['hours_streamed'] }}</p>--}}
                        </div>
                    </div>
                </div>
            @endif

        @endforeach
    </div>

    <!-- Swiper Pagination -->
    <div class="swiper-pagination-stream"></div>
</div>

@endsection
