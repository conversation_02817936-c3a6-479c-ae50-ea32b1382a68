@foreach($server_status as $server_name => $online)
<div class="header__server-info">
    <div class="header__server-info-container">
        <div class="header__server-info-content online-info">
            <div class="header__server-info-players">
                <span data-max-online="1000" class="header__server-info-players-count online-count">{{ is_numeric($online) ? $online : '0' }}</span>
            </div>
            <div class="header__server-info-details">
                <div class="header__server-info-name-container">
                    <span class="header__server-info-name">{{ $server_name }}</span>
                    <span class="header__server-info-rate">x100</span>
                </div>
                <span class="header__server-info-type">Interlude</span>
                <div class="header__server-info-rating online-stars"></div>
            </div>
        </div>
    </div>
    <button class="header__server-info-toggle">
        <svg width="20" height="20" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"
             class="header__server-info-toggle-icon">
            <path
                d="M9.91666 7.82264L7.41416 5.34931C7.35993 5.29463 7.29541 5.25124 7.22433 5.22162C7.15324 5.19201 7.077 5.17676 6.99999 5.17676C6.92298 5.17676 6.84674 5.19201 6.77565 5.22162C6.70457 5.25124 6.64005 5.29463 6.58582 5.34931L4.11249 7.82264C4.05782 7.87687 4.01442 7.94139 3.9848 8.01247C3.95519 8.08355 3.93994 8.1598 3.93994 8.23681C3.93994 8.31381 3.95519 8.39006 3.9848 8.46114C4.01442 8.53223 4.05782 8.59675 4.11249 8.65097C4.22179 8.75962 4.36963 8.8206 4.52374 8.8206C4.67785 8.8206 4.8257 8.75962 4.93499 8.65097L6.99999 6.58597L9.06499 8.65097C9.17364 8.75874 9.32029 8.8195 9.47332 8.82014C9.55009 8.82058 9.6262 8.80587 9.69727 8.77684C9.76834 8.74781 9.83298 8.70504 9.88749 8.65097C9.94412 8.5987 9.98982 8.53572 10.022 8.46567C10.0541 8.39563 10.0721 8.31991 10.0748 8.24289C10.0775 8.16587 10.0649 8.08907 10.0378 8.01694C10.0106 7.94481 9.96947 7.87877 9.91666 7.82264Z"
                fill="white" />
        </svg>
    </button>
</div>
@endforeach
