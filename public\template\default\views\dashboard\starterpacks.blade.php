@section('title', __('Starterpacks'))
@extends('template::layouts.dashboard')

@section('content')
<div id="s-pack">
    @if(session('error'))
        <div class="alert alert-danger">
            <p>{{ session('error') }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif
    <div class="s-pack">
        @foreach(config('app.starterpacks') as $pack => $i)
            <form action="{{ route('starterpacks.store') }}" class="s-pack-item" method="POST">
                @csrf
                <input type="hidden" name="spackid" value="{{ $loop->iteration }}">
                <div class="s-pack-title">
                    <img src="{{ base_url('img/lin2web/pack-' . $loop->iteration . '.png') }}">
                    <span>{{ $i['title'] }}</span>
                </div>
                <div class="s-pack-info">
                    <div class="s-pack-i">
                        @foreach($i['items'] as $item_info)
                            <div class="s-item-i">
                                <img src="{{ item_icon($item_info['item_id']) }}" alt="{{ itemName($item_info['item_id']) }}">
                                <div class="s-item-title">{{ itemName($item_info['item_id']) }}</div>
                                @if($item_info['enchant'] > 0)
                                    <div class="s-item-count">+{{ $item_info['enchant'] }}</div>
                                @else
                                    <div class="s-item-count">x {{ $item_info['item_count'] }}</div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>

                <div class="s-pack-prices">
                    @if($i['discount'])
                        @php
                            $originalPrice = $i['price'] + $i['discount'];
                            $discountPercent = round(($i['discount'] / $originalPrice) * 100);
                        @endphp
                        <div class="s-pack-price">{{ $originalPrice }} {{ config('app.custom_config.donate_coin_name') }}</div>
                        <div class="s-pack-discount">-{{ $discountPercent }}% {{ __('购买立省') }}</div>
                    @endif

                    <button type="submit" class="btn-primary">{{ __('立即购买') }} {{ $i['price'] }} {{ config('app.custom_config.donate_coin_name') }}</button>
                </div>
            </form>
        @endforeach
    </div>
</div>

@endsection
