<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <title>@yield('title')</title>

    <link rel="icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>
    <link rel="shortcut icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>

    <link rel="stylesheet" href="{{ base_url('css/auth.css') }}">
    <script src="{{ base_url('js/app.js') }}"></script>
</head>
<body>

<div class="main-bg"></div>

<main>
   <div class="auth_wrapper">
       <div class="auth_top">
           <nav class="auth_nav_top">
               <ul>
                   <li>
                       <a href="{{ route('main-page') }}">
                           <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-home"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                           <span>{{ __('Home') }}</span>
                       </a>
                   </li>
                   <li>
                       <a href="{{ route('about') }}">
                           <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-brightness-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" /><path d="M12 5l0 .01" /><path d="M17 7l0 .01" /><path d="M19 12l0 .01" /><path d="M17 17l0 .01" /><path d="M12 19l0 .01" /><path d="M7 17l0 .01" /><path d="M5 12l0 .01" /><path d="M7 7l0 .01" /></svg>
                           <span>{{ __('About') }}</span>
                       </a>
                   </li>
                   <li>
                       <a href="{{ config('app.custom_config.telegram_support') }}" target="_blank">
                           <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-message"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
                           <span>{{ __('Community') }}</span>
                       </a>
                   </li>
               </ul>
           </nav>
           <div class="auth_top_r">
               <a href="{{ route('download') }}" class="btn-accent">
                   <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-cloud-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 18.004h-5.343c-2.572 -.004 -4.657 -2.011 -4.657 -4.487c0 -2.475 2.085 -4.482 4.657 -4.482c.393 -1.762 1.794 -3.2 3.675 -3.773c1.88 -.572 3.956 -.193 5.444 1c1.488 1.19 2.162 3.007 1.77 4.769h.99c1.38 0 2.573 .813 3.13 1.99" /><path d="M19 16v6" /><path d="M22 19l-3 3l-3 -3" /></svg>
                   <span>{{ __('Download files') }}</span>
               </a>
               @include('template::lang')
           </div>
       </div>

       <div class="auth-content">
           <div class="content">
               @yield('content')
           </div>
       </div>
   </div>
</main>

</body>
</html>
