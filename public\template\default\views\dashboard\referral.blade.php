@section('title', __('Referral System'))
@extends('template::layouts.dashboard')

@section('content')
    <div class="card">
        <div class="title">
            <h1>{{ __('Your referral link') }}</h1>
        </div>
        @if ($referralLink)
            <div class="referral_block">
                <div class="referral_form">
                    <input type="text" class="form-control" id="referralLink" value="{{ $referralLink }}" readonly>
                    <div class="referral_copy_link">
                        <button class="btn-primary" onclick="copyReferralLink()">{{ __('Copy') }}</button>
                        <span id="copyMessage" class="copy-message">{{ __('Copied') }}!</span>
                    </div>
                </div>
                <button class="btn-green open-popup" data-popup-id="popupEditReferral">{{ __('Change') }}</button>
            </div>

            @if(session('success'))
                <div class="alert alert-success">
                    <p>{{ session('success') }}</p>
                    <a href="{{ url()->current() }}" class="close"></a>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    <p>{{ session('error') }}</p>
                    <a href="{{ url()->current() }}" class="close"></a>
                </div>
            @endif

            @error('newReferralCode')
            <div class="alert alert-danger">
                <p>{{ $message }}</p>
                <a href="{{ url()->current() }}" class="close"></a>
            </div>
            @enderror

        @endif

        <div class="referrals_text">
            <p>{{ __('Invited Users') }}: {{ $referralsCount }}</p>
            <h3>{{ __('referral_bonus', ['coin' => config('app.custom_config.donate_coin_name'), 'percent' => config('app.referral_bonus_percent')]) }}</h3>
            <p>{{ __('The bonus is credited to your account immediately after a successful purchase.') }}</p>
        </div>

        {{-- ✅ История реферальных выплат --}}
        @if(empty($user->referralPayments))
            <table class="omg-table">
                <thead>
                <tr>
                    <td>{{ __('Referral') }}</td>
                    <td>{{ __('Your Bonus') }}</td>
                    <td>{{ __('Status') }}</td>
                </tr>
                </thead>
                <tbody>
                @foreach ($user->referralPayments as $payment)
                    <tr>
                        <td>{{ optional($payment->referred)->referral_code ?? __('Referral') }}</td>
                        <td>{{ number_format($payment->bonus_amount, 0) }} {{ config('app.custom_config.donate_coin_name') }}</td>
                        <td>
                            @if($payment->status == 'Success')
                                <div class="alert-success">{{ __('Paid') }}</div>
                            @else
                                {{ __('Pending') }}
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        @endif
    </div>

    {{-- ✅ Попап изменения реферального кода --}}
    <div id="popupEditReferral" class="popup-overlay" style="display: none;">
        <div class="popup-content">
            <div class="popup-title-block">
                <div class="popup-title">{{ __('Edit Referral Code') }}</div>
                <span class="popup-close">
                <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-x"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12" /><path d="M6 6l12 12" /></svg>
            </span>
            </div>
            <form action="{{ route('referral.update') }}" method="POST">
                @csrf
                <div class="form-group">
                    <input type="text" class="form-control @error('newReferralCode') is-invalid @enderror"
                           id="newReferralCode" name="newReferralCode"
                           value="{{ old('newReferralCode', $user->referral_code) }}" required>
                </div>
                <div class="form-group login_button">
                    <button type="submit" class="btn-primary">{{ __('Save') }}</button>
                </div>
                @error('newReferralCode')
                    <div class="form-group">
                        <div class="text-success">{{ $message }}</div>
                    </div>
                @enderror
                <div class="form-group">
                    <p>{{ __('The code must be unique, using only Latin letters and numbers.') }}</p>
                </div>
            </form>
        </div>
    </div>


    <script>
        function copyReferralLink() {
            const link = document.getElementById('referralLink');
            const message = document.getElementById('copyMessage');

            // Копируем текст в буфер обмена
            link.select();
            document.execCommand('copy');

            // Показываем сообщение
            message.style.opacity = '1';

            // Скрываем сообщение через 4 секунды
            setTimeout(() => {
                message.style.opacity = '0';
            }, 4000);
        }

    </script>

@endsection
