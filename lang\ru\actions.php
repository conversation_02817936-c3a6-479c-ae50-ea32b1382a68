<?php

declare(strict_types=1);

return [
    'accept'          => 'Принять',
    'action'          => 'Действие',
    'actions'         => 'Действия',
    'add'             => 'Добавить',
    'admin'           => 'Панель администратора',
    'agree'           => 'Согласен(на)',
    'archive'         => 'Архив',
    'assign'          => 'Назначить',
    'attach'          => 'Прикрепить',
    'browse'          => 'Просмотр',
    'cancel'          => 'Отмена',
    'choose'          => 'Выбрать',
    'choose_file'     => 'Выбрать файл',
    'choose_image'    => 'Выбрать изображение',
    'click_to_copy'   => 'Скопировать',
    'close'           => 'Закрыть',
    'collapse'        => 'Свернуть',
    'collapse_all'    => 'Свернуть всё',
    'comment'         => 'Комментарий',
    'confirm'         => 'Подтвердить',
    'connect'         => 'Подключить',
    'create'          => 'Создать',
    'delete'          => 'Удалить',
    'detach'          => 'Открепить',
    'details'         => 'Подробнее',
    'disable'         => 'Отключить',
    'discard'         => 'Отказаться',
    'done'            => 'Готово',
    'down'            => 'Вниз',
    'duplicate'       => 'Дублировать',
    'edit'            => 'Редактировать',
    'enable'          => 'Включить',
    'expand'          => 'Раскрыть',
    'expand_all'      => 'Раскрыть всё',
    'explanation'     => 'Объяснить',
    'export'          => 'Экспорт',
    'file'            => 'В поле :attribute должен быть указан файл.',
    'files'           => 'Файлы',
    'go_home'         => 'Вернуться на главную',
    'hide'            => 'Скрыть',
    'home'            => 'На главную',
    'image'           => 'Файл, указанный в поле :attribute, должен быть изображением.',
    'Impersonate'     => 'Войти под пользователем',
    'Impersonation'   => 'Войти под пользователем',
    'import'          => 'Импорт',
    'introduction'    => 'Введение',
    'like'            => 'Нравится',
    'load'            => 'Загрузить',
    'localize'        => 'Локализовать',
    'named'           => [
        'choose'    => 'Выбрать :name',
        'duplicate' => 'Дублировать :name',
        'edit'      => 'Редактировать :name',
        'hide'      => 'Скрыть :name',
        'import'    => 'Испортировать :name',
        'new'       => 'Новый :name',
        'restore'   => 'Восстановить :name',
        'save'      => 'Сохранить :name',
        'search'    => 'Искать :name',
        'show'      => 'Показать :name',
        'update'    => 'Обновить :name',
        'view'      => 'Просмотреть :name',
    ],
    'new'             => 'Новый',
    'no'              => 'Нет',
    'open'            => 'Открыть',
    'open_website'    => 'Открыть на сайте',
    'preview'         => 'Предпросмотр',
    'price'           => 'Цена',
    'restore'         => 'Восстановить',
    'save'            => 'Сохранить',
    'save_and_close'  => 'Сохранить и закрыть',
    'save_and_return' => 'Сохранить и вернуться',
    'search'          => 'Поиск',
    'select'          => 'Выбрать',
    'select_all'      => 'Выбрать всё',
    'send'            => 'Отправить',
    'settings'        => 'Настройки',
    'show'            => 'Показать',
    'show_all'        => 'Показать всё',
    'solve'           => 'Решить',
    'submit'          => 'Отправить',
    'subscribe'       => 'Подписаться',
    'switch'          => 'Переключить',
    'switch_to_role'  => 'Переключиться на роль',
    'tag'             => 'Тег',
    'tags'            => 'Теги',
    'target_link'     => [
        'blank'  => 'Открыть в новом окне',
        'parent' => 'Открыть в родительском фрейме',
        'self'   => 'Открыть в текущем окне',
        'top'    => 'Открыть в главном фрейме',
    ],
    'translate'       => 'Перевод',
    'translate_it'    => 'Перевести',
    'unpack'          => 'Распаковать',
    'unsubscribe'     => 'Отписаться',
    'up'              => 'Вверх',
    'update'          => 'Обновить',
    'user'            => 'Не удалось найти пользователя с указанным электронным адресом.',
    'view'            => 'Просмотр',
    'yes'             => 'Да',
];
