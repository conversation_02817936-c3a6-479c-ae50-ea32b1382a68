@section('title', __('Top Players'))
@extends('template::style.stats')

@section('content')
    <div class="lin2Table">
        <table class="omg" cellpadding="0" cellspacing="0" fixed="1">
            <thead>
            <tr class="glav">
                <td class="catbg2" align="center">№</td>
                <td class="catbg2" align="center">{{ __('Game nickname') }}</td>
                <td class="catbg2" align="center">{{ __('Level') }}</td>
                <td class="catbg2" align="center">{{ __('Class') }}</td>
                <td class="catbg2" align="center">{{ __('Gender') }}</td>
                <td class="catbg2" align="center">{{ __('Clan') }}</td>
                <td class="catbg2" align="center">{{ __('Time in the game') }}</td>
            </tr>
            </thead>
            <tbody>
            @php $index = 1; @endphp
            @foreach($top_players as $p)
                <tr>
                    <td align="center">{{ $index++ }}</td>
                    <td align="center">
                        <div class="char_name">
                            @if($p->ally_crest || $p->clan_crest)
                                <div class="clan_crest">
                                    @if($p->ally_crest)
                                        <img src="{{ $p->ally_crest }}" border="0" width="8" height="12">
                                    @endif
                                    @if($p->clan_crest)
                                        <img src="{{ $p->clan_crest }}" border="0" width="16" height="12">
                                    @endif
                                </div>
                            @endif
                            <span>{{ $p->char_name }}</span>
                        </div>
                    </td>
                    <td align="center">{{ $p->level }}</td>
                    <td align="center">{{ $p->class_id }}</td>
                    <td align="center">{{ $p->sex }}</td>
                    <td align="center">{{ $p->clan_name }}</td>
                    <td align="center">{{ $p->onlinetime }}</td>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>

@endsection
