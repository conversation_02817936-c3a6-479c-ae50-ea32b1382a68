<?php

declare(strict_types=1);

return [
    'accepted'             => '你必须接受 :attribute.',
    'accepted_if'          => '你必须接受 :attribute, 当 :other 根据 :value.',
    'active_url'           => '字段 :attribute 必须是有效的网址地址.',
    'after'                => '字段 :attribute 应该是在某个日期之后 :date.',
    'after_or_equal'       => '字段 :attribute 应该是在日期之后或者等于该日期 :date.',
    'alpha'                => '字段 :attribute 只能包含字母.',
    'alpha_dash'           => '字段 :attribute 只能包含字母、数字、连字符和下划线.',
    'alpha_num'            => '字段 :attribute 只能包含字母和数字.',
    'array'                => '字段 :attribute 应该是一个数组.',
    'ascii'                => '字段 :attribute 必须只包含单字节的数字-字母组合字符.',
    'before'               => '字段 :attribute 必须是日期之前 :date.',
    'before_or_equal'      => '字段 :attribute должно быть датой до или равной :date.',
    'between'              => [
        'array'   => '字段中的元素数量 :attribute 应该是在 :min - :max.',
        'file'    => '文件大小字段中的值 :attribute 应当在两者之间 :min - :max Кб.',
        'numeric' => '字段 :attribute 应该是在 :min - :max.',
        'string'  => '字段中的字符数量 :attribute 应该是在 :min - :max.',
    ],
    'boolean'              => '字段 :attribute 应该是逻辑类型的.',
    'can'                  => '字段 :attribute 必须是经过授权的.',
    'confirmed'            => '字段 :attribute 与所证实的情况不符.',
    'current_password'     => '错误的密码.',
    'date'                 => '字段 :attribute 应该是一个正确的日期.',
    'date_equals'          => '字段 :attribute 应该有一个与之相等的日期 :date.',
    'date_format'          => '字段 :attribute 应当符合日期的格式要求 :format.',
    'decimal'              => '字段 :attribute 应该有十位数位数.',
    'declined'             => 'Поле :attribute 应当被驳回.',
    'declined_if'          => 'Поле :attribute должно быть отклонено, когда :other равно :value.',
    'different'            => 'Значения полей :attribute и :other должны различаться.',
    'digits'               => 'Количество символов в поле :attribute должно быть равным :digits.',
    'digits_between'       => 'Количество символов в поле :attribute должно быть между :min и :max.',
    'dimensions'           => 'Изображение, указанное в поле :attribute, имеет недопустимые размеры.',
    'distinct'             => 'Значения поля :attribute не должны повторяться.',
    'doesnt_end_with'      => '字段 :attribute не должно заканчиваться одним из следующих: :values.',
    'doesnt_start_with'    => '字段 :attribute не должно начинаться с одного из следующих: :values.',
    'email'                => '字段 :attribute должно быть действительным электронным адресом.',
    'ends_with'            => '字段 :attribute должно заканчиваться одним из следующих: :values',
    'enum'                 => '字段 :attribute некорректно.',
    'exists'               => '字段 :attribute не существует.',
    'extensions'           => '字段文件：属性必须具有以下扩展名之一: :values.',
    'file'                 => '注意 :attribute 必须指定一个文件.',
    'filled'               => '字段 :attribute 必须填满.',
    'gt'                   => [
        'array'   => '字段中的元素数 :attribute 应该有更多 :value.',
        'file'    => '字段中指定的文件大小 :attribute, 应该有更多 :value kb.',
        'numeric' => '字段 :attribute 应该有更多 :value.',
        'string'  => '字段中的字符数 :attribute 应该有更多 :value.',
    ],
    'gte'                  => [
        'array'   => '字段中的元素数 :attribute 应该是一个值或更多.',
        'file'    => '字段中指定的文件大小 :attribute, 应该 :value KB或更多.',
        'numeric' => '字段 :attribute 应该是一个值或更多.',
        'string'  => '字段中的字符数 :attribute 应该是一个值或更多.',
    ],
    'hex_color'            => '字段 :attribute 应该是HEX格式的正确颜色.',
    'image'                => '字段中指定的文件 :attribute, 应该是一个图像.',
    'in'                   => '字段 :attribute 不正确.',
    'in_array'             => '字段 :attribute 必须出席 :other.',
    'integer'              => '字段 :attribute 一定是一个整数.',
    'ip'                   => '字段 :attribute 必须是一个有效的IP地址.',
    'ipv4'                 => '字段 :attribute 必须是一个有效的IPv4地址.',
    'ipv6'                 => '字段 :attribute 必须是一个有效的IPv6地址.',
    'json'                 => '字段 :attribute 必须是JSON字符串.',
    'lowercase'            => '字段 :attribute должно быть в нижнем регистре.',
    'lt'                   => [
        'array'   => '字段中的元素数 :attribute 应该更少 :value.',
        'file'    => '字段中指定的文件大小 :attribute, 应该更少 :value Кб.',
        'numeric' => '字段 :attribute 应该更小е :value.',
        'string'  => '字段中的字符数 :attribute 应该更少 :value.',
    ],
    'lte'                  => [
        'array'   => '字段中的元素数 :attribute 一定是 :value 或更少.',
        'file'    => '字段中指定的文件大小 :attribute, 应该 :value KB或更少.',
        'numeric' => '字段 :attribute 必须等于或小于 :value.',
        'string'  => '字段中的字符数 :attribute 一定是 :value 或更少.',
    ],
    'mac_address'          => '字段 :attribute 应该是正确的MAC地址.',
    'max'                  => [
        'array'   => '字段中的元素数 :attribute 不能超过 :max.',
        'file'    => '字段文件大小 :attribute 不能超过 :max 个.',
        'numeric' => '字段 :attribute 不能再多了 :max.',
        'string'  => '字段值中的字符数 :attribute 不能超过 :max.',
    ],
    'max_digits'           => '字段 :attribute 不能包含更多 :max 数字.',
    'mimes'                => '字段中指定的文件 :attribute, 必须是以下类型之一: :values.',
    'mimetypes'            => '字段中指定的文件 :attribute, 必须是以下类型之一: :values.',
    'min'                  => [
        'array'   => '字段中的元素数 :attribute 至少应该是 :min.',
        'file'    => '字段中指定的文件大小 :attribute, 必须至少 :min Кб.',
        'numeric' => '字段 :attribute 至少应该是 :min.',
        'string'  => '字段中的字符数 :attribute 至少应该是 :min.',
    ],
    'min_digits'           => '字段 :attribute 必须包含至少 :min 个.',
    'missing'              => '字段 :attribute 需要失踪.',
    'missing_if'           => '字段 :attribute 需要失踪, 当 :other 等于 :value.',
    'missing_unless'       => '字段 :attribute 必须缺席 :other 不等于 :value.',
    'missing_with'         => '字段 :attribute 如果没有, 当 :values 表明.',
    'missing_with_all'     => '字段 :attribute 如果全部都指定了，则必须省略 :values.',
    'multiple_of'          => '字段 :attribute 一定是多重的 :value',
    'not_in'               => '字段 :attribute 不正确.',
    'not_regex'            => '字段 :attribute 有错误的格式.',
    'numeric'              => '字段 :attribute 一定是一个数字.',
    'password'             => [
        'letters'       => '字段 :attribute 必须包含至少一个字母.',
        'mixed'         => '字段 :attribute 必须包含至少一个大写字母和一个小写字母.',
        'numbers'       => '字段 :attribute 必须包含至少一个数字.',
        'symbols'       => '字段 :attribute 必须包含至少一个字符.',
        'uncompromised' => '字段 :attribute 在泄露的数据中发现的.拜托, 选择另一个值 :attribute.',
    ],
    'present'              => '字段 :attribute должно быть.',
    'present_if'           => '字段 :attribute должно быть когда :other равно :value.',
    'present_unless'       => '字段 :attribute должно быть, если только :other не равно :value.',
    'present_with'         => '字段 :attribute должно быть когда одно из :values присутствуют.',
    'present_with_all'     => '字段 :attribute должно быть когда все из значений присутствуют: :values.',
    'prohibited'           => '字段 :attribute запрещено.',
    'prohibited_if'        => '字段 :attribute запрещено, когда :other равно :value.',
    'prohibited_unless'    => '字段 :attribute запрещено, если :other не состоит в :values.',
    'prohibits'            => '字段 :attribute запрещает присутствие :other.',
    'regex'                => '字段 :attribute имеет некорректный формат.',
    'required'             => 'Поле :attribute 一定.',
    'required_array_keys'  => '字段中的数组 :attribute 必须要有钥匙: :values',
    'required_if'          => 'Поле :attribute 必须填写, когда :other равно :value.',
    'required_if_accepted' => 'Поле :attribute 一定, когда :other принято.',
    'required_unless'      => 'Поле :attribute 必须填写的条件是 :other не равно :values.',
    'required_with'        => 'Поле :attribute 必须填写的条件是 :values указано.',
    'required_with_all'    => 'Поле :attribute 必须填写的条件是 :values указано.',
    'required_without'     => 'Поле :attribute 必须填写的条件是 :values не указано.',
    'required_without_all' => 'Поле :attribute 必须填写，当没有任何一项满足条件时 :values не указано.',
    'same'                 => 'Значения полей :attribute и :other должны совпадать.',
    'size'                 => [
        'array'   => 'Количество элементов в поле :attribute должно быть равным :size.',
        'file'    => 'Размер файла, указанный в поле :attribute, должен быть равен :size Кб.',
        'numeric' => '字段 :attribute должно быть равным :size.',
        'string'  => '字段中的字符数 :attribute 一定等于 :size.',
    ],
    'starts_with'          => 'Поле :attribute 必须从以下值之一开始: :values',
    'string'               => '字段 :attribute 应该是一个字符串.',
    'timezone'             => '字段 :attribute 必须是一个有效的时区.',
    'ulid'                 => '字段 :attribute должно быть корректным ULID.',
    'unique'               => '字段 :attribute 已经存在.',
    'uploaded'             => '从字段下载文件 :attribute не удалась.',
    'uppercase'            => '字段 :attribute должно быть в верхнем регистре.',
    'url'                  => '字段 :attribute 有错误的格式 URL.',
    'uuid'                 => '字段 :attribute 一定是正确的 UUID.',
    'attributes'           => [
        'address'                  => '地址',
        'affiliate_url'            => '伙伴链接',
        'age'                      => '年龄',
        'amount'                   => '数量',
        'area'                     => '区域',
        'available'                => '可用',
        'birthday'                 => '出生日期',
        'body'                     => '内容',
        'city'                     => '城市',
        'content'                  => '内容',
        'country'                  => '国家',
        'created_at'               => '成立',
        'creator'                  => '创造者',
        'currency'                 => '货币',
        'current_password'         => '当前的密码',
        'customer'                 => '客户',
        'date'                     => '日期',
        'date_of_birth'            => '生日',
        'day'                      => '白天',
        'deleted_at'               => '删除',
        'description'              => '描述',
        'district'                 => '县',
        'duration'                 => '持续时间',
        'email'                    => '电子邮件',
        'excerpt'                  => '摘录',
        'filter'                   => '渗入',
        'first_name'               => '名字',
        'gender'                   => '性别',
        'group'                    => '团体',
        'hour'                     => '小时',
        'image'                    => '图片',
        'is_subscribed'            => '签署',
        'items'                    => '物品',
        'last_name'                => '姓',
        'lesson'                   => '课程',
        'line_address_1'           => 'строка адреса 1',
        'line_address_2'           => 'строка адреса 2',
        'message'                  => '信息',
        'middle_name'              => '中间名',
        'minute'                   => '分钟',
        'mobile'                   => '移动',
        'month'                    => '月',
        'name'                     => '名字',
        'national_code'            => '国家代码',
        'number'                   => '号码',
        'password'                 => '密码',
        'password_confirmation'    => '确认密码',
        'phone'                    => '电话号码',
        'photo'                    => '照片',
        'postal_code'              => '邮政编码',
        'preview'                  => '预览',
        'price'                    => '价格',
        'product_id'               => 'ID 产品',
        'product_uid'              => 'UID 产品',
        'product_uuid'             => 'UUID 产品',
        'promo_code'               => 'промокод',
        'province'                 => '省',
        'quantity'                 => '数量',
        'recaptcha_response_field' => '验证码错误',
        'remember'                 => '记住',
        'restored_at'              => '恢复于',
        'result_text_under_image'  => '图片下方的文字',
        'role'                     => '角色',
        'second'                   => '秒',
        'sex'                      => '性别',
        'shipment'                 => '交货',
        'short_text'               => '简短的描述',
        'size'                     => '大小',
        'state'                    => '圣保罗',
        'street'                   => '街道',
        'student'                  => '学生',
        'subject'                  => '标题',
        'teacher'                  => '老师',
        'terms'                    => '规则',
        'test_description'         => '测试描述',
        'test_locale'              => '测试本地化',
        'test_name'                => '测试他们',
        'text'                     => '发短信',
        'time'                     => '时间',
        'title'                    => '名称',
        'updated_at'               => '在更新',
        'user'                     => '用户',
        'username'                 => '用户名',
        'year'                     => '年',
    ],
];
