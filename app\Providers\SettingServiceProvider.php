<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class SettingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Проверка на существование таблицы перед загрузкой настроек
//        if (Schema::hasTable('settings')) {
//            $settings = Setting::all();
//            $configSettings = [];
//
//            foreach ($settings as $setting) {
//                if ($setting->type == 'json') {
//                    $configSettings[$setting->key] = [
//                        'value' => json_decode($setting->value, true),
//                        'name' => $setting->name,
//                        'description' => $setting->description
//                    ];
//                    Config::set('app.' . $setting->key, json_decode($setting->value, true));
//                } else {
//                    $configSettings[$setting->key] = [
//                        'value' => $setting->value,
//                        'name' => $setting->name,
//                        'description' => $setting->description
//                    ];
//                    Config::set('app.' . $setting->key, $setting->value);
//                }
//            }
//
//            \Log::info('Loaded settings from database:', $configSettings);
//
//            if (config('app.env') === 'local') {
//                //dd($configSettings);
//            }
//        }
    }
}
