@extends('template::layouts.auth')
@section('title', __('Recover Password'))

@section('content')
    <div class="form-group group-text">
        <p>{{ __('Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}</p>
    </div>

    @if(session('status'))
        <div class="alert alert-success">
            <p>{{ session('status') }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif

    <form method="POST" action="{{ route('password.email') }}">
        @csrf
        <div class="form-group">
            <input id="email" class="form-control" type="email" name="email" placeholder="{{ __('Your Email') }}" required autofocus />
            @foreach ($errors->get('password') as $error)
                <div class="text-danger">{{ $error }}</div>
            @endforeach
        </div>
        <div class="form-group login_button">
            <button type="submit" class="btn-primary"><span>{{ __('Reset Password') }}</span></button>
        </div>
        <div class="form-group text-center">
            <a class="btn-gray-no-border" href="{{ route('login') }}">
                {{ __('Already registered?') }}
            </a>
        </div>
    </form>
@endsection
