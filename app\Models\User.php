<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail, FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $connection = 'mysql';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'balance',
        'password',
        'last_unstuck_expired',
        'referral_code',
        'can_use_referral',
        'sync_mmoweb'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return str_ends_with($this->email, '@admin.com');
        }

        return true;
    }

    public function referrals()
    {
        return $this->hasMany(Referral::class, 'referrer_id');
    }

    public function referredBy()
    {
        return $this->belongsToMany(User::class, 'referrals', 'referred_id', 'referrer_id');
    }

    public function generateReferralCode()
    {
        if (!$this->referral_code) {
            $this->referral_code = strtolower($this->name) . rand(100, 999);
            $this->save();
        }
    }

    public function canUseReferral(): bool
    {
        return config('app.referral_system_enabled') || $this->can_use_referral;
    }

    public function referralPayments()
    {
        return $this->hasMany(ReferralBonus::class, 'referrer_id');
    }
}
