<!-- nav left -->
@if(request()->routeIs('main-page'))
    <div id="sectionNav" class="section-nav">
        <div class="section-nav__item section-nav__item--active" data-section="home">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg"
                 class="section-nav__icon">
                <path
                    d="M11.1226 3.6882C11.502 2.99466 12.498 2.99466 12.8774 3.6882L15.0842 7.72326C15.3595 8.22663 15.7734 8.6405 16.2767 8.9158L20.3118 11.1226C21.0053 11.502 21.0053 12.498 20.3118 12.8774L16.2767 15.0842C15.7734 15.3595 15.3595 15.7734 15.0842 16.2767L15.9616 16.7566L15.0842 16.2767L12.8774 20.3118C12.498 21.0053 11.502 21.0053 11.1226 20.3118L8.9158 16.2767C8.6405 15.7734 8.22663 15.3595 7.72326 15.0842L7.24342 15.9616L7.72326 15.0842L3.6882 12.8774C2.99466 12.498 2.99466 11.502 3.6882 11.1226L7.72326 8.9158C8.22663 8.6405 8.6405 8.22663 8.9158 7.72326L8.03844 7.24342L8.9158 7.72326L11.1226 3.6882Z"
                    stroke="white" stroke-width="2" />
            </svg>
            <span class="section-nav__text">Начать игру</span>
        </div>

        <div class="section-nav__item" data-section="features">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="section-nav__icon">
                <path
                    d="M11.1226 3.6882C11.502 2.99466 12.498 2.99466 12.8774 3.6882L15.0842 7.72326C15.3595 8.22663 15.7734 8.6405 16.2767 8.9158L20.3118 11.1226C21.0053 11.502 21.0053 12.498 20.3118 12.8774L16.2767 15.0842C15.7734 15.3595 15.3595 15.7734 15.0842 16.2767L15.9616 16.7566L15.0842 16.2767L12.8774 20.3118C12.498 21.0053 11.502 21.0053 11.1226 20.3118L8.9158 16.2767C8.6405 15.7734 8.22663 15.3595 7.72326 15.0842L7.24342 15.9616L7.72326 15.0842L3.6882 12.8774C2.99466 12.498 2.99466 11.502 3.6882 11.1226L7.72326 8.9158C8.22663 8.6405 8.6405 8.22663 8.9158 7.72326L8.03844 7.24342L8.9158 7.72326L11.1226 3.6882Z"
                    stroke="white" stroke-width="2" />
            </svg>
            <span class="section-nav__text">Наши особенности</span>
        </div>

        <div class="section-nav__item" data-section="news">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="section-nav__icon">
                <path
                    d="M11.1226 3.6882C11.502 2.99466 12.498 2.99466 12.8774 3.6882L15.0842 7.72326C15.3595 8.22663 15.7734 8.6405 16.2767 8.9158L20.3118 11.1226C21.0053 11.502 21.0053 12.498 20.3118 12.8774L16.2767 15.0842C15.7734 15.3595 15.3595 15.7734 15.0842 16.2767L15.9616 16.7566L15.0842 16.2767L12.8774 20.3118C12.498 21.0053 11.502 21.0053 11.1226 20.3118L8.9158 16.2767C8.6405 15.7734 8.22663 15.3595 7.72326 15.0842L7.24342 15.9616L7.72326 15.0842L3.6882 12.8774C2.99466 12.498 2.99466 11.502 3.6882 11.1226L7.72326 8.9158C8.22663 8.6405 8.6405 8.22663 8.9158 7.72326L8.03844 7.24342L8.9158 7.72326L11.1226 3.6882Z"
                    stroke="white" stroke-width="2" />
            </svg>
            <span class="section-nav__text">Новости и акции</span>
        </div>

        <div class="section-nav__item" data-section="forum">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="section-nav__icon">
                <path
                    d="M11.1226 3.6882C11.502 2.99466 12.498 2.99466 12.8774 3.6882L15.0842 7.72326C15.3595 8.22663 15.7734 8.6405 16.2767 8.9158L20.3118 11.1226C21.0053 11.502 21.0053 12.498 20.3118 12.8774L16.2767 15.0842C15.7734 15.3595 15.3595 15.7734 15.0842 16.2767L15.9616 16.7566L15.0842 16.2767L12.8774 20.3118C12.498 21.0053 11.502 21.0053 11.1226 20.3118L8.9158 16.2767C8.6405 15.7734 8.22663 15.3595 7.72326 15.0842L7.24342 15.9616L7.72326 15.0842L3.6882 12.8774C2.99466 12.498 2.99466 11.502 3.6882 11.1226L7.72326 8.9158C8.22663 8.6405 8.6405 8.22663 8.9158 7.72326L8.03844 7.24342L8.9158 7.72326L11.1226 3.6882Z"
                    stroke="white" stroke-width="2" />
            </svg>
            <span class="section-nav__text">Темы с форума</span>
        </div>

        <div class="section-nav__item" data-section="rating">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="section-nav__icon">
                <path
                    d="M11.1226 3.6882C11.502 2.99466 12.498 2.99466 12.8774 3.6882L15.0842 7.72326C15.3595 8.22663 15.7734 8.6405 16.2767 8.9158L20.3118 11.1226C21.0053 11.502 21.0053 12.498 20.3118 12.8774L16.2767 15.0842C15.7734 15.3595 15.3595 15.7734 15.0842 16.2767L15.9616 16.7566L15.0842 16.2767L12.8774 20.3118C12.498 21.0053 11.502 21.0053 11.1226 20.3118L8.9158 16.2767C8.6405 15.7734 8.22663 15.3595 7.72326 15.0842L7.24342 15.9616L7.72326 15.0842L3.6882 12.8774C2.99466 12.498 2.99466 11.502 3.6882 11.1226L7.72326 8.9158C8.22663 8.6405 8.6405 8.22663 8.9158 7.72326L8.03844 7.24342L8.9158 7.72326L11.1226 3.6882Z"
                    stroke="white" stroke-width="2" />
            </svg>
            <span class="section-nav__text">Рейтинг</span>
        </div>
    </div>
@endif

<!-- header -->
<header class="header ">
    @if(request()->routeIs('main-page'))
        <!-- date -->
        <div class="header__countdown">
            <div class="header__countdown-container">
                <div class="countdown_info">
                    <div class="header__countdown-title-container">
                        <span class="header__countdown-title">Старт сервера</span>
                        <span class="header__countdown-date" data-countdown-date>24.05.25 20:00</span>
                    </div>
                    <div class="header__countdown-timer">
                        <span class="header__countdown-unit header__countdown-days" data-countdown="days">28</span>
                        <svg width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg"
                             class="header__countdown-separator">
                            <path
                                d="M2.18463 0.604179C2.56395 -0.0893653 3.56003 -0.0893649 3.93934 0.60418L4.35758 1.36889C4.44934 1.53668 4.5873 1.67463 4.75509 1.7664L5.5198 2.18463C6.21334 2.56395 6.21334 3.56003 5.5198 3.93934L4.75509 4.35758C4.5873 4.44934 4.44934 4.5873 4.35758 4.75509L3.93934 5.5198C3.56003 6.21334 2.56395 6.21334 2.18463 5.5198L1.7664 4.75509C1.67463 4.5873 1.53668 4.44934 1.36889 4.35758L0.604179 3.93934C-0.0893653 3.56003 -0.0893649 2.56395 0.60418 2.18463L1.36889 1.7664C1.53668 1.67463 1.67463 1.53668 1.7664 1.36889L2.18463 0.604179Z"
                                fill="white" />
                        </svg>
                        <span class="header__countdown-unit header__countdown-hours" data-countdown="hours">14</span>
                        <svg width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg"
                             class="header__countdown-separator">
                            <path
                                d="M2.18463 0.604179C2.56395 -0.0893653 3.56003 -0.0893649 3.93934 0.60418L4.35758 1.36889C4.44934 1.53668 4.5873 1.67463 4.75509 1.7664L5.5198 2.18463C6.21334 2.56395 6.21334 3.56003 5.5198 3.93934L4.75509 4.35758C4.5873 4.44934 4.44934 4.5873 4.35758 4.75509L3.93934 5.5198C3.56003 6.21334 2.56395 6.21334 2.18463 5.5198L1.7664 4.75509C1.67463 4.5873 1.53668 4.44934 1.36889 4.35758L0.604179 3.93934C-0.0893653 3.56003 -0.0893649 2.56395 0.60418 2.18463L1.36889 1.7664C1.53668 1.67463 1.67463 1.53668 1.7664 1.36889L2.18463 0.604179Z"
                                fill="white" />
                        </svg>
                        <span class="header__countdown-unit header__countdown-minutes" data-countdown="minutes">38</span>
                        <svg width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg"
                             class="header__countdown-separator">
                            <path
                                d="M2.18463 0.604179C2.56395 -0.0893653 3.56003 -0.0893649 3.93934 0.60418L4.35758 1.36889C4.44934 1.53668 4.5873 1.67463 4.75509 1.7664L5.5198 2.18463C6.21334 2.56395 6.21334 3.56003 5.5198 3.93934L4.75509 4.35758C4.5873 4.44934 4.44934 4.5873 4.35758 4.75509L3.93934 5.5198C3.56003 6.21334 2.56395 6.21334 2.18463 5.5198L1.7664 4.75509C1.67463 4.5873 1.53668 4.44934 1.36889 4.35758L0.604179 3.93934C-0.0893653 3.56003 -0.0893649 2.56395 0.60418 2.18463L1.36889 1.7664C1.53668 1.67463 1.67463 1.53668 1.7664 1.36889L2.18463 0.604179Z"
                                fill="white" />
                        </svg>
                        <span class="header__countdown-unit header__countdown-seconds" data-countdown="seconds">57</span>
                    </div>
                </div>
            </div>
            <button class="header__countdown-toggle">
                <svg width="20" height="20" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="header__countdown-toggle-icon">
                    <path
                        d="M9.91666 7.82264L7.41416 5.34931C7.35993 5.29463 7.29541 5.25124 7.22433 5.22162C7.15324 5.19201 7.077 5.17676 6.99999 5.17676C6.92298 5.17676 6.84674 5.19201 6.77565 5.22162C6.70457 5.25124 6.64005 5.29463 6.58582 5.34931L4.11249 7.82264C4.05782 7.87687 4.01442 7.94139 3.9848 8.01247C3.95519 8.08355 3.93994 8.1598 3.93994 8.23681C3.93994 8.31381 3.95519 8.39006 3.9848 8.46114C4.01442 8.53223 4.05782 8.59675 4.11249 8.65097C4.22179 8.75962 4.36963 8.8206 4.52374 8.8206C4.67785 8.8206 4.8257 8.75962 4.93499 8.65097L6.99999 6.58597L9.06499 8.65097C9.17364 8.75874 9.32029 8.8195 9.47332 8.82014C9.55009 8.82058 9.6262 8.80587 9.69727 8.77684C9.76834 8.74781 9.83298 8.70504 9.88749 8.65097C9.94412 8.5987 9.98982 8.53572 10.022 8.46567C10.0541 8.39563 10.0721 8.31991 10.0748 8.24289C10.0775 8.16587 10.0649 8.08907 10.0378 8.01694C10.0106 7.94481 9.96947 7.87877 9.91666 7.82264Z"
                        fill="white" />
                </svg>
            </button>
        </div>
    @endif
    <div class="header_container">
        @if(request()->routeIs('main-page'))
            @include('template::server_status')
        @endif

        <a class="header__logo-link" href="/">
            <button id="logoButton" class="header__logo-button group">
                <img class="header__logo-image" src="{{ base_url('./img/logo-small.svg') }}" alt="Logo" draggable="false" />
            </button>
        </a>

        <nav class="header__nav">
            <ul class="header__nav-list">
                <li>
                    <a href="{{ route('main-page') }}" class="header__nav-item">
                        <img class="header__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="header__nav-link" href="{{ route('main-page') }}">{{ __('Home') }}</span>
                    </a>
                </li>

                <li>
                    <a href="{{ route('about') }}" class="header__nav-item">
                        <img class="header__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="header__nav-link" href="/knowledge.html">{{ __('About') }}</span>
                    </a>
                </li>

                <li>
                    <a href="#" class="header__nav-item">
                        <img class="header__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="header__nav-link" href="/">{{ __('Forum') }}</span>
                    </a>
                </li>

                <li>
                    <a href="{{ route('download') }}" class="header__nav-item">
                        <img class="header__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="header__nav-link" href="/">{{ __('Files') }}</span>
                    </a>
                </li>

                <li>
                    <a href="#" class="header__nav-item">
                        <img class="header__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="header__nav-link" href="/">{{ __('Support') }}</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="header__right">
            @include('template::lang')

            <a href="{{ route('donate') }}" class="header__donation">
                <img class="header__donation-icon"
                     src="data:image/svg+xml,%3csvg%20width='46'%20height='46'%20viewBox='0%200%2046%2046'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20.3679%204.81254C21.5059%202.73191%2024.4941%202.73191%2025.6321%204.81254L30.7101%2014.0974C30.9854%2014.6007%2031.3993%2015.0146%2031.9026%2015.2899L41.1875%2020.3679C43.2681%2021.5059%2043.2681%2024.4941%2041.1875%2025.6321L31.9026%2030.7101C31.3993%2030.9854%2030.9854%2031.3993%2030.7101%2031.9026L25.6321%2041.1875C24.4941%2043.2681%2021.5059%2043.2681%2020.3679%2041.1875L15.2899%2031.9026C15.0146%2031.3993%2014.6007%2030.9854%2014.0974%2030.7101L4.81254%2025.6321C2.73191%2024.4941%202.73191%2021.5059%204.81254%2020.3679L14.0974%2015.2899C14.6007%2015.0146%2015.0146%2014.6007%2015.2899%2014.0974L20.3679%204.81254Z'%20fill='url(%23paint0_linear_2261_781)'/%3e%3cpath%20d='M27.1666%2031.3335H18.8333C18.4916%2031.3335%2018.2083%2031.0502%2018.2083%2030.7085C18.2083%2030.3668%2018.4916%2030.0835%2018.8333%2030.0835H27.1666C27.5083%2030.0835%2027.7916%2030.3668%2027.7916%2030.7085C27.7916%2031.0502%2027.5083%2031.3335%2027.1666%2031.3335Z'%20fill='white'/%3e%3cpath%20d='M29.9583%2017.6002L26.625%2019.9835C26.1833%2020.3002%2025.55%2020.1085%2025.3583%2019.6002L23.7833%2015.4002C23.5167%2014.6752%2022.4917%2014.6752%2022.225%2015.4002L20.6417%2019.5919C20.45%2020.1085%2019.825%2020.3002%2019.3833%2019.9752L16.05%2017.5919C15.3833%2017.1252%2014.5%2017.7835%2014.775%2018.5585L18.2417%2028.2669C18.3583%2028.6002%2018.675%2028.8169%2019.025%2028.8169H26.9667C27.3167%2028.8169%2027.6333%2028.5919%2027.75%2028.2669L31.2167%2018.5585C31.5%2017.7835%2030.6167%2017.1252%2029.9583%2017.6002ZM25.0833%2025.2919H20.9167C20.575%2025.2919%2020.2917%2025.0085%2020.2917%2024.6669C20.2917%2024.3252%2020.575%2024.0419%2020.9167%2024.0419H25.0833C25.425%2024.0419%2025.7083%2024.3252%2025.7083%2024.6669C25.7083%2025.0085%2025.425%2025.2919%2025.0833%2025.2919Z'%20fill='white'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_2261_781'%20x1='23'%20y1='0'%20x2='23'%20y2='46'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23263538'/%3e%3cstop%20offset='1'%20stop-color='%23359EA6'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e"
                     alt="Icon" draggable="false" />
                <span class="header__donation-link">{{ __('Donate') }}</span>
            </a>

            @if(auth()->check())
                <a href="{{ route('dashboard') }}" class="header__cabinet-button bordered-button">
                    <span class="header__cabinet-text">{{ __('Hello!') }} {{ Auth::user()->name }}</span>
                </a>
            @else
                <a href="{{ route('login') }}" class="header__cabinet-button bordered-button">
                    <span class="header__cabinet-text">{{ __('Cabinet') }}</span>
                </a>
            @endif
        </div>

        <button class="header__burger-button">
            <span class="header__burger-line"></span>
            <span class="header__burger-line"></span>
            <span class="header__burger-line"></span>
        </button>
    </div>
</header>
<!-- Mobile menu -->
<div class="mobile-menu">
    <div class="mobile-menu__container">
        <div class="mobile-menu__header">
            <a class="mobile-menu__logo-link" href="/">
                <img class="mobile-menu__logo" src="{{ base_url('./img/logo-small.svg') }}" alt="Logo" draggable="false" />
            </a>
            <button class="mobile-menu__close-button">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </button>
        </div>

        <nav class="mobile-menu__nav">
            <ul class="mobile-menu__nav-list">
                <li>
                    <a href="{{ route('main-page') }}" class="mobile-menu__nav-item">
                        <img class="mobile-menu__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="mobile-menu__nav-link">{{ __('Home') }}</span>
                    </a>
                </li>

                <li>
                    <a href="{{ route('about') }}" class="mobile-menu__nav-item">
                        <img class="mobile-menu__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="mobile-menu__nav-link">{{ __('About') }}</span>
                    </a>
                </li>

                <li>
                    <a href="/" class="mobile-menu__nav-item">
                        <img class="mobile-menu__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="mobile-menu__nav-link">{{ __('Forum') }}</span>
                    </a>
                </li>

                <li>
                    <a href="{{ route('download') }}" class="mobile-menu__nav-item">
                        <img class="mobile-menu__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="mobile-menu__nav-link">{{ __('Files') }}</span>
                    </a>
                </li>

                <li>
                    <a href="/" class="mobile-menu__nav-item">
                        <img class="mobile-menu__nav-icon" src="{{ base_url('./img/full-star.svg') }}" alt="Icon" draggable="false" />
                        <span class="mobile-menu__nav-link">{{ __('Support') }}</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="mobile-menu__footer">
            <div class="mobile-menu__donation">
                <img class="mobile-menu__donation-icon"
                     src="data:image/svg+xml,%3csvg%20width='46'%20height='46'%20viewBox='0%200%2046%2046'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20.3679%204.81254C21.5059%202.73191%2024.4941%202.73191%2025.6321%204.81254L30.7101%2014.0974C30.9854%2014.6007%2031.3993%2015.0146%2031.9026%2015.2899L41.1875%2020.3679C43.2681%2021.5059%2043.2681%2024.4941%2041.1875%2025.6321L31.9026%2030.7101C31.3993%2030.9854%2030.9854%2031.3993%2030.7101%2031.9026L25.6321%2041.1875C24.4941%2043.2681%2021.5059%2043.2681%2020.3679%2041.1875L15.2899%2031.9026C15.0146%2031.3993%2014.6007%2030.9854%2014.0974%2030.7101L4.81254%2025.6321C2.73191%2024.4941%202.73191%2021.5059%204.81254%2020.3679L14.0974%2015.2899C14.6007%2015.0146%2015.0146%2014.6007%2015.2899%2014.0974L20.3679%204.81254Z'%20fill='url(%23paint0_linear_2261_781)'/%3e%3cpath%20d='M27.1666%2031.3335H18.8333C18.4916%2031.3335%2018.2083%2031.0502%2018.2083%2030.7085C18.2083%2030.3668%2018.4916%2030.0835%2018.8333%2030.0835H27.1666C27.5083%2030.0835%2027.7916%2030.3668%2027.7916%2030.7085C27.7916%2031.0502%2027.5083%2031.3335%2027.1666%2031.3335Z'%20fill='white'/%3e%3cpath%20d='M29.9583%2017.6002L26.625%2019.9835C26.1833%2020.3002%2025.55%2020.1085%2025.3583%2019.6002L23.7833%2015.4002C23.5167%2014.6752%2022.4917%2014.6752%2022.225%2015.4002L20.6417%2019.5919C20.45%2020.1085%2019.825%2020.3002%2019.3833%2019.9752L16.05%2017.5919C15.3833%2017.1252%2014.5%2017.7835%2014.775%2018.5585L18.2417%2028.2669C18.3583%2028.6002%2018.675%2028.8169%2019.025%2028.8169H26.9667C27.3167%2028.8169%2027.6333%2028.5919%2027.75%2028.2669L31.2167%2018.5585C31.5%2017.7835%2030.6167%2017.1252%2029.9583%2017.6002ZM25.0833%2025.2919H20.9167C20.575%2025.2919%2020.2917%2025.0085%2020.2917%2024.6669C20.2917%2024.3252%2020.575%2024.0419%2020.9167%2024.0419H25.0833C25.425%2024.0419%2025.7083%2024.3252%2025.7083%2024.6669C25.7083%2025.0085%2025.425%2025.2919%2025.0833%2025.2919Z'%20fill='white'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_2261_781'%20x1='23'%20y1='0'%20x2='23'%20y2='46'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23263538'/%3e%3cstop%20offset='1'%20stop-color='%23359EA6'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e"
                     alt="Icon" draggable="false" />
                <a href="{{ route('donate') }}" class="mobile-menu__donation-link">{{ __('Donate') }}</a>
            </div>
            <button class="mobile-menu__cabinet-button bordered-button" onclick="location.href='{{ route('login') }}'">
                <span class="mobile-menu__cabinet-text">{{ __('Cabinet') }}</span>
            </button>

        </div>
    </div>
</div>
