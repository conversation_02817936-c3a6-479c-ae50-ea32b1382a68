<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referral extends Model
{
    use HasFactory;

    protected $fillable = ['referrer_id', 'referred_id', 'referral_code'];

// Связь с пригласившим пользователем
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    // Связь с приглашенным пользователем
    public function referred()
    {
        return $this->belongsTo(User::class, 'referred_id');
    }

    public function payments()
    {
        return $this->hasMany(ReferralBonus::class, 'referrer_id', 'referrer_id');
    }
}
