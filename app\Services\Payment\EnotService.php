<?php

namespace App\Services\Payment;

use App\Services\Payment\PaymentProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;
use Stripe\Checkout\Session;

class EnotService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;
    }

    public function createPayment($payment_id, $count)
    {
        try {
            $currency = config('app.pay_system.enot_currency'); // Валюта
            $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
            // Расчет суммы платежа
            $sum = (int)ceil($count * $coin_price);

            $customFieldsData = ['payment_id' => $payment_id];

            $data = [
                'currency' => $currency,
                'amount' => $sum,
                'order_id' => $payment_id,
                'shop_id' => config('app.pay_system.enot_shop_id'),
                'custom_fields' => json_encode($customFieldsData),
            ];

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'x-api-key' => config('app.pay_system.enot_api_key'),
            ])->post('https://api.mivion.com/invoice/create', $data);

            if ($response->successful()) {
                $parsedResult = $response->json();
                return $parsedResult['data']['url'] ?? null;
            }

            Log::error('Enot: Error creating payment', ['response' => $response->body()]);
            throw new Exception('Failed to create Enot payment.');
        } catch (\Exception $e) {
            Log::error('Enot: Error creating payment', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function handleWebhook(Request $request)
    {
        try {
            // Проверка IP-адреса откуда идет вебхук
//            if (!$this->isAllowedIp($request->ip())) {
//                Log::warning('FreeKassa: Access denied for IP', ['ip' => $request->ip()]);
//                return response('Access Denied', 403);
//            }

            $request->validate([
                'amount' => 'required',
                'order_id' => 'required',
                'custom_fields' => 'required',
                'status' => 'required',
            ]);

            $rawData = $request->method() === 'GET' ? $request->query() : json_decode($request->getContent(), true);

            $custom_fields = is_array($rawData['custom_fields']) ? $rawData['custom_fields'] : json_decode($rawData['custom_fields'], true);
            $payStatus = $rawData['status'];

            if ($payStatus !== 'success') {
                Log::warning('Enot: Payment status not successful', $rawData);
                return response('Payment status not successful', 400);
            }

            $requiredFields = ['payment_id', 'count', 'selected_server'];
            foreach ($requiredFields as $field) {
                if (!isset($custom_fields[$field])) {
                    Log::warning("Enot: Missing required field: $field");
                    return response('Missing required fields', 400);
                }
            }

            $payment_id = $custom_fields['payment_id'];

            $this->paymentProcessingService->processPayment($payment_id);

            return response('OK', 200);
        } catch (Exception $e) {
            Log::error('Enot: Error handling webhook', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }

    // разрешенные IP для отправки для отправки вебхука
    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '************',
        ];

        return in_array($ip, $allowedIps);
    }
}
