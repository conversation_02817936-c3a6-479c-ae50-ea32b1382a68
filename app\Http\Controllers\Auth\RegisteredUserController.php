<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Referral;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        // Проверяем наличие реферального кода в запросе и сохраняем его в сессии
        if (request()->has('ref')) {
            session(['referral_code' => request()->query('ref')]);
        }

        return view('template::auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'min:5', 'max:16', 'unique:mysql.users,name'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:mysql.users,email'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        // Проверка наличия пользователя в таблице 'users'
        $existingUser = User::where('name', $request->name)->first();

        if ($existingUser) {
            return redirect()->back()->withErrors(['message' => 'Пользователь с таким аккаунтом уже существует.']);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        if ($user->can_use_referral) {
            $user->generateReferralCode();
        }

        $referralCode = session('referral_code');

        if ($referralCode) {
            $referrer = User::where('referral_code', $referralCode)->first();

            if ($referrer && $referrer->id !== $user->id) {
                Referral::create([
                    'referrer_id' => $referrer->id,
                    'referred_id' => $user->id,
                ]);
            }
        }

        // система игрового аккаунта
        if (config('app.game_account_system')) {
            // Проверка наличия аккаунта в таблице 'accounts'
            $existingAccount = DB::connection('loginserver')
                ->table('accounts')
                ->where('login', $request->name)
                ->first();

            // Если аккаунт уже существует, можно принять решение, например, вернуть ошибку или выполнить другие действия
            if ($existingAccount) {
                return redirect()->back()->withErrors(['message' => 'Аккаунт с таким именем уже существует.']);
            }

            $password = hashPassword($request->password);
            DB::connection('loginserver')
                ->table('accounts')
                ->insert([
                    'login' => $request->name,
                    'password' => $password,
                    'ma_id' => $user->id,
                ]);
        }

        // Отправляем письмо только если активация включена
        if (config('app.email_verification')) {
            event(new Registered($user));
            Auth::login($user);
            return redirect('/dashboard')->with('status', '注册成功!');
        }

        // Если активация выключена, сразу логиним пользователя
        Auth::login($user);
        return redirect('/dashboard')->with('status', '注册成功!');
    }
}
