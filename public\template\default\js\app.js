console.log(
    `%c🛡️ Ты не пройдёшь!`,
    'color: crimson; font-size: 20px; font-weight: bold; text-shadow: 1px 1px black;'
);
console.log(
    `%cЭта страница запечатана великими чарами!`,
    'color: goldenrod; font-size: 16px; font-style: italic;'
);
console.log(
    `%cЯ пошлю тебя на %cishkhaqwi ai durugnul`,
    'color: white; font-size: 14px;',
    'color: goldenrod; font-weight: bold;'
);
console.log(
    `%cЭтот путь проложен: %chttps://lin2web.com/en`,
    'color: gray; font-size: 13px;',
    'color: cyan; font-weight: bold; text-decoration: underline;'
);

//------------------------------------------------------------------------------------------ //
// LANGUAGES
//------------------------------------------------------------------------------------------ //

document.addEventListener('DOMContentLoaded', () => {
    let currentLangItem = document.querySelector('.nav__langs-item--current');
    let langsStroke = document.querySelector('.nav__langs-stroke');

    if (currentLangItem) {
        currentLangItem.addEventListener('click', function (event) {
            if (window.getComputedStyle(langsStroke).display === 'none') {
                langsStroke.style.display = 'block';
                currentLangItem.classList.add('active');
            } else {
                langsStroke.style.display = 'none';
                currentLangItem.classList.remove('active');
            }
        });
    }

    document.addEventListener('mouseup', function (e) {
        if (
            currentLangItem &&
            langsStroke &&
            !currentLangItem.isEqualNode(e.target) &&
            !currentLangItem.contains(e.target) &&
            !langsStroke.isEqualNode(e.target) &&
            !langsStroke.contains(e.target)
        ) {
            currentLangItem.classList.remove('active');
            if (window.getComputedStyle(langsStroke).display === 'block') {
                langsStroke.style.display = 'none';
            }
        }
    });
});


//------------------------------------------------------------------------------------------ //
// LINKS
//------------------------------------------------------------------------------------------ //
//
// let navSwitch = document.querySelector('.nav__switch');
// let navLinks = document.querySelector('.nav__links');
//
// navSwitch.addEventListener('click', function (event) {
//     if (navSwitch.classList.contains('active')) {
//         navSwitch.classList.remove('active');
//         navLinks.classList.remove('active');
//     } else {
//         navSwitch.classList.add('active');
//         navLinks.classList.add('active');
//     }
// });
//
// document.addEventListener('mouseup', function (e) {
//     if (
//         !navSwitch.isEqualNode(e.target) &&
//         !navSwitch.contains(e.target) &&
//         !navLinks.isEqualNode(e.target) &&
//         !navLinks.contains(e.target)
//     ) {
//         navSwitch.classList.remove('active');
//         navLinks.classList.remove('active');
//     }
// });

// PAYMENT RANGE
document.addEventListener('DOMContentLoaded', () => {
    let paymentRange = document.getElementById('payment-range');
    const countInput = document.querySelector('input[name="count"]'); // Поле с количеством токенов
    const currencyItems = document.querySelectorAll('.currency'); // Элементы для валют
    const coinPrice = window.coinPrice; // Цена за 1 токен
    const currencyRate = window.currencyRate; // Коэффициенты валют

    // Функция обновления валют
    const updatePrices = () => {
        if (!countInput) {
            //console.error('Поле countInput не найдено');
            return;
        }

        const count = parseInt(countInput.value) || 0; // Количество токенов
        currencyItems.forEach(item => {
            const currency = item.dataset.currency; // Валюта (USD, EUR, RUB)
            const pricePerCoin = coinPrice[currency] || 1; // Цена за токен
            const rate = currencyRate[currency] || 1; // Коэффициент
            const total = count * pricePerCoin; // Итоговая цена
            item.innerText = total.toFixed(2); // Обновляем текст
        });
    };


    if (paymentRange) {
        noUiSlider.create(paymentRange, {
            start: 10,
            connect: [true, false],
            tooltips: true,
            range: {
                'min': 0,
                '20%': 100,
                '40%': 200,
                '60%': 300,
                '80%': 400,
                'max': 500
            },
            step: 1,
            format: {
                to: value => parseInt(value),
                from: value => value
            },
            pips: {
                mode: 'range',
                density: 5
            }
        });

        const updatePayment = () => {
            let count = parseInt(paymentRange.noUiSlider.get());
            let price = parseFloat(document.querySelector('.price_col').innerText);
            let countInput = document.getElementById('count');
            let bonusBox = document.getElementById('payment-bonus');
            let totalBox = document.getElementById('payment-total');

            if (isNaN(count)) {
                count = 0;
            }

            countInput.value = count; // Обновляем поле количества
            let donateResult = count * price;

            // Обновляем бонус
            let bonus = calcPaymentBonus(count);
            bonusBox.innerText = bonus;

            // Обновляем общий результат
            totalBox.innerText = count + bonus;

            // Вызываем обновление цен в валютной таблице
            updatePrices();
        };

        paymentRange.noUiSlider.on('update', updatePayment);

        document.getElementById('count').addEventListener('input', () => {
            let count = parseInt(document.getElementById('count').value) || 0;
            paymentRange.noUiSlider.set(count); // Обновляем ползунок
            updatePayment();
        });

        function calcPaymentBonus(sum) {
            const bonusLevels = window.bonusSystemPercent;
            let bonusPercent = 0;

            Object.keys(bonusLevels).forEach(level => {
                if (sum >= parseInt(level)) {
                    bonusPercent = bonusLevels[level].bonus_percent;
                }
            });

            return parseInt(sum * (bonusPercent / 100));
        }
    }

    // Инициализация таблицы цен при загрузке страницы
    updatePrices();
});
// PAYMENT RANGE END

document.addEventListener('DOMContentLoaded', function () {
    const paymentMethods = document.querySelectorAll('.pay_method');
    const hiddenInput = document.getElementById('pay_select');

    paymentMethods.forEach(method => {
        method.addEventListener('click', function () {
            // Убираем active со всех методов
            paymentMethods.forEach(m => m.classList.remove('active'));

            // Добавляем active к текущему методу
            this.classList.add('active');

            // Сохраняем значение в скрытое поле
            hiddenInput.value = this.getAttribute('data-method');
        });
    });
});

// Get all tab buttons and tab contents
document.addEventListener('DOMContentLoaded', () => {
    const tabButtons = document.querySelectorAll('.tablinks');
    const tabContents = document.querySelectorAll('.tabcontent');

    // Проверка, что табы и контент существуют на странице
    if (tabButtons.length === 0 || tabContents.length === 0) {
        return; // Выход из функции, если табов или контента нет
    }

    // Function to show a specific tab content
    function showTab(tabName) {
        // Hide all tab contents with fade-out effect
        tabContents.forEach(tabContent => {
            tabContent.classList.remove('active');
        });

        // Deactivate all tab buttons
        tabButtons.forEach(tabButton => {
            tabButton.classList.remove('active');
        });

        // Show the selected tab content with fade-in effect
        const selectedTabContent = document.getElementById(tabName);
        selectedTabContent.classList.add('active');

        // Activate the corresponding tab button
        document.getElementById(tabName + 'Btn').classList.add('active');
    }

    // Check for default tab
    const defaultTab = document.querySelector('.tablinks[data-default]') || document.getElementById('tab1Btn');
    if (defaultTab) {
        const defaultTabName = defaultTab.id.replace('Btn', '');
        showTab(defaultTabName);
    }

    // Add event listeners to each tab button
    tabButtons.forEach(tabButton => {
        tabButton.addEventListener('click', function() {
            const tabName = this.id.replace('Btn', '');
            showTab(tabName);
        });
    });
});






//
// document.addEventListener("DOMContentLoaded", function() {
//     // Получаем все элементы с классом "characters"
//     var characters = document.querySelectorAll(".characters");
//
//     if (characters.length > 0) {
//         // Генерация случайного индекса от 0 до characters.length - 1
//         var randomIndex = Math.floor(Math.random() * characters.length);
//
//         // Показываем только блок с выбранным случайным индексом
//         characters[randomIndex].style.display = "flex";
//     }
//
//     document.getElementById("totop").addEventListener("click", function() {
//         // Получаем текущую позицию прокрутки
//         var currentScroll = document.documentElement.scrollTop || document.body.scrollTop;
//         // Устанавливаем интервал, в течение которого будет происходить плавная прокрутка
//         var interval = setInterval(function() {
//             // Уменьшаем позицию прокрутки на небольшое значение, чтобы создать плавный эффект
//             window.scrollTo(0, currentScroll -= 20);
//             // Когда доскроллили до самого верха, останавливаем интервал
//             if (currentScroll <= 0) clearInterval(interval);
//         }, 10); // Чем меньше значение, тем плавнее будет прокрутка
//     });
//
// });




document.addEventListener('DOMContentLoaded', () => {
    const burger = document.querySelector(".gw-burger");

    if (!burger) return; // выходим, если бургера нет

    burger.addEventListener("click", function (e) {
        e.preventDefault();
        const topMenu = document.querySelector(".top_menu");
        const isActive = topMenu.classList.contains("active");

        this.classList.toggle("gw-burger_active", !isActive);
        topMenu.classList.toggle("active", !isActive);

        if (!isActive) {
            document.addEventListener("click", hideMenu);
        } else {
            document.removeEventListener("click", hideMenu);
        }

        function hideMenu(event) {
            if (!topMenu.contains(event.target) && event.target !== burger) {
                topMenu.classList.remove("active");
                burger.classList.remove("gw-burger_active");
                document.removeEventListener("click", hideMenu);
            }
        }
    });
});


// Находим элемент <span> по его id
const serverOldTimeSpan = document.getElementById('server_old_time');

// Проверка наличия элемента
if (serverOldTimeSpan) {
    // время серверов сколько прошло
// Дата и время запуска сервера
    const startDate = new Date('2024-01-12T20:00:00Z'); // 12 января 2023 года, 20:00 по МСК

// Текущая дата и время
    const currentDate = new Date();

// Разница между текущей датой и датой запуска сервера в миллисекундах
    const difference = currentDate - startDate;

// Преобразуем миллисекунды в дни
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));

    serverOldTimeSpan.textContent = days;

}


// Глазик возле пароля чтобы увидеть пароль
const eyeOpenSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
        <path d="M12 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
        <path d="M22 12c-2.667 4 -6 6 -10 6s-7.333 -2 -10 -6c2.667 -4 6 -6 10 -6s7.333 2 10 6" />
    </svg>`;

const eyeOffSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
        <path d="M3 3l18 18" />
        <path d="M10.584 10.587a2 2 0 0 0 2.829 2.828" />
        <path d="M16.681 16.674c-1.407 .87 -3.05 1.326 -4.681 1.326c-4 0 -7.333 -2 -10 -6
                 c1.223 -1.84 2.71 -3.234 4.47 -4.18" />
        <path d="M9.827 5.826c.702 -.18 1.437 -.276 2.173 -.276c4 0 7.333 2 10 6
                 c-.843 1.27 -1.79 2.386 -2.82 3.35" />
    </svg>`;

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.password-toggle').forEach(toggle => {
        // Вставляем иконку по умолчанию
        toggle.innerHTML = eyeOffSVG;

        toggle.addEventListener('click', () => {
            const target = document.getElementById(toggle.dataset.target);
            const isHidden = toggle.dataset.state === 'hidden';

            target.setAttribute('type', isHidden ? 'text' : 'password');
            toggle.dataset.state = isHidden ? 'visible' : 'hidden';
            toggle.innerHTML = isHidden ? eyeOpenSVG : eyeOffSVG;
        });
    });
});

// END Глазик возле пароля чтобы увидеть пароль

// SERVER SWITCH START
document.addEventListener('DOMContentLoaded', function () {
    let currentServerItem = document.getElementById('currentServer');
    if (!currentServerItem) {
        //console.warn('Element with id "currentServer" not found.');
        return;
    }

    let serverList = document.getElementById('serverList');
    let serverForm = document.querySelector('.server-switcher__form');
    let arrow = currentServerItem.querySelector('.arrow-down');

    currentServerItem.addEventListener('click', function () {
        if (serverList && (serverList.style.display === 'none' || !serverList.style.display)) {
            serverList.style.display = 'block';
            currentServerItem.classList.add('active');
            arrow.classList.add('rotate'); // Rotate arrow
        } else if (serverList) {
            serverList.style.display = 'none';
            currentServerItem.classList.remove('active');
            arrow.classList.remove('rotate'); // Remove rotation
        }
    });

    if (serverList) {
        serverList.addEventListener('click', function (event) {
            if (event.target.dataset.server) {
                let selectedServer = event.target.dataset.server;

                // Update hidden inputs in the form with the selected server
                if (serverForm) {
                    serverForm.querySelectorAll('input[name="server"]').forEach(input => {
                        input.disabled = input.value !== selectedServer;
                    });

                    // Submit the form
                    serverForm.submit();
                } else {
                    console.warn('Server form not found.');
                }
            }
        });
    } else {
        console.warn('Element with id "serverList" not found.');
    }

    document.addEventListener('mouseup', function (e) {
        // Проверяем наличие элементов перед вызовом метода contains
        if (
            currentServerItem &&
            serverList &&
            !currentServerItem.contains(e.target) &&
            !serverList.contains(e.target)
        ) {
            currentServerItem.classList.remove('active');
            serverList.style.display = 'none';
            if (arrow) arrow.classList.remove('rotate'); // Remove rotation
        }
    });
});
// SERVER SWITCH END

// ACP PANEL TOP
document.addEventListener('DOMContentLoaded', function () {
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    const arrowDown = document.querySelector('.arrow-down-acp');

    if(dropdownToggle) {
        dropdownToggle.addEventListener('click', function () {
            dropdownMenu.classList.toggle('show');
            arrowDown.classList.toggle('rotate');
        });

        document.addEventListener('click', function (event) {
            if (!dropdownToggle.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.classList.remove('show');
                arrowDown.classList.remove('rotate');
            }
        });
    }
});
// ACP PANEL TOP END

// POPUP попап окно
document.addEventListener('DOMContentLoaded', function () {
    // Функция для показа попапа
    function showPopup(popupId) {
        const popup = document.getElementById(popupId);
        if (popup) {
            popup.style.display = 'flex';
        }
    }

    // Функция для скрытия попапа
    function hidePopup(popupId) {
        const popup = document.getElementById(popupId);
        if (popup) {
            popup.style.display = 'none';
        }
    }

    // Добавление обработчиков событий на все элементы с классом 'popup-close'
    const closeButtons = document.querySelectorAll('.popup-close');
    closeButtons.forEach(function (button) {
        button.addEventListener('click', function () {
            const popup = button.closest('.popup-overlay');
            if (popup) {
                hidePopup(popup.id);
            }
        });
    });

    // Пример использования: показать попап при клике на кнопку
    const openPopupButtons = document.querySelectorAll('.open-popup');
    openPopupButtons.forEach(function (button) {
        button.addEventListener('click', function () {
            const popupId = button.getAttribute('data-popup-id');
            showPopup(popupId);
        });
    });

    // Закрытие попапа при клике вне контента
    document.querySelectorAll('.popup-overlay').forEach(function (overlay) {
        overlay.addEventListener('click', function (e) {
            if (e.target === overlay) {
                hidePopup(overlay.id);
            }
        });
    });
});
// POPUP попап окно
