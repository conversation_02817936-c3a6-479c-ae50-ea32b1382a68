<?php

namespace App\Http\Controllers;

use App\Models\DonateOrder;
use App\Models\ModelFactory;
use App\Services\Payment\EnotService;
use App\Services\Payment\FreeKassaService;
use App\Services\Payment\MidTransService;
use App\Services\Payment\MoruneService;
use App\Services\Payment\PayPalService;
use App\Services\Payment\PrimePaymentsService;
use App\Services\Payment\StripeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DonateController extends Controller
{
    protected $freeKassaService;
    protected $moruneService;
    protected $primePaymentsService;
    protected $enotService;
    protected $paypalService;
    protected $stripeService;
    protected $midTransService;


    public function __construct(
        FreeKassaService $freeKassaService,
        MoruneService $moruneService,
        PrimePaymentsService $primePaymentsService,
        EnotService $enotService,
        PayPalService $paypalService,
        StripeService $stripeService,
        MidTransService $midTransService
    ) {
        $this->freeKassaService = $freeKassaService;
        $this->moruneService = $moruneService;
        $this->primePaymentsService = $primePaymentsService;
        $this->enotService = $enotService;
        $this->paypalService = $paypalService;
        $this->stripeService = $stripeService;
        $this->midTransService = $midTransService;
    }

    // Для авторизованных пользователей
    public function createOrder(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['message' => 'Необходима авторизация'], 401);
        }

        $validated = $request->validate([
            'pay_select' => 'required|string',
            'count' => 'required|integer|min:1',
        ]);

        $paymentMethod = $validated['pay_select'];
        $count = $validated['count'];
        $user = Auth::user();

        $payment_id = "{$user->id}_" . time();

        // Вызов соответствующего сервиса
        return $this->processPayment($paymentMethod, $payment_id, $count, null, $user->id, null);
    }

    // Для неавторизованных пользователей
    public function store(Request $request)
    {
        $validated = $request->validate([
            'server' => 'required|string',
            'pay_select' => 'required|string',
            'char_name' => 'required|string|min:3',
            'count' => 'required|integer|min:1',
        ]);

        $selected_server = $validated['server'];
        $paymentMethod = $validated['pay_select'];
        $char_name = $validated['char_name'];
        $count = $validated['count'];

        // Проверка персонажа
        try {
            $char_id = ModelFactory::l2jModel(config('app.l2server_version'))
                ->getCharIdByCharName($char_name, $selected_server)
                ->obj_Id;
        } catch (\Exception $e) {
            return redirect()->back()->with('char_name', 'Игрок с ником ' . $request->input('char_name') . ' не найден');
        }


        $payment_id = "{$char_id}_" . time();

        // Вызов соответствующего сервиса
        return $this->processPayment($paymentMethod, $payment_id, $count, $selected_server, null, $char_id);
    }

    // Обработка платежа
    private function processPayment($paymentMethod, $payment_id, $count, $selected_server, $user_id = null, $char_id = null)
    {
        $success_url = null;

        switch ($paymentMethod) {
            case 'freekassa':
                $success_url = $this->freeKassaService->createPayment($payment_id, $count);
                break;
            case 'morune':
                $success_url = $this->moruneService->createPayment($payment_id, $count);
                break;
            case 'primepayments':
                $success_url = $this->primePaymentsService->createPayment($payment_id, $count);
                break;
            case 'enot':
                $success_url = $this->enotService->createPayment($payment_id, $count);
                break;
            case 'paypal':
                $success_url = $this->paypalService->createPayment($payment_id, $count);
                break;
            case 'stripe':
                $success_url = $this->stripeService->createPayment($payment_id, $count);
                break;
            case 'midtrans':
                $success_url = $this->midTransService->createPayment($payment_id, $count);
                break;
            default:
                return redirect()->back()->withErrors(['payment_error' => 'Unsupported payment system']);
        }

        if ($success_url) {
            $payOrder = new DonateOrder;
            $payOrder->payment_id = $payment_id;
            $payOrder->user_id = $user_id ?? $char_id;
            $payOrder->count = $count;
            $payOrder->server_id = $selected_server;
            $payOrder->pay_system = $paymentMethod;
            $payOrder->save();

            return redirect($success_url);
        }

        return redirect()->back()->withErrors(['payment_error' => 'Failed to create payment']);
    }

    public function callbackFreekassa(Request $request)
    {
        return $this->freeKassaService->handleWebhook($request);
    }

    public function callbackMorune(Request $request)
    {
        return $this->moruneService->handleWebhook($request);
    }

    public function callbackPrimePayments(Request $request)
    {
        return $this->primePaymentsService->handleWebhook($request);
    }

    public function callbackEnot(Request $request)
    {
        return $this->enotService->handleWebhook($request);
    }

    public function callbackPaypal(Request $request)
    {
        return $this->paypalService->handleWebhook($request);
    }

    public function callbackStripe(Request $request)
    {
        return $this->stripeService->handleWebhook($request);
    }

    public function callbackMidTrans(Request $request)
    {
        return $this->midTransService->handleWebhook($request);
    }

    public function index() {
        if(!Auth::check()) {
            return view('template::pages.donate');
        }
        else {
            return redirect()->route('pay');
        }
    }

    public function auth_page() {
        return view('template::dashboard.pay');
    }
}
