<div class="server-switcher">
    <div class="server-switcher__current" id="currentServer">
        <span>{{ config('app.getServers')()[session('server', array_key_first(config('app.getServers')()))]['server_name'] ?? 'No server selected' }}</span>
        <i class="arrow-down"></i>
    </div>

    @if(count(config('app.getServers')()) > 1)
        <form action="{{ route('switch-server') }}" method="POST" class="server-switcher__form">
            @csrf
            @foreach(config('app.getServers')() as $serverKey => $serverDetails)
                <input type="hidden" name="server" value="{{ $serverKey }}" />
            @endforeach
        </form>

        <ul class="server-switcher__list" id="serverList">
            @foreach(config('app.getServers')() as $serverKey => $serverDetails)
                @if($serverKey !== session('server', array_key_first(config('app.getServers')())))
                    <li>
                        <a href="#" data-server="{{ $serverKey }}" class="server-switcher__item">
                            {{ $serverDetails['server_name'] }}
                        </a>
                    </li>
                @endif
            @endforeach
        </ul>
    @endif
</div>
