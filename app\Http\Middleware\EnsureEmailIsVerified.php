<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureEmailIsVerified
{

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (config('app.email_verification') && !$request->user()?->hasVerifiedEmail()) {
            return redirect()->route('verify.notice')->with('message', 'Пожалуйста, активируйте свой аккаунт через письмо на почте.');
        }


        return $next($request);
    }
}
