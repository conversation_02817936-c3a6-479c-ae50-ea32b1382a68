<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DonateOrder extends Model
{
    use HasFactory;

    protected $connection = 'mysql';

    protected $fillable = ['payment_id', 'user_id', 'count', 'status', 'server_id', 'pay_system'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
