<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class News extends Model
{
    use HasFactory;

    protected $connection = 'mysql';

    protected $fillable = [
        'title',         // Заголовок новости
        'image',         // Изображение новости
        'short_desc',   // Краткое описание
        'description',   // Описание новости
        'slug',          // ЧПУ ссылка на новость
        'forum_link',    // Ссылка на форум
        'published_at',  // Дата публикации
        'lang',
        'status',        // Статус новости (черновик, опубликовано, скрыто)
        'show_sector',
    ];

    public static function boot()
    {
        parent::boot();

        // Автоматически генерируем слаг перед сохранением
        static::saving(function ($model) {
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->title);
            }
        });
    }

    // Обработчик для загрузки изображений
    public function setImageAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['image'] = $value;
        } elseif ($value) {
            // Удаление старого изображения, если есть
            if ($this->attributes['image']) {
                Storage::delete($this->attributes['image']);
            }
            // Загрузка нового изображения
            $path = $value->store('public/images');
            $this->attributes['image'] = $path;
        }
    }

    public function getImageUrlAttribute()
    {
        return $this->attributes['image'] ? Storage::url($this->attributes['image']) : null;
    }
}
