@section('title', __('Update Password'))
@extends('template::layouts.dashboard')

@section('content')

<div class="card">
    <div class="title">{{ __('Ensure your account is using a long, random password to stay secure.') }}</div>

    <form method="post" action="{{ route('password.update') }}">
        @csrf
        @method('put')

        <div class="form-group">
            <div class="password-group">
                <input id="current_password" class="form-control"
                       type="password"
                       name="current_password"
                       placeholder="{{ __('Current Password') }}"
                       required
                />
                <span class="input-group-text password-toggle" data-target="current_password" data-state="hidden"></span>
            </div>
            <x-input-error :messages="$errors->updatePassword->get('current_password')" class="text-danger" />
        </div>

        <div class="form-group">
            <div class="password-group">
                <input id="update_password_password" class="form-control"
                       type="password"
                       name="password"
                       placeholder="{{ __('New Password') }}"
                       required
                />
                <span class="input-group-text password-toggle" data-target="update_password_password" data-state="hidden"></span>
            </div>
            <x-input-error :messages="$errors->updatePassword->get('password')" class="text-danger" />
        </div>

        <div class="form-group">
            <div class="password-group">
                <input id="update_password_password_confirmation" class="form-control"
                       type="password"
                       name="password_confirmation"
                       placeholder="{{ __('Confirm Password') }}"
                       required
                />
                <span class="input-group-text password-toggle" data-target="update_password_password_confirmation" data-state="hidden"></span>
            </div>
            <x-input-error :messages="$errors->updatePassword->get('password_confirmation')" class="text-danger" />
        </div>

        <div class="form-group">
            <button class="btn-primary" type="submit"><span>{{ __('Save') }}</span></button>
            @if (session('status') === 'password-updated')
                <div class="alert alert-success">
                    <p>{{ __('Your password has been successfully changed.') }}</p>
                    <a href="{{ url()->current() }}" class="close"></a>
                </div>
            @endif
        </div>
    </form>
</div>

@endsection
