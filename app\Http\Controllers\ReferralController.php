<?php

namespace App\Http\Controllers;

use App\Models\Referral;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReferralController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        if (!$user->canUseReferral()) {
            return redirect()->route('dashboard')->with('error', 'Вы не можете участвовать в реферальной системе.');
        }

        // Проверяем, есть ли реферальный код, и если нет – создаем
        if (!$user->referral_code) {
            $user->generateReferralCode();
        }

        // Получаем список рефералов
        $referrals = $user->referrals()->with('referred')->get();
        $referralsCount = $referrals->count(); // Подсчет приглашенных пользователей

        return view('template::dashboard.referral', [
            'user' => $user,
            'referralLink' => url("/?ref=" . $user->referral_code),
            //'referrals' => $referrals,
            'referralsCount' => $referralsCount, // Передача количества в представление
        ]);
    }


    public function updateReferralCode(Request $request)
    {
        $user = Auth::user();

        if (!$user->canUseReferral()) {
            return redirect()->route('dashboard')->with('error', 'Вы не можете участвовать в реферальной системе.');
        }

        // Валидируем новый код
        $request->validate([
            'newReferralCode' => [
                'required',
                'string',
                'max:16',
                'regex:/^[a-zA-Z0-9]+$/',
                'unique:users,referral_code',
            ],
        ], [
            'newReferralCode.unique' => __('This referral code is already in use. Please choose another one.'),
            'newReferralCode.regex' => __('The referral code can only contain Latin letters and numbers.'),
        ]);

        // Обновляем код
        $user->update(['referral_code' => $request->newReferralCode]);

        return redirect()->route('referral.index')->with('success', 'Реферальный код обновлен.');
    }
}
