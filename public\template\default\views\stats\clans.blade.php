@section('title', __('Clans'))
@extends('template::layouts.dashboard')

@section('content')
    @include('template::components.nav-stats')
    <div class="lin2Table">
        <table class="rating">
            <thead>
            <tr>
                <th>#</th>
                <th></th>
                <th>{{ __('Name clan') }}</th>
                <th>{{ __('Lv') }}</th>
                <th>{{ __('Leader') }}</th>
                <th>{{ __('Holdings') }}</th>
                <th>{{ __('Reputation') }}</th>
                <th>{{ __('Players') }}</th>
                <th>{{ __('Alliance') }}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($clans as $cl)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>
                        <div class="clan-crests">
                            @if($cl->ally_crest)
                                <img src="{{ $cl->ally_crest }}" width="8" height="12" alt="Ally Crest">
                            @endif
                            @if($cl->clan_crest)
                                <img src="{{ $cl->clan_crest }}" width="16" height="12" alt="Clan Crest">
                            @endif
                        </div>
                    </td>
                    <td>{{ $cl->clan_name }}</td>
                    <td>{{ $cl->clan_level }}</td>
                    <td>{{ $cl->clan_leader }}</td>
                    <td>
                        @if($cl->castle_name)
                            <div>{{ __('Castle') }}: <span>{{ $cl->castle_name }}</span></div>
                        @endif
                        @if($cl->clanhall_name)
                            <div>{{ __('Clanhall') }}: <span>{{ $cl->clanhall_name }}</span></div>
                        @endif
                    </td>
                    <td>{{ $cl->reputation_score }}</td>
                    <td>{{ $cl->clan_players }}</td>
                    <td>{{ $cl->ally_name }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
@endsection
