<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title') - {{ config('app.name') }}</title>

    <link rel="icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>
    <link rel="shortcut icon" href="{{ base_url('img/favicon/favicon.png') }}" type="image/x-icon"/>

    <meta name="description" content="@yield('meta_description', 'Удобная панель управления аккаунтом Lin2Web: мониторинг баланса, отправка файлов, управление рефералами и настройками для Lineage 2.')">
    <meta name="keywords" content="@yield('meta_keywords', 'Lin2Web,CMS,личный кабинет,L2J,панель управления,баланс,рефералы,Lineage 2')">

    <!-- Open Graph для социальных сетей -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="@yield('title', 'Личный кабинет Lin2Web')">
    <meta property="og:description" content="@yield('meta_description')">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:image" content="{{ base_url('img/lin2web/bg.jpg') }}">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('title', 'Личный кабинет Lin2Web')">
    <meta name="twitter:description" content="@yield('meta_description')">
    <meta name="twitter:image" content="{{ base_url('img/lin2web/bg.jpg') }}">


    <link rel="stylesheet" href="{{ base_url('css/public.css') }}">
    <script src="{{ base_url('js/app.js') }}"></script>
</head>
<body>
<div class="wrapper">
    @include('template::header')
    <main>
        @yield('content')
    </main>
    @include('template::footer')
</div>
</body>
</html>
