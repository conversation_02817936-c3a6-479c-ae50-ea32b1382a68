<?php

namespace App\Http\Controllers;

use App\Models\ModelFactory;
use App\Models\News;
use App\Services\StreamerService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PDO;
use PDOException;

class HomeController extends Controller
{
    protected $streamerService;

    public function __construct(StreamerService $streamerService)
    {
        if(config('app.STREAMER_ENABLED')) {
            $this->streamerService = $streamerService;
        }
    }


    public function showLatestThreads()
    {
        $client = new Client();

        try {
            if(config('app.FORUM_XF_ENABLED')) {
                $response = $client->get(config('app.XF_URL_FORUM'), [
                    'headers' => [
                        'XF-Api-Key' => config('app.XF_API_KEY'),
                        'XF-Api-User' => config('app.XF_API_USER'),
                    ],
                    'verify' => false // ⛔️ отключает проверку SSL
                ]);


                $data = json_decode($response->getBody()->getContents(), true);

                // Проверяем, существует ли ключ 'threads' и ограничиваем количество тем до 5
                $threads = isset($data['threads']) ? array_slice($data['threads'], 0, 5) : [];

                // Возвращаем массив с темами
                Log::info($data);
                return $threads;
            }
        }
        catch (\Exception $e) {

            Log::info('forum error: ' . $e);
            return [];
        }
    }


    public function showLatestThreadsFromCategory($boardId = 2) // ID раздела по умолчанию
    {
        $rssUrl = config('app.SMF_URL') . "/index.php?action=.xml;type=rss;board={$boardId};limit=5";

        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->get($rssUrl);
            $xml = simplexml_load_string($response->getBody()->getContents());

            $threads = [];

            foreach ($xml->channel->item as $item) {
                $threads[] = [
                    'title' => (string) $item->title,
                    'link' => (string) $item->link,
                    'author' => (string) $item->author,
                    'pubDate' => (string) $item->pubDate,
                ];
            }

            return $threads;
        } catch (\Exception $e) {
            \Log::error('Ошибка загрузки RSS из категории SMF: ' . $e->getMessage());
            return [];
        }
    }



    public function index(Request $request)
    {
        // Если есть реферальный код, перенаправляем на регистрацию
        if ($request->has('ref')) {
            return redirect()->route('register', ['ref' => $request->query('ref')]);
        }

        try {
            $top_pvp = config('app.statistics.enable_top_pvp')
                ? Cache::remember('top_pvp', config('app.statistics.stats_cached'), function () {
                    try {
                        return ModelFactory::l2jModel(config('app.l2server_version'))->getPvpPlayersMainPage();
                    } catch (\Exception $e) {
                        Log::warning('Ошибка загрузки топ PvP: ' . $e->getMessage());
                        return [];
                    }
                })
                : [];

            if ($top_pvp) {
                foreach ($top_pvp as &$item) {
                    $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                    $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                }
            }

            $top_pk = config('app.statistics.enable_top_pk')
                ? Cache::remember('top_pk', config('app.statistics.stats_cached'), function () {
                    try {
                        return ModelFactory::l2jModel(config('app.l2server_version'))->getPkPlayersMainPage();
                    } catch (\Exception $e) {
                        Log::warning('Ошибка загрузки топ PK: ' . $e->getMessage());
                        return [];
                    }
                })
                : [];

            if ($top_pk) {
                foreach ($top_pk as &$item) {
                    $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                    $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                }
            }

            $clans = config('app.statistics.enable_clans')
                ? Cache::remember('clans', config('app.statistics.stats_cached'), function () {
                    try {
                        return ModelFactory::l2jModel(config('app.l2server_version'))->getTopClansMainPage();
                    } catch (\Exception $e) {
                        Log::warning('Ошибка загрузки кланов: ' . $e->getMessage());
                        return [];
                    }
                })
                : [];

            if ($clans) {
                foreach ($clans as &$item) {
                    $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                    $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                }
            }

            $castles = config('app.statistics.enable_castles')
                ? Cache::remember('castles_main_page', config('app.statistics.stats_cached'), function () {
                    try {
                        return ModelFactory::l2jModel(config('app.l2server_version'))->getCastleMainPage();
                    } catch (\Exception $e) {
                        Log::warning('Ошибка загрузки замков: ' . $e->getMessage());
                        return [];
                    }
                })
                : [];

            if ($castles) {
                foreach ($castles as &$item) {
                    $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                    $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                }
            }

            $news = News::where('status', 'published')
                ->orderByRaw("CASE WHEN show_sector = 'main' THEN 0 ELSE 1 END") // 'main' ставится выше всех
                ->orderBy('created_at', 'desc') // остальные сортируются по дате
                ->limit(3) // Берём только 2 записи
                ->get()
                ->map(function ($item) {
                    $item->formatted_date = Carbon::parse($item->created_at)->format('d.m.Y');
                    return $item;
                });

            $streamers = config('app.STREAMER_ENABLED')
                ? $this->streamerService->getAllStreamersData(config('app.streamerTwitch'), config('app.streamerTrovo'))
                : false;

            $online = config('app.enable_online')
                ? Cache::remember('online_count', config('app.server_status_cached_time'), function () {
                    try {
                        $data = ModelFactory::l2jModel(config('app.l2server_version'))->getOnline();
                        return is_array($data) ? $data : [];
                    } catch (\Exception $e) {
                        Log::warning('Ошибка загрузки онлайн-игроков: ' . $e->getMessage());
                        return [];
                    }
                })
                : [];


            $forum_latest = $this->showLatestThreads();
            //$forum_latest = $this->showLatestThreadsFromCategory(2);


            return view('template::pages.main', [
                'top_pvp' => $top_pvp,
                'top_pk' => $top_pk,
                'clans' => $clans,
                'castles' => $castles,
                'streamers' => $streamers,
                'news' => $news,
                'forum_latest' => $forum_latest,
                'server_status' => $online
            ]);
        } catch (\Exception $e) {
            Log::error('Общая ошибка в HomeController@index: ' . $e->getMessage());
            return view('template::pages.main', [
                'top_pvp' => [],
                'top_pk' => [],
                'clans' => [],
                'castles' => [],
                'streamers' => false,
                'news' => [],
                'forum_latest' => [],
                'server_status' => 0,
            ]);
        }
    }



    public function switchServer(Request $request)
    {
        $server = $request->input('server');
        $request->session()->put('server', $server);

        return back();
    }

    public function test()
    {
        try {
            $pdo = new PDO("sqlsrv:Server=15.235.220.58,1433;Database=lin2db;Encrypt=no;TrustServerCertificate=yes", "sa", "Lumpia001");
            echo "Connected successfully!";
        } catch (PDOException $e) {
            echo "Connection failed: " . $e->getMessage();
        }

        //phpinfo();
//        // Извлечь сохраненный пароль для "daniel2"
//        $storedPassword = DB::connection('lin2db')
//            ->table('user_auth')
//            ->where('account', 'kenrix')
//            ->value('password');
//
//// Убедимся, что значение извлечено
//        if (!$storedPassword) {
//            die("Не удалось найти пароль для аккаунта 'daniel2'");
//        }
//
//// Преобразуем из binary в hex (если нужно)
//        $storedPasswordHex = bin2hex($storedPassword);
//
//// Известный plaintext пароль
//        $plaintextPassword = 'µ;ViTinp}?22yeCC';
//
//
//        print_r('pass user_auth daniel2 ' . $storedPasswordHex);
//        echo '<hr>';
//        print_r('pass user_auth tester11 ' . $plaintextPassword);
//        echo '<hr>';
//
//
//// Зашифровать известный пароль
//        $encryptedPassword = encryptPTS($plaintextPassword);
//
//// Сравнить результат
//        if (strcasecmp($storedPassword, substr($encryptedPassword, 2)) === 0) {
//            echo "Пароль совпадает! Все работает корректно.";
//        } else {
//            echo "Пароль не совпадает! Проверьте алгоритм или исходные данные.";
//        }

    }


}
