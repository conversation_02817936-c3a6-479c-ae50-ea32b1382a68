<?php

namespace App\Filament\Resources\TicketResource\Pages;

use App\Filament\Resources\TicketResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTicket extends EditRecord
{
    protected static string $resource = TicketResource::class;


    // Для перевода заголовка страницы
    protected static ?string $title = 'Редактирование тикета'; // Заголовок страницы редактирования


    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['messages'] = $this->record->messages()->with('user')->get()->map(function ($message) {
            return [
                'author' => $message->user ? $message->user->name : 'Администратор',
                'message' => $message->message,
                'attachment' => $message->attachment,
                'created_at' => $message->created_at->format('d.m.Y H:i'),
            ];
        })->toArray();

        return $data;
    }


    protected function saveResponse(string $response): void
    {
        $this->record->messages()->create([
            'user_id' => null, // Администратор
            'message' => $response,
        ]);

        $this->record->update(['status' => 'in_progress']);
    }
}
