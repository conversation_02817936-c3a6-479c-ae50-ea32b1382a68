<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MergeServerController extends Controller
{
    // ALTER TABLE `characters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    protected $objID = [
        ["characters", ["obj_Id", "clanid"]],
        ["character_macroses", ["char_obj_id"]],
        ["character_quests", "char_id"],
        ["clan_subpledges", ["clan_id", "leader_id"]],
        ["clan_data", "clan_id"],
        ["clan_privs", "clan_id"],
        ["clan_skills", "clan_id"],
        ["clan_subpledges_skills", "clan_id"],
//        ["mail", "message_id"],
//        ["character_mail", "message_id"],
//        ["mail_attachments", "message_id"],
        ["character_hennas", "char_obj_id"],
        ["character_minigame_score", "object_id"],
        ["character_premium_items", "charId"],
        ["character_recipebook", "char_id"],
        ["character_shortcuts", "object_id"],
        ["character_subclasses", "char_obj_id"],
        ["character_variables", "obj_id"],
        ["pets", ["item_obj_id", "objId"]],
        ["items", ["object_id", "owner_id"]],
        ["character_skills", "char_obj_id"],
        ["character_skills_save", "char_obj_id"],
        ["olympiad_nobles", "char_id"],
        ["bans", "obj_Id"],
        ["bbs_teleport_bm", ["char_id", 'name']],
    ];

    public function index()
    {
        try {
            Log::info('MergeServerController started.');

            $idMap = $this->createIdMap();
            DB::connection('dist_server')->beginTransaction();

            foreach ($this->objID as $tableInfo) {
                $table = $tableInfo[0];
                $columns = is_array($tableInfo[1]) ? $tableInfo[1] : [$tableInfo[1]];

                $this->mergeTable($table, $columns, $idMap);
            }

            DB::connection('dist_server')->commit();
            Log::info("Merge completed successfully.");
        } catch (\Exception $e) {
            DB::connection('dist_server')->rollBack();
            Log::error("Error merging servers: " . $e->getMessage());
        }
    }

    private function createIdMap()
    {
        $idMap = [];
        $srcConnection = DB::connection('src_server');
        Log::info('Creating ID map.');

        foreach ($this->objID as $tableInfo) {
            $table = $tableInfo[0];
            $columns = is_array($tableInfo[1]) ? $tableInfo[1] : [$tableInfo[1]];

            $data = $srcConnection->table($table)->get();
            Log::info("Fetched data from table {$table}.");

            foreach ($data as $row) {
                foreach ($columns as $column) {
                    $currentId = $row->$column;
                    if (!isset($idMap[$currentId])) {
                        $idMap[$currentId] = $this->generateNewId($currentId);
                    }
                }
            }
        }

        return $idMap;
    }

    private function generateNewId($currentId)
    {
        static $maxId = null;
        $distConnection = DB::connection('dist_server');

        // Инициализация максимального ID при первом вызове функции
        if ($maxId === null) {
            $maxId = $this->getMaxId($distConnection);
        }

        // Инкрементируем максимальный ID для нового элемента
        $maxId++;

        return $maxId;
    }

    private function getMaxId($connection)
    {
        $maxId = 0;
        foreach ($this->objID as $tableInfo) {
            $table = $tableInfo[0];
            $columns = is_array($tableInfo[1]) ? $tableInfo[1] : [$tableInfo[1]];

            foreach ($columns as $column) {
                $tableMaxId = $connection->table($table)->max($column) ?? 0;
                if ($tableMaxId > $maxId) {
                    $maxId = $tableMaxId;
                }
            }
        }

        return $maxId;
    }

    private function mergeTable($table, $columns, $idMap)
    {
        $srcConnection = DB::connection('src_server');
        $distConnection = DB::connection('dist_server');

        $data = $srcConnection->table($table)->get();
        Log::info("Merging table {$table}.");

        $insertData = [];

        foreach ($data as $row) {
            foreach ($columns as $column) {
                $row->$column = $idMap[$row->$column] ?? $row->$column;
            }

            if ($table === 'characters') {
                $row->char_name = $this->processCharName($row->char_name, $distConnection);
            }

            if ($table === 'clan_subpledges') {
                $row->name = $this->processClanName($row->name, $distConnection);
            }

            // Проверка существования message_id в таблице mail
//            if ($table === 'character_mail' && !$distConnection->table('mail')->where('message_id', $row->message_id)->exists()) {
//                Log::info("Message ID {$row->message_id} не найден в таблице mail, вставка нового сообщения.");
//                // Вставка отсутствующего сообщения
//                $distConnection->table('mail')->insert([
//                    'message_id' => $row->message_id,
//                    // Добавьте другие необходимые поля здесь
//                ]);
//            }

            $insertData[] = (array)$row;
        }

        // Выполняем пакетные вставки
        $chunkSize = 500; // Размер пакета
        foreach (array_chunk($insertData, $chunkSize) as $chunk) {
            $distConnection->table($table)->upsert($chunk, [$columns[0]]);
        }

        Log::info("Table {$table} processed successfully.");
    }

    private function mergePremiumAccounts($idMap)
    {
        Log::info('mergePremiumAccounts: started.');
        $srcConnection = DB::connection('src_server');
        $distConnection = DB::connection('dist_server');

        try {
            $data = $srcConnection->table('premium_accounts')->get();
            Log::info("mergePremiumAccounts: fetched data from src_server, count: " . count($data));

            $mergedData = [];

            foreach ($data as $row) {
                $account = $row->account;
                if (isset($mergedData[$account])) {
                    if ($row->type > $mergedData[$account]->type) {
                        $mergedData[$account]->type = $row->type;
                    }
                    if ($row->expire_time > $mergedData[$account]->expire_time) {
                        $mergedData[$account]->expire_time = $row->expire_time;
                    }
                } else {
                    $mergedData[$account] = clone $row;
                }
            }
            Log::info('mergePremiumAccounts: merged data prepared.');

            $distData = $distConnection->table('premium_accounts')->get()->keyBy('account');
            Log::info("mergePremiumAccounts: fetched data from dist_server, count: " . count($distData));

            foreach ($mergedData as $account => $row) {
                if (isset($distData[$account])) {
                    if ($row->type > $distData[$account]->type) {
                        $distData[$account]->type = $row->type;
                    }
                    if ($row->expire_time > $distData[$account]->expire_time) {
                        $distData[$account]->expire_time = $row->expire_time;
                    }
                    $distConnection->table('premium_accounts')->where('account', $account)->update((array)$distData[$account]);
                } else {
                    $distConnection->table('premium_accounts')->insert((array)$row);
                }
            }

            Log::info("mergePremiumAccounts: processed successfully.");
        } catch (\Exception $e) {
            Log::error("mergePremiumAccounts: error - " . $e->getMessage());
        }
    }

    public function taskPremiumAccount() {
        $idMap = $this->createIdMap();
        $this->mergePremiumAccounts($idMap);
    }

    private function processCharName($charName, $distConnection)
    {
        // Максимальная длина имени
        $maxLength = 16;

        // Обрезка имени до максимальной длины, если оно длиннее
        if (strlen($charName) > $maxLength) {
            $charName = substr($charName, 0, $maxLength);
        }

        $originalCharName = $charName;
        $suffix = 1;

        // Проверка существования имени в базе данных и добавление суффикса при необходимости
        while ($distConnection->table('characters')->whereRaw('LOWER(char_name) = ?', [strtolower($charName)])->exists()) {
            $charName = $originalCharName . '_' . $suffix;

            // Обрезка имени с учетом суффикса, если оно длиннее максимальной длины
            if (strlen($charName) > $maxLength) {
                $charName = substr($originalCharName, 0, $maxLength - strlen('_' . $suffix)) . '_' . $suffix;
            }
            $suffix++;
        }

        return $charName;
    }

    private function processClanName($clanName, $distConnection)
    {
        // Макс длина клана
        $maxLength = 16;

        if (strlen($clanName) > $maxLength) {
            $clanName = substr($clanName, 0, $maxLength);
        }

        $originalClanName = $clanName;
        $suffix = 1;

        while ($distConnection->table('clan_subpledges')->whereRaw('LOWER(name) = ?', [strtolower($clanName)])->exists()) {
            $clanName = $originalClanName . '_' . $suffix;
            if (strlen($clanName) > $maxLength) {
                $clanName = substr($originalClanName, 0, $maxLength - strlen('_' . $suffix)) . '_' . $suffix;
            }
            $suffix++;
        }

        return $clanName;
    }
}
