<?php

namespace App\Http\Controllers;

use App\Models\ModelFactory;
use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use stdClass;

class StatisticController extends Controller
{
    public function index() {
        return view('template::app');
    }

    public function list() {
        $data = Cache::remember('statistics_list_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));
            $config = config('app.statistics');
            $data = new stdClass;

            if ($config['accounts_count_enabled']) {
                $data->accounts_count = $model->getCountAccounts();
            }

            if ($config['characters_count_enabled']) {
                $data->characters_count = $model->getCountCharacters();
            }

            if ($config['nobles_count_enabled']) {
                $data->nobles_count = $model->getCountNobles();
            }

            if ($config['heroes_count_enabled']) {
                $data->heroes_count = $model->getCountHeroes();
            }

            if ($config['gm_count_enabled']) {
                $data->gm_count = $model->getCountGameMasters();
            }

            if ($config['clans_count_enabled']) {
                $data->clans_count = $model->getCountClans();
            }

            if ($config['alliance_count_enabled']) {
                $data->alliance_count = $model->getCountAllyance();
            }

            if ($config['gender_stats_enabled']) {
                $data->sexman = $model->getSexMan();
                $data->sexwoman = $model->getSexWoman();
                $totalSex = $data->sexman + $data->sexwoman;
                if ($totalSex > 0) {
                    $data->sexman_percent = intval(100 * ($data->sexman / $totalSex));
                    $data->sexwoman_percent = intval(100 * ($data->sexwoman / $totalSex));
                } else {
                    $data->sexman_percent = 0; // Или любое другое значение по умолчанию
                    $data->sexwoman_percent = 0; // Или любое другое значение по умолчанию
                }
            }

            return $data;
        });

        $raceCounts = null;
        if (config('app.statistics.races_stats_enabled')) {
            $raceCounts = ModelFactory::l2jModel(config('app.l2server_version'))->countRaces();
        }



        return view('template::stats.index', [
            'list' => $data,
            'raceCounts' => $raceCounts
        ]);
    }

    public function top_players() {
        $data = Cache::remember('top_players_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getTopPlayers() as $item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');

                $onlinetime = Carbon::parse($item->onlinetime);
                $item->onlinetime = $onlinetime->diffForHumans([
                    'parts' => 2, // Максимум 3 части (например, "5 месяцев 1 неделя 6 дней")
                    'join' => true // Склеивает части с "и" (напр., "5 месяцев и 1 неделя")
                ]);
                $data[] = $item;
            }
            return $data;
        });


        return view('template::stats.top-players', [
            'top_players' => $data
        ]);
    }

    public function top_rich() {
        $model = ModelFactory::l2jModel(config('app.l2server_version'));
        $data = $model->getTopRich();

        return view('template::stats.top-rich', [
            'top_rich' => $data
        ]);
    }

    public function top_pvp() {
        $data = Cache::remember('top_pvp_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getPvpPlayers() as $item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');

                // Преобразуем секунды в человекочитаемый формат через Carbon
                try {
                    $milliseconds = (int)$item->onlinetime;
                    $seconds = (int)($milliseconds / 1000); // 🔥 делим миллисекунды на 1000
                    $interval = CarbonInterval::seconds($seconds)->cascade();
                    $item->onlinetime = $interval->forHumans([
                        'short' => true, // Короткий формат
                        'parts' => 2,    // Максимум 2 части, например "5h 30m"
                    ]);
                } catch (\Exception $e) {
                    $item->onlinetime = 'Неизвестно';
                }


                $data[] = $item;
            }
            return $data;
        });

        return view('template::stats.top-pvp', [
            'pvp' => $data
        ]);
    }



    public function top_pk()
    {
        $data = Cache::remember('top_pk_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getPkPlayers() as $item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');

                try {
                    $milliseconds = (int)$item->onlinetime;
                    $seconds = (int)($milliseconds / 1000); // 🔥 делим миллисекунды на 1000
                    $interval = CarbonInterval::seconds($seconds)->cascade();
                    $item->onlinetime = $interval->forHumans([
                        'short' => true, // Короткий формат
                        'parts' => 2,    // Максимум 2 части, например "5h 30m"
                    ]);
                } catch (\Exception $e) {
                    $item->onlinetime = 'Неизвестно';
                }


                $data[] = $item;
            }
            return $data;
        });

        return view('template::stats.top-pk', [
            'pk' => $data
        ]);
    }

    public function clans() {
        $data = Cache::remember('clans_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getTopClans() as &$item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                $data[] = $item;
            }

            return $data;
        });

        return view('template::stats.clans', [
            'clans' => $data
        ]);
    }

    public function ally() {
        $data = Cache::remember('ally_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getTopAlly() as $item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $data[] = $item;
            }

            return $data;
        });

        return view('template::stats.alliance', [
            'alliance' => $data
        ]);
    }

    public function castle() {
        $data = Cache::remember('castle_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];

            // можно удалить цикл он для вывода кастомных замков
//            foreach ($model->getClanHallById(64) as &$clanHall) {
//                $clanHall->castle_name = "Fortress of the Dead";
//                $clanHall->castle_img = asset('storage/castles/' . $clanHall->id . '.jpg');
//                $clanHall->ally_crest = crest('ally', $clanHall->ally_id, $clanHall->allyCrest);
//                $clanHall->clan_crest = crest('clan', $clanHall->clan_id, $clanHall->clanCrest);
//                $clanHall->tax_percent = null;
//                $clanHall->treasury = null;
//                $clanHall->clan_skills = null;
//                if ($clanHall->siege_date != 0) {
//                    $siegeDateTime = Carbon::createFromTimestampMs($clanHall->siege_date);
//                    $siegeDateTime->timezone(config('app.timezone')); // Установите нужный часовой пояс
//                    $clanHall->siege_date = $siegeDateTime->isoFormat('DD MMMM YYYY - HH:mm');
//                }
//                $data[] = $clanHall;
//            }

            foreach ($model->getCastle() as &$castleItem) {

//                $castleItem->clan_skills = $model->getClanSkills($castleItem->clan_id);
//
//                foreach ($castleItem->clan_skills as &$clanSkillItem) {
//                    // Ваши операции с данными о навыках клана, например:
//                    $clanSkillItem->clan_id;
//                    $clanSkillItem->skill_name = skillName($clanSkillItem->skill_id);
//                    $clanSkillItem->skill_img = skillIcon($clanSkillItem->skill_id);
//                }

                if ($castleItem->siege_date != 0) {
                    try {
                        // Преобразуем миллисекунды в секунды и форматируем дату
                        $castleItem->siege_date = Carbon::createFromTimestampMs($castleItem->siege_date)
                            ->format('d.m.Y H:i');
                    } catch (\Exception $e) {
                        $castleItem->siege_date = 'Неизвестно';
                    }
                }

                $castleItem->castle_img = base_url('img/lin2web/castles/' . $castleItem->castle_id . '.jpg');
                $castleItem->ally_crest = crest('ally', $castleItem->ally_id, $castleItem->allyCrest);
                $castleItem->clan_crest = crest('clan', $castleItem->clan_id, $castleItem->clanCrest);
                $data[] = $castleItem;
            }

            return $data;
        });


        return view('template::stats.castle', [
            'castle' => $data
        ]);
    }

    public function clanhall() {
        $data = Cache::remember('clanhall_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));

            $data = [];
            foreach ($model->getClanHall() as &$item) {
                $item->ally_crest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clan_crest = crest('clan', $item->clan_id, $item->clanCrest);
                $data[] = $item;
            }

            return $data;
        });

        return view('template::stats.clanhall', [
            'clanhall' => $data
        ]);
    }

    public function epicBosses() {
        $data = Cache::remember('top_epic_bosses_data', config('app.statistics.stats_cached'), function () {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));
            $data = [];


            if(config('app.statistics.beleth_stats_enabled')) {
                $belethStatus = $model->getBelethStatus();
                if ($belethStatus->value) {
                    $beleth = new \stdClass();
                    $beleth->bossId = 29118;
                    $beleth->epic_title = 'Beleth';
                    $beleth->level = 80;
                    // Подготовка информации о времени возрождения и изображении, если необходимо

                    if ($belethStatus->value > 0) {
                        $timestampMilliseconds = $belethStatus->value;
                        $timestampSeconds = $timestampMilliseconds / 1000; // Преобразование миллисекунд в секунды
                        $dateTime = Carbon::createFromTimestamp($timestampSeconds, config('app.timezone')); // Указываем временную зону "Europe/Moscow"
                        $formattedDateTime = $dateTime->isoFormat('DD MMMM YYYY - HH:mm');

                        $beleth->respawn = $formattedDateTime;
                    } else {
                        // Если значение равно 0, возможно, вам нужно обработать этот случай отдельно
                        $beleth->respawn = 'Неизвестно';
                    }

                    $beleth->epic_img = asset('storage/epic/' . $beleth->bossId . '.jpg');
                    $data[] = $beleth; // Добавляем Белефа к общему списку
                }
            }

            $defaultRaidBosses = [
                (object)[
                    'id' => 29006,
                    'epic_title' => 'Core',
                    'level' => 85,
                    'respawn_delay' => 0,
                ],
                (object)[
                    'id' => 29014,
                    'epic_title' => 'Orfen',
                    'level' => 85,
                    'respawn_delay' => 0,
                ],
                (object)[
                    'id' => 29001,
                    'epic_title' => 'Queen Ant',
                    'level' => 85,
                    'respawn_delay' => 0,
                ],
                (object)[
                    'id' => 29022,
                    'epic_title' => 'Zaken',
                    'level' => 85,
                    'respawn_delay' => 0,
                ],
//                (object)[
//                    'id' => 25325,
//                    'epic_title' => 'Flame of Splendor Barakiel',
//                    'level' => 85,
//                    'respawn_delay' => 0,
//                ],
            ];

            $raidBosses = $model->getRaidBosses();

            foreach ($defaultRaidBosses as $defaultBoss) {
                $raidBoss = $raidBosses->firstWhere('id', $defaultBoss->id);
                if ($raidBoss) {
                    $defaultBoss->respawn_delay = $raidBoss->respawn_delay;
                }
            }

            foreach ($defaultRaidBosses as &$raidBoss) {
                if ($raidBoss->respawn_delay > 0) {
                    $timestamp = $raidBoss->respawn_delay;
                    $dateTime = Carbon::createFromTimestamp($timestamp, config('app.timezone'));
                    $formattedDateTime = $dateTime->isoFormat('DD MMMM YYYY - HH:mm');
                    $raidBoss->respawn = $formattedDateTime;
                } else {
                    $raidBoss->respawn = 0;
                }

                $raidBoss->epic_img = asset('storage/epic/' . $raidBoss->id . '.jpg');
            }
            $data = $defaultRaidBosses;

            $epicBosses = $model->getEpicBosses();

            foreach ($epicBosses as &$epicBoss) {
                $item = clone $epicBoss;
                switch ($item->bossId) {
                    case 29020:
                        $item->epic_title = 'Baium';
                        $item->level = 85;
                        break;
                    case 29068:
                        $item->epic_title = 'Antharas';
                        $item->level = 85;
                        break;
                    case 29028:
                        $item->epic_title = 'Valakas';
                        $item->level = 85;
                        break;
                    case 29045:
                        $item->epic_title = 'Frintezza';
                        $item->level = 85;
                        break;
                }

                if ($item->respawnDate > 0) {
                    $timestamp = $item->respawnDate;
                    $dateTime = Carbon::createFromTimestamp($timestamp, config('app.timezone')); // Указываем временную зону "Europe/Moscow"
                    $formattedDateTime = $dateTime->isoFormat('DD MMMM YYYY - HH:mm');

                    $item->respawn = $formattedDateTime;
                } else {
                    $item->respawn = $item->respawnDate;
                }

                $item->epic_img = asset('storage/epic/' . $item->bossId . '.jpg');
                $data[] = $item;
            }

            return $data;
        });

        return view('template::stats.epic-bosses', [
            'epic' => $data
        ]);
    }

    public function topskills() {
        $data = Cache::remember('top_skills_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;
            $rawData = $conn->getTopSkills();
            $data = [];

            foreach ($rawData as $item) {
                $skillData = [
                    'name' => $item->name,
                    'skill_id' => $item->skill_id,
                    'skill_level' => $item->skill_level,
                    'skill_name' => skillName($item->skill_id),
                    'skill_icon' => skillIcon($item->skill_id)
                ];
                $data[] = $skillData;
            }

            return $data;
        });

        return response()->json([
            'topskills' => $data
        ],200);
    }


    public function topitems() {
        $data = Cache::remember('top_items_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;

            $data = [];
            foreach ($conn->getTopItems() as &$item) {

                $item->name;
                $item->item_id;
                $item->enchant_level;
                $item->item_name = itemName($item->item_id);
                $item->item_icon = itemIcon($item->item_id);
                $item->item_desc = itemDesc($item->item_id);
                $data[] = $item;
            }
            return $data;
        });

        //Log::info('Top items data:', $data); // Здесь происходит логирование

        return response()->json([
            'items' => $data
        ],200);
    }


    public function bestPlayers()
    {
//        function convert_seconds($seconds)
//        {
//            $dt1 = new DateTime("@0");
//            $dt2 = new DateTime("@$seconds");
//            return $dt1->diff($dt2)->format('%a дней, %h часов, %i минуты и %s секунд');
//        }
//        echo convert_seconds(512884);

        $data = Cache::remember('top_best_players_data', config('app.statistics.stats_cached'), function () {

            $conn = new Lucera;

            $data = [];
            foreach ($conn->getBestPlayers() as $item) {
                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->char_name;
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');
                $item->name;
                $item->onlinetime = CarbonInterval::seconds($item->onlinetime)->cascade()->forHumans();
                $item->pvpkills;
                $item->pkkills;
                $data[] = $item;
            }

            return $data;
        });

        return response()->json([
            'players' => $data
        ],200);
    }

    public function hpPlayers()
    {
        $data = Cache::remember('top_hpPlayers_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;

            $data = [];
            foreach ($conn->getHpPlayers() as $item) {
                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->char_name;
                $item->curHp = ceil($item->curHp / 60);
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');
                $item->clanName;
                $data[] = $item;
            }
            return $data;
        });

        return response()->json([
            'hp' => $data
        ],200);
    }

    public function mpPlayers()
    {
        $data = Cache::remember('top_mpPlayers_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;

            $data = [];
            foreach ($conn->getMpPlayers() as $item) {
                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->char_name;
                $item->mp = ceil($item->mp / 60);
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');
                $item->clanName;
                $data[] = $item;
            }

            return $data;
        });

        return response()->json([
            'mp' => $data
        ],200);
    }

//    public function richPlayers()
//    {
//        $data = Cache::remember('top_rich_data', config('app.statistics.stats_cached'), function () {
//            $conn = new Lucera;
//
//            $data = [];
//            foreach ($conn->getRichPlayers() as $item) {
//                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
//                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
//                $item->count = number_format($item->count, 0);
//                $item->char_name;
//                $item->class_id = class_id($item->class_id);
//                $item->sex = ($item->sex == 0) ? 'М' : __('F');
//                $item->clanName;
//                $data[] = $item;
//            }
//
//            return $data;
//        });
//
//        return response()->json([
//            'rich' => $data
//        ],200);
//    }

    public function heroPlayers()
    {
        $data = Cache::remember('top_heroes_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;

            $data = [];
            foreach ($conn->getHeroPlayers() as $item) {
                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->char_name;
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');
                $item->clanName;
                $data[] = $item;
            }

            return $data;
        });

        return response()->json([
            'hero' => $data
        ],200);
    }

    public function baronPlayers()
    {
        $data = Cache::remember('top_baron_data', config('app.statistics.stats_cached'), function () {
            $conn = new Lucera;

            $data = [];
            foreach ($conn->getBaronPlayers() as $item) {
                $item->allyCrest = crest('ally', $item->ally_id, $item->allyCrest);
                $item->clanCrest = crest('clan', $item->clan_id, $item->clanCrest);
                $item->char_name;
                $item->class_id = class_id($item->class_id);
                $item->sex = ($item->sex == 0) ? 'М' : __('F');
                $item->name;
                $item->onlinetime = CarbonInterval::seconds($item->onlinetime)->cascade()->forHumans();
                $item->pvpkills;
                $item->pkkills;
                $data[] = $item;
            }

            return $data;
        });

        return response()->json([
            'baron' => $data
        ],200);
    }

    public function top_balance() {
        $data = Cache::remember('top_balance_data', config('app.statistics.stats_cached'), function () {
            // Получаем топ-25 пользователей по убыванию баланса
            return \App\Models\User::orderByDesc('balance')
                ->limit(25)
                ->get(['id', 'name', 'email', 'balance']); // Выбираем нужные поля
        });

        return view('template::stats.top-balance', [
            'top_balance' => $data
        ]);
    }

}
