@section('title', __('Syncing accounts'))
@extends('template::style.dashboard')
@section('content')
    <div class="title-center">
        <p>{{ __('Here you can sync your game account with your master account') }}</p>
    </div>

    <form id="sync-game-block" action="{{ route('sync-game-account.store') }}" method="POST">
        @csrf

        <!-- Name -->
        <div class="form-group">
            <label for="name">{{ __('Your game login') }}</label>
            <input id="name" class="form-control @error('name') is-invalid @enderror" type="text" name="name" value="{{ old('name') }}" required autofocus autocomplete="name" />
            @error('name')
                <div class="alert alert-danger">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div class="form-group">
            <label for="password">{{ __('Password') }}</label>
            <div class="password-group">
                <input id="password" class="form-control @error('password') is-invalid @enderror" type="password" name="password" required autocomplete="new-password" />
                <span class="input-group-text password-toggle" data-target="password">👁️</span>
            </div>
            @error('password')
                <div class="alert alert-danger">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password Confirmation -->
        <div class="form-group">
            <label for="password_confirmation">{{ __('Confirm Password') }}</label>
            <div class="password-group">
                <input id="password_confirmation" class="form-control @error('password_confirmation') is-invalid @enderror" type="password" name="password_confirmation" required autocomplete="new-password" />
                <span class="input-group-text password-toggle" data-target="password_confirmation">👁️</span>
            </div>
            @error('password_confirmation')
                <div class="alert alert-danger">{{ $message }}</div>
            @enderror
        </div>

        <!-- Log In Buttons -->
        <div class="log-in-buttons">
            <button type="submit" class="normal-link"><span>{{ __('Synchronize') }}</span></button>
        </div>
    </form>

    @if (session('message'))
        <div class="alert alert-success">
            {{ session('message') }}
        </div>
    @endif

@endsection
