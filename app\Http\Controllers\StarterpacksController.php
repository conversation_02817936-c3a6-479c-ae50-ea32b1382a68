<?php

namespace App\Http\Controllers;

use App\Models\ModelFactory;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StarterpacksController extends Controller
{
    public function index()
    {
        if (!\Auth::check()) {
            return redirect()->route('login');
        }

        return view('template::dashboard.starterpacks');
    }

    public function store(Request $request)
    {
        $starterpacks = config('app.starterpacks');

        // Получаем индекс (ID) стартерпака
        $packIndex = (int) $request->spackid;
        $packsList = array_values($starterpacks); // Преобразуем в индексный массив

        // Проверка существования пакета
        if (!isset($packsList[$packIndex - 1])) {
            return redirect()->back()->with('error', 'This Startepack Not Found');
        }

        $selectedPack = $packsList[$packIndex - 1];
        $currentUser = auth()->user();

        // ❗ Проверка: уже куплен?
        if (config('app.starterpacks_repeat')) {
            $alreadyBought = DB::table('starterpack_user')
                ->where('user_id', $currentUser->id)
                ->where('starterpack_id', $packIndex)
                ->exists();

            if ($alreadyBought) {
                return redirect()->back()->with('error', __('You have already purchased this starter pack.'));
            }
        }

        // Проверка на блокировку
        if (Cache::has('send_starterpacks_' . $currentUser->id)) {
            return redirect()->back()->with('error', 'Пожалуйста, подождите, пока предыдущая операция завершится.');
        }

        // Проверка баланса
        if ($selectedPack['price'] > $currentUser->balance) {
            return redirect()->back()->with('error', __('Insufficient funds.'));
        }

        // Блокировка на 10 секунд
        Cache::put('send_starterpacks_' . $currentUser->id, true, 10);

        try {
            // Добавление предметов в склад
            foreach ($selectedPack['items'] as $item) {
                $existingItem = Warehouse::where('user_id', $currentUser->id)
                    ->where('item_id', $item['item_id'])
                    ->first();

                if ($existingItem) {
                    $existingItem->count += $item['item_count'];
                    $existingItem->save();
                } else {
                    (new Warehouse)->addItem($currentUser->id, $item['item_id'], $item['item_count'], $item['enchant']);
                }
            }

            // Списание монет
            $currentUser->decrement('balance', $selectedPack['price']);

            // ✅ Сохраняем, что он купил этот стартерпак
            if (config('app.starterpacks_repeat')) {
                DB::table('starterpack_user')->insert([
                    'user_id' => $currentUser->id,
                    'starterpack_id' => $packIndex,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Успешно
            Cache::forget('send_starterpacks_' . $currentUser->id);
            return redirect()->route('warehouse')->with('success_starterpack', __('Congratulations! You have purchased the starter pack.'));
        } catch (\Exception $e) {
            Cache::forget('send_starterpacks_' . $currentUser->id);
            \Log::error('Ошибка при покупке стартерпака: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Произошла ошибка при покупке пакета.');
        }
    }

}
