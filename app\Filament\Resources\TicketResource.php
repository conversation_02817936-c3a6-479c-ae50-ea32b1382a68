<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TicketResource\Pages;
use App\Models\Ticket;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;

class TicketResource extends Resource
{
    protected static ?string $model = Ticket::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket'; // Изменена иконка на "ticket"
    protected static ?int $navigationSort = 2;


    public static function getNavigationLabel(): string
    {
        return __('Tickets');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Tickets');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Название тикета')
                    ->disabled(),

                Forms\Components\Select::make('status')
                    ->label('Статус')
                    ->options([
                        'new' => 'Новый',
                        'in_progress' => 'В работе',
                        'closed' => 'Закрыт',
                    ])
                    ->required(),

                Forms\Components\Repeater::make('messages')
                    ->label('Переписка')
                    ->schema([
                        Forms\Components\TextInput::make('author')
                            ->label('Автор')
                            ->disabled(),

                        Forms\Components\Textarea::make('message')
                            ->label('Сообщение')
                            ->disabled(),

                        Forms\Components\FileUpload::make('attachment')
                            ->label('Вложение')
                            ->image() // Отображаем изображение
                            ->disabled(),

                        Forms\Components\TextInput::make('created_at')
                            ->label('Дата и время')
                            ->disabled(),
                    ])
                    ->columns(1)
                    ->disabled(),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Тикет')
                    ->limit(50),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Пользователь'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Статус')
                    ->sortable(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Приоритет')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Дата создания')
                    ->dateTime(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Фильтр по пользователю')
                    ->options(function () {
                        return User::pluck('name', 'id')->toArray();
                    })
                    ->column('user_id'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Action::make('Ответить')
                    ->label('Ответить')
                    ->modalHeading('Ответ на тикет')
                    ->form([
                        Forms\Components\Textarea::make('message')
                            ->label('Ответ')
                            ->required()
                            ->placeholder('Введите сообщение...'),
                    ])
                    ->action(function (array $data, Ticket $record) {
                        $record->messages()->create([
                            'user_id' => null, // Администратор
                            'message' => $data['message'],
                        ]);
                    }),

                Tables\Actions\DeleteAction::make()
                    ->label('Удалить') // Перевод кнопки на русский
                    ->action(function (Ticket $record) {
                        $record->delete(); // Удаление тикета
                    })
                    ->color('danger') // Устанавливаем красный цвет для кнопки
                    ->modalHeading('Подтвердите удаление') // Заголовок модального окна
                    ->modalSubheading('Вы уверены, что хотите удалить этот тикет? Это действие невозможно отменить.') // Описание в модальном окне
                    ->modalButton('Удалить тикет'),
        ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function canCreate(): bool
    {
        return false;
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTickets::route('/'),
            'create' => Pages\CreateTicket::route('/create'),
            'edit' => Pages\EditTicket::route('/{record}/edit'),
        ];
    }
}
