<?php

namespace App\Http\Controllers;

use App\Models\News;

class NewsController extends Controller
{
    public function index()
    {
        $news = News::where('status', 'published')
            ->orderByRaw("CASE WHEN show_sector = 'main' THEN 0 ELSE 1 END") // 'main' ставится выше всех
            ->orderBy('created_at', 'desc') // остальные сортируются по дате
            ->paginate(10); // Разбиваем на страницы по 10 новостей

        return view('template::pages.news_page', compact('news'));
    }

    public function show($slug)
    {
        $news = News::where('slug', $slug)->firstOrFail();
        $news->formatted_date = $news->created_at->format('d.m.Y');

        return view('template::pages.news_show', compact('news'));
    }

}
