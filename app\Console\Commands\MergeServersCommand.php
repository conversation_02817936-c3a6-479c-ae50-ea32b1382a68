<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\MergeServerController;
use Illuminate\Support\Facades\Log;

class MergeServersCommand extends Command
{
    protected $signature = 'merge:servers {task}';
    protected $description = 'Merge servers data';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $task = $this->argument('task');

        Log::info('MergeServersCommand started with task: ' . $task);
        $this->info('MergeServersCommand started with task: ' . $task);

        $controller = new MergeServerController();

        switch ($task) {
            case '1':
                $controller->index();
                $this->info('Task 1 Merge servers successfully.');
                Log::info('Task 1 Merge servers successfully.');
                break;
            case '2':
                $controller->taskPremiumAccount();
                $this->info('Task 2 Premium accounts successfully.');
                Log::info('Task 2 Premium accounts successfully.');
                break;
            case '3':
                $controller->taskCustomMacrosesQuest();
                $this->info('Task 3 Quests, Macroses successfully.');
                Log::info('Task 3 Quests, Macroses successfully.');
                break;
            default:
                $this->error('Invalid task specified.');
                Log::error('Invalid task specified.');
                break;
        }

        $this->info('Merge servers job finished.');
        Log::info('Merge servers job finished.');
    }
}
