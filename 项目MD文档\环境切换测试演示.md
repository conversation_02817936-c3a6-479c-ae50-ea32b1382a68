# 环境切换测试演示

## 🎯 演示目的
验证测试功能的环境安全机制是否正常工作

## 📋 测试步骤

### 1. 当前状态 (开发环境)
```bash
# 查看当前环境
grep APP_ENV .env
# 输出: APP_ENV=local

# 访问测试页面
curl -I http://localhost/db-test
# 应该返回: HTTP/1.1 200 OK
```

### 2. 模拟生产环境
```bash
# 备份当前配置
cp .env .env.backup

# 临时修改为生产环境
sed -i 's/APP_ENV=local/APP_ENV=production/' .env

# 重启容器使配置生效
docker-compose restart laravel.test

# 测试访问 (应该返回404)
curl -I http://localhost/db-test
# 应该返回: HTTP/1.1 404 Not Found
```

### 3. 恢复开发环境
```bash
# 恢复配置
cp .env.backup .env

# 重启容器
docker-compose restart laravel.test

# 验证恢复 (应该正常访问)
curl -I http://localhost/db-test
# 应该返回: HTTP/1.1 200 OK
```

## ✅ 预期结果
- **开发环境 (APP_ENV=local)**: 测试页面正常访问
- **生产环境 (APP_ENV=production)**: 测试页面返回404错误
- **环境恢复**: 测试页面重新可用

## 🛡️ 安全验证
这个机制确保了：
1. 生产环境不会暴露测试功能
2. 敏感的数据库信息不会泄露
3. 开发工具不会在生产环境中可用

## 📝 注意事项
- 测试完成后务必恢复原始配置
- 生产环境切换会影响所有功能，仅用于测试
- 建议在独立的测试环境中进行此类验证
