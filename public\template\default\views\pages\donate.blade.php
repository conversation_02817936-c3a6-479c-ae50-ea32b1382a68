@section('title', __('Donate'))
@extends('template::layouts.public')

<link rel="stylesheet" href="{{ base_url('css/nouislider.min.css') }}">
<script src="{{ base_url('js/nouislider.min.js') }}"></script>

@section('content')
    <div class="other-bg"></div>
    <section id="other">
        <div class="wrap">
            <form id="payment-form" action="{{ route('donate.store') }}" method="POST">
                <div class="payment-pay">
                    @csrf
                    <div class="donate-title-block">
                        <div class="donate-title">{{ __('Refill the balance') }}</div>
                        <p class="price_donate">{{ __('The price of one') }} {{ config('app.custom_config.donate_coin_name') }} = <span class="price_col">{{ config('app.pay_system.item_price') }}</span> {{ config('app.pay_system.symbol') }}*</p>
                    </div>
                    <div class="form-group">
                        <div class="pay-currency">
                            @foreach (config('app.pay_system.coin_price') as $currency => $price)
                                <div class="curr-item">
                                    <p class="curr_symbol">{{ $currency }}</p>
                                    <p class="currency" data-currency="{{ $currency }}">0.00</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="form-group">
                        {{--                <label for="pay_select">{{ __('Select server') }}:</label>--}}
                        <select name="server" class="form-control">
                            @foreach(config('app.getServers')() as $serverKey => $serverDetails)
                                <option value="{{ $serverKey }}">
                                    {{ $serverDetails['server_name'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        {{--                <label for="char_name">{{ __('Character name') }}:</label>--}}
                        <input type="text" name="char_name" class="form-control" id="char_name" placeholder="{{ __('Enter the character name') }}" required="">
                        @if(Session::has('char_name'))
                            <div class="text-danger">
                                {{ Session::get('char_name') }}
                            </div>
                        @endif
                    </div>
                    <div class="form-group">
                        {{--                <label for="count">{{ __('Specify the quantity of coins') }}:</label>--}}
                        <div class="payment__input">
                            <input type="number" name="count" class="form-control" id="count" placeholder="{{ __('How many coins would you like to purchase?') }}" required="">
                            <div class="payment_bonus_price">+<span id="payment-bonus">0</span></div>
                        </div>
                        @if($errors->has('count'))
                            <div class="alert alert-danger">
                                <p>{{ $errors->first('count') }}</p>
                                <a href="{{ url()->current() }}" class="close"></a>
                            </div>
                        @endif
                    </div>
                    <div class="form-group">
                        <div id="payment-range" class="payment__range"></div>
                    </div>

                    <div class="form-group">
                        <div class="payment_select">
                            @if(config('app.pay_system.midtrans_enable'))
                                <div class="pay_method" data-method="midtrans">
                                    <img src="{{ base_url('img/lin2web/icon-midtrans.png') }}" alt="Midtrans">
                                </div>
                            @endif
                            @if(config('app.pay_system.freekassa_enable'))
                                <div class="pay_method active" data-method="freekassa">
                                    <img src="{{ base_url('img/lin2web/icon-freekassa.png') }}" alt="Freekassa">
                                </div>
                            @endif
                            @if(config('app.pay_system.morune_enable'))
                                <div class="pay_method" data-method="morune">
                                    <img src="{{ base_url('img/lin2web/icon-morune.png') }}" alt="Morune">
                                </div>
                            @endif
                            @if(config('app.pay_system.primepayments_enable'))
                                <div class="pay_method" data-method="primepayments">
                                    <img src="{{ base_url('img/lin2web/icon-primepayments.png') }}" alt="primepayments">
                                </div>
                            @endif
                            @if(config('app.pay_system.paypal_enable'))
                                <div class="pay_method" data-method="paypal">
                                    <img src="{{ base_url('img/lin2web/icon-paypal.png') }}" alt="Paypal">
                                </div>
                            @endif
                            @if(config('app.pay_system.stripe_enable'))
                                <div class="pay_method" data-method="stripe">
                                    <img src="{{ base_url('img/lin2web/icon-stripe.png') }}" alt="Stripe">
                                </div>
                            @endif
                            @if(config('app.pay_system.enot_enable'))
                                <div class="pay_method" data-method="enot">
                                    <img src="{{ base_url('img/lin2web/icon-enot.png') }}" alt="Enot">
                                </div>
                            @endif
                        </div>
                        <input type="hidden" id="pay_select" name="pay_select" value="freekassa">
                    </div>
                    <div class="form-group">
                        <div class="btn-wrapper btn-full">
                            <button type="submit" class="btn-main"><span class="text">{{ __('Pay') }}</span></button>
                        </div>

                        <p class="total_text">{{ __("You'll get") }}: <span id="payment-total" class="orange">0</span> {{ config('app.custom_config.donate_coin_name') }}</p>
                    </div>
                    @if($errors->has('payment_error'))
                        <div class="alert alert-danger">
                            <p>{{ $errors->first('payment_error') }}</p>
                            <a href="{{ url()->current() }}" class="close"></a>
                        </div>
                    @endif
                </div>

                <div id="payment_bonus_list">
                    <div class="donate-title-block">
                        <div class="donate-title">{{ __('Bonuses') }}</div>
                        <p>{{ __('Get awesome bonuses for topping up your balance!') }}</p>
                    </div>

                    @if (config('app.bonus_system_percent_enable'))
                        <div class="payment-bonus">
                            @php
                                $levels = config('app.bonus_system_percent.bonus_levels');
                                $ranges = [];
                                $prev = null;

                                foreach ($levels as $coins => $level) {
                                    if ($prev) {
                                        $ranges[] = [
                                            'min' => $prev,
                                            'max' => $coins - 1,
                                            'bonus_percent' => $levels[$prev]['bonus_percent'],
                                        ];
                                    }
                                    $prev = $coins;
                                }

                                // Последний диапазон
                                if ($prev) {
                                    $ranges[] = [
                                        'min' => $prev,
                                        'max' => null, // Бесконечный верхний предел
                                        'bonus_percent' => $levels[$prev]['bonus_percent'],
                                    ];
                                }
                            @endphp

                            @foreach ($ranges as $range)
                                <div class="bonus-item">
                                    <p>
                                        <small>{{ __('From') }}</small> {{ $range['min'] }}
                                        @if ($range['max']) — {{ $range['max'] }} @else+ @endif CoL
                                    </p>
                                    <p><span>+{{ $range['bonus_percent'] }} % {{ __('Bonus') }}</span></p>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </form>
        </div>
    </section>

    <script>
        window.bonusSystemPercent = @json(config('app.bonus_system_percent.bonus_levels'));
        window.coinPrice = @json(config('app.pay_system.coin_price'));
        window.currencyRate = @json(config('app.pay_system.currency_rate'));
        window.itemPrice = {{ config('app.pay_system.item_price') }};
    </script>

@endsection
