<?php

namespace App\Services\Payment;

use App\Services\Payment\PaymentProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PrimePaymentsService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;
    }

    public function createPayment($payment_id, $count)
    {
        $currency = config('app.pay_system.primepayments_currency'); // Валюта
        $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
        // Расчет суммы платежа
        $sum = (int)ceil($count * $coin_price);

        $customFields = "pay_id@{$payment_id}";
        $email = "{$payment_id}@test.com";

        $data = [
            'action' => 'initPayment',
            'project' => config('app.pay_system.primepayments_project_id'),
            'sum' => $sum,
            'currency' => $currency,
            'innerID' => $customFields,
            'payWay' => '1', // Payment method (e.g., 1 for cards)
            'email' => $email,
            'returnLink' => 1,
        ];

        $secret1 = config('app.pay_system.primepayments_secret_key_1');
        $data['sign'] = md5($secret1 . $data['action'] . $data['project'] . $data['sum'] . $data['currency'] . $data['innerID'] . $data['email'] . $data['payWay']);

        $response = Http::asForm()->post('https://pay.primepayments.io/API/v2/', $data);

        $answer = $response->json();

        if (isset($answer['status']) && $answer['status'] === 'OK') {
            return $answer['result'] ?? null;
        }

        Log::error('PrimePayments: Error creating payment', ['response' => $response->body()]);
        throw new Exception('Failed to create PrimePayments payment.');
    }

    public function handleWebhook(Request $request)
    {
        try {
            // Проверка IP-адреса откуда идет вебхук
//            if (!$this->isAllowedIp($request->ip())) {
//                Log::warning('FreeKassa: Access denied for IP', ['ip' => $request->ip()]);
//                return response('Access Denied', 403);
//            }

            $request->validate([
                'orderID' => 'required',
                'payWay' => 'required',
                'innerID' => 'required',
                'sum' => 'required',
                'webmaster_profit' => 'required',
                'sign' => 'required',
            ]);

            $orderID = $request->input('orderID');
            $payWay = $request->input('payWay');
            $innerID = $request->input('innerID');
            $sum = $request->input('sum');
            $webmaster_profit = $request->input('webmaster_profit');
            $received_sign = $request->input('sign');

            $secret2 = config('app.pay_system.primepayments_secret_key_2');
            $calculated_sign = md5($secret2 . $orderID . $payWay . $innerID . $sum . $webmaster_profit);

            if ($calculated_sign !== $received_sign) {
                Log::error('PrimePayments: Invalid signature', ['orderID' => $orderID]);
                return response('Invalid signature', 403);
            }

            $arr = explode('@', $innerID);
            $payment_id = $arr[1];

            $this->paymentProcessingService->processPayment($payment_id);

            return response('OK', 200);
        } catch (Exception $e) {
            Log::error('PrimePayments: Error handling webhook', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }

    // разрешенные IP для отправки для отправки вебхука
    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '************',
        ];

        return in_array($ip, $allowedIps);
    }
}
