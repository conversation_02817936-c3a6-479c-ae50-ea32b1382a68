{"(and :count more error)": "(and :count more error)", "(and :count more errors)": "(and :count more errors)", "A new verification link has been sent to the email address you provided during registration.": "A new verification link has been sent to the email address you provided during registration.", "A new verification link has been sent to your email address.": "A new verification link has been sent to your email address.", "A Timeout Occurred": "A Timeout Occurred", "Accept": "Accept", "Accepted": "Accepted", "Action": "Action", "Actions": "Actions", "Add": "Add", "Admin": "Admin", "Agree": "Agree", "All rights reserved.": "All rights reserved.", "Already registered?": "Already registered?", "Already Reported": "Already Reported", "Archive": "Archive", "Are you sure you want to delete your account?": "Are you sure you want to delete your account?", "Assign": "Assign", "Attach": "Attach", "Bad Gateway": "Bad Gateway", "Bad Request": "Bad Request", "Bandwidth Limit Exceeded": "Bandwidth Limit Exceeded", "Browse": "Browse", "Cancel": "Cancel", "Choose": "<PERSON><PERSON>", "Choose :name": "Choose :name", "Choose File": "Choose <PERSON>", "Choose Image": "Choose Image", "Click here to re-send the verification email.": "Click here to re-send the verification email.", "Click to copy": "Click to copy", "Client Closed Request": "Client Closed Request", "Close": "Close", "Collapse": "Collapse", "Collapse All": "Collapse All", "Comment": "Comment", "Confirm": "Confirm", "Confirm Password": "Confirm Password", "Conflict": "Conflict", "Connect": "Connect", "Connection Closed Without Response": "Connection Closed Without Response", "Connection Timed Out": "Connection Timed Out", "Continue": "Continue", "Create": "Create", "Created": "Created", "Current Password": "Current Password", "Dashboard": "Dashboard", "Delete": "Delete", "Delete Account": "Delete Account", "Detach": "<PERSON><PERSON>", "Details": "Details", "Disable": "Disable", "Discard": "Discard", "Done": "Done", "Down": "Down", "Duplicate": "Duplicate", "Duplicate: name": "Duplicate: name", "Edit": "Edit", "Edit :name": "Edit :name", "Email": "Email", "Email Password Reset Link": "Email Password Reset Link", "Enable": "Enable", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Expand": "Expand", "Expand All": "Expand All", "Expectation Failed": "Expectation Failed", "Explanation": "Explanation", "Export": "Export", "Failed Dependency": "Failed Dependency", "File": "File", "Files": "Files", "Forbidden": "Forbidden", "Forgot your password?": "Forgot your password?", "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.": "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.", "Found": "Found", "Gateway Timeout": "Gateway Timeout", "Go Home": "Go Home", "Go to page :page": "Go to page :page", "Gone": "Gone", "Hello!": "Hello!", "Hide": "<PERSON>de", "Hide :name": "Hide :name", "Home": "Home", "HTTP Version Not Supported": "HTTP Version Not Supported", "I'm a teapot": "I'm a teapot", "If you did not create an account, no further action is required.": "If you did not create an account, no further action is required.", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:", "IM Used": "IM Used", "Image": "Image", "Impersonate": "Impersonate", "Impersonation": "Impersonation", "Import": "Import", "Import :name": "Import :name", "Insufficient Storage": "Insufficient Storage", "Internal Server Error": "Internal Server Error", "Introduction": "Introduction", "Invalid JSON was returned from the route.": "Invalid JSON was returned from the route.", "Invalid SSL Certificate": "Invalid SSL Certificate", "Length Required": "Length Required", "Like": "Like", "Load": "Load", "Localize": "Localize", "Locked": "Locked", "Log in": "Log in", "Log Out": "Log Out", "Login": "<PERSON><PERSON>", "Logout": "Logout", "Loop Detected": "Loop Detected", "Maintenance Mode": "Maintenance Mode", "Method Not Allowed": "Method Not Allowed", "Misdirected Request": "Misdirected Request", "Moved Permanently": "Moved Permanently", "Multi-Status": "Multi-Status", "Multiple Choices": "Multiple Choices", "Name": "Name", "Network Authentication Required": "Network Authentication Required", "Network Connect Timeout Error": "Network Connect Timeout Error", "Network Read Timeout Error": "Network Read Timeout Error", "New": "New", "New :name": "New :name", "New Password": "New Password", "No": "No", "No Content": "No Content", "Non-Authoritative Information": "Non-Authoritative Information", "Not Acceptable": "Not Acceptable", "Not Extended": "Not Extended", "Not Found": "Not Found", "Not Implemented": "Not Implemented", "Not Modified": "Not Modified", "of": "of", "OK": "OK", "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.": "Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "Open": "Open", "Open in a current window": "Open in a current window", "Open in a new window": "Open in a new window", "Open in a parent frame": "Open in a parent frame", "Open in the topmost frame": "Open in the topmost frame", "Open on the website": "Open on the website", "Origin Is Unreachable": "Origin Is Unreachable", "Page Expired": "Page Expired", "Pagination Navigation": "Pagination Navigation", "Partial Content": "Partial Content", "Password": "Password", "Payload Too Large": "Payload Too Large", "Payment Required": "Payment Required", "Permanent Redirect": "Permanent Redirect", "Please click the button below to verify your email address.": "Please click the button below to verify your email address.", "Precondition Failed": "Precondition Failed", "Precondition Required": "Precondition Required", "Preview": "Preview", "Price": "Price", "Processing": "Processing", "Profile": "Profile", "Profile Information": "Profile Information", "Proxy Authentication Required": "Proxy Authentication Required", "Railgun Error": "Railgun Error", "Range Not Satisfiable": "Range Not Satisfiable", "Regards": "Regards", "Register": "Register", "Remember me": "Remember me", "Request Header Fields Too Large": "Request Header Fields Too Large", "Request Timeout": "Request Timeout", "Resend Verification Email": "Resend Verification Email", "Reset Content": "Reset Content", "Reset Password": "Reset Password", "Reset Password Notification": "Reset Password Notification", "Restore": "Rest<PERSON>", "Restore :name": "Restore :name", "results": "results", "Retry With": "Retry With", "Save": "Save", "Save & Close": "Save & Close", "Save & Return": "Save & Return", "Save :name": "Save :name", "Saved.": "Saved.", "Search": "Search", "Search :name": "Search :name", "See Other": "See Other", "Select": "Select", "Select All": "Select All", "Send": "Send", "Server Error": "Server Error", "Service Unavailable": "Service Unavailable", "Session Has Expired": "Session Has Expired", "Settings": "Settings", "Show": "Show", "Show :name": "Show :name", "Show All": "Show All", "Showing": "Showing", "Solve": "Solve", "SSL Handshake Failed": "SSL Handshake Failed", "Submit": "Submit", "Subscribe": "Subscribe", "Switch": "Switch", "Switch To Role": "Switch To Role", "Switching Protocols": "Switching Protocols", "Tag": "Tag", "Tags": "Tags", "Temporary Redirect": "Temporary Redirect", "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.": "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.", "The given data was invalid.": "The given data was invalid.", "The response is not a streamed response.": "The response is not a streamed response.", "The response is not a view.": "The response is not a view.", "This is a secure area of the application. Please confirm your password before continuing.": "This is a secure area of the application. Please confirm your password before continuing.", "This password reset link will expire in :count minutes.": "This password reset link will expire in :count minutes.", "to": "to", "Toggle navigation": "Toggle navigation", "Too Early": "Too Early", "Too Many Requests": "Too Many Requests", "Translate": "Translate", "Translate It": "Translate It", "Unauthorized": "Unauthorized", "Unavailable For Legal Reasons": "Unavailable For Legal Reasons", "Unknown Error": "Unknown Error", "Unpack": "Unpack", "Unprocessable Entity": "Unprocessable Entity", "Unsubscribe": "Unsubscribe", "Unsupported Media Type": "Unsupported Media Type", "Up": "Up", "Update": "Update", "Update :name": "Update :name", "Update Password": "Update Password", "Update your account's profile information and email address.": "Update your account's profile information and email address.", "Upgrade Required": "Upgrade Required", "URI Too Long": "URI Too Long", "Use Proxy": "Use Proxy", "User": "User", "Variant Also Negotiates": "Variant Also Negotiates", "Verify Email Address": "Verify Em<PERSON> Address", "View": "View", "View :name": "View :name", "Web Server is Down": "Web Server is Down", "Whoops!": "Whoops!", "Yes": "Yes", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account.", "You're logged in!": "You're logged in!", "Your email address is unverified.": "Your email address is unverified.", "AccountLocked": "Your account has been blocked for :minutes.", "LoginOrPasswordIncorrect": "Invalid username or password. Attempts left: :attempts", "AccountLockedFixedTime": "Your account has been blocked for 10 minutes.", "Account Successfully Synchronized": "Your account has been successfully synchronized", "Account Not Found": "Account with this username was not found", "success_delivery": ":count :coin_name Success Delivery to :char_name.", "referral_bonus": "Attention! You receive :coin from each purchase made by your referral - :percent%", "referral_summary": "You have invited :count referral(s) and earned :amount bonus coins."}