<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class SwitchDatabaseConnection
{
    public function handle($request, Closure $next)
    {
        // Проверяем, выполняется ли запрос в Filament
        if ($request->is('admin/*')) {
            return $next($request);
        }

        // Получаем сервер из метода gameservers
        $connection = gameservers();

        // Устанавливаем текущее подключение
        DB::purge($connection);
        DB::reconnect($connection);

        return $next($request);
    }

}
