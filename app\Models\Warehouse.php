<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Warehouse extends Model
{
    use HasFactory;

    protected $connection = 'mysql';

    protected $fillable = [
        'user_id',
        'item_id',
        'count',
        'enchant',
        'status',
        'description',
        'server',
    ];

    public function addItem($user_id, $item_id, $count, $enchant) {
        $item = Warehouse::create([
            'user_id' => $user_id,
            'item_id' => $item_id,
            'count' => $count,
            'enchant' => $enchant
        ]);
    }
}
