<?php

namespace App\Helpers;
class CacheD {
    var $cachedport = 12006;
    var $cachedip = '*************';
    var $webadmin = 'Admin';
    var $version = null;

    //To have explicit output, use CacheD_chatterbox, better than editing this array
    var $fsockerror = false;
    var $socketerrors = array(
        "1" => True, "01" => False, "02" => False, "03" => False, "04" => False,
        "05" => False, "06" => False, "07" => False, "08" => False, "09" => False,
        "010" => False, "011" => False,	"012" => False,	"013" => False,	"014" => False,
        "015" => False,	"016" => False,	"017" => False,	"018" => False,	"019" => False,
        "020" => False,	"021" => False,	"022" => False,	"023" => False,	"024" => False,
        "025" => False,	"026" => False);



    function tounicode ($string){
        $rs="";
        for($i=0; $i < strlen($string); $i++)	{ $rs .= $string[$i].chr(0); }
        $rs .= chr(0).chr(0);
        return($rs);
    }

    function CacheDInteractive($buf) {
        $fp = fsockopen($this->cachedip, $this->cachedport , $errno, $errstr, 5);

        if (!$fp) {
            die("Ошибка подключения к CacheD: $errstr ($errno)");
        }

        $packet = pack("v", (strlen($buf) + 2)) . $buf;
        error_log("Отправляемый пакет: " . bin2hex($packet));

        if (fwrite($fp, $packet) === false) {
            fclose($fp);
            die("Ошибка: не удалось отправить данные на CacheD");
        }

        // Читаем длину ответа
        $rawLen = fread($fp, 2);
        if (strlen($rawLen) < 2) {
            fclose($fp);
            die("Ошибка: сервер CacheD вернул пустой ответ или разорвал соединение");
        }

        $len = unpack("v", $rawLen);
        error_log("Длина ответа: " . $len[1]);

        // Читаем ID ответа
        $ridRaw = fread($fp, 1);
        if (strlen($ridRaw) < 1) {
            fclose($fp);
            die("Ошибка: сервер CacheD не вернул ID ответа");
        }
        $rid = unpack("c", $ridRaw);

        // Читаем тело ответа
        $rs = '';
        for ($i = 0; $i < (($len[1] - 4) / 4); $i++) {
            $readRaw = fread($fp, 4);
            if (strlen($readRaw) < 4) {
                fclose($fp);
                die("Ошибка: сервер CacheD прислал недостаточно данных");
            }
            $read = unpack("i", $readRaw);
            $rs .= $read[1];
        }

        fclose($fp);

        if (!isset($this->socketerrors[$rs])) {
            die("Ошибка: неизвестный код ответа от CacheD ($rs)");
        }

        return $this->socketerrors[$rs];
    }


    function CheckCharacterPacket($char_id) {
        $buf = pack("c", 1);
        $buf .= pack("V", $char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetCharacterLocationPacket($char_id,$xloc,$yloc,$zloc) {
        $buf = pack("c", 2);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", 1);
        $buf .= pack("V", $xloc);
        $buf .= pack("V", $yloc);
        $buf .= pack("V", $zloc);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetBuilderCharacterPacket($char_id, $builder_level) {
        $buf = pack("c", 3);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $builder_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangeCharacterNamePacket($char_id,$new_char_name){
        $buf=pack("cV",4,$char_id);
        $buf .= $this -> tounicode($new_char_name);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function KickCharacterPacket($char_id){
        $buf = pack("cV",5,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function AddSkillPacket($char_id, $skill_id, $skill_level) {
        $buf=pack("cVVV",6,$char_id,$skill_id,$skill_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelSkillPacket($char_id, $skill_id) {
        $buf = pack("c", 7);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $skill_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModSkillPacket($char_id, $skill_id, $new_skill_level) {
        $buf=pack("cVVV",8,$char_id,$skill_id,$new_skill_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelItemPacket($char_id, $item_warehouse, $item_uid, $amount) {
        $buf = pack("cVVVV",13,$char_id,$item_warehouse,$item_uid,$amount);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModCharPacket($char_id, $new_SP, $new_EXP, $new_Karma, $new_PK, $new_PKP, $new_PVP) {
        $buf = pack("c", 15);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $new_SP);
        $buf .= pack("V", $new_EXP);
        $buf .= pack("V", $new_Karma);
        $buf .= pack("V", $new_PK);
        $buf .= pack("V", $new_PKP);
        $buf .= pack("V", $new_PVP);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ModChar2Packet($char_id, $gender, $race, $class, $face, $hair_shape, $hair_color) {
        $buf = pack("c", 16);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $gender);
        $buf .= pack("V", $race);
        $buf .= pack("V", $class);
        $buf .= pack("V", $face);
        $buf .= pack("V", $hair_shape);
        $buf .= pack("V", $hair_color);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function PunishCharPacket($char_id,$punish_id,$punish_time) {
        $buf = pack("cVVV",18,$char_id,$punish_id,$punish_time);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetBuilderAccountPacket($account_name, $builder_level) {
        $buf = pack("c", 19);
        $buf .= $this -> tounicode($account_name);
        $buf .= pack("V", $builder_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DisableCharacterPacket($char_id) {
        $buf = pack("c", 20);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", 1);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function EnableCharacterPacket($char_id, $account_id) {
        $buf = pack("c", 21);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $account_id);
        return $this -> CacheDInteractive($buf);
    }

    function ModChar3Packet($char_id, $add_SP, $add_EXP, $add_Karma, $add_PK, $add_PKP, $add_PVP) {
        $buf = pack("c", 29);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $add_SP);
        $buf .= pack("V", $add_EXP);
        $buf .= pack("V", $add_Karma);
        $buf .= pack("V", $add_PK);
        $buf .= pack("V", $add_PKP);
        $buf .= pack("V", $add_PVP);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function MoveCharacterPacket($char_id,$new_account_id,$new_account_name){
        $buf=pack("cVVS",31,$char_id,$new_account_id,$this -> tounicode($new_account_name));
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DeleteCharPacket($char_id) {
        $buf = pack("cV",34,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function PledgeChangeOwnerPacket($pledge_id, $new_leader_id) {
        $buf = pack("cVV",37,$pledge_id,$new_leader_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }


    function PledgeDeletePacket($pledge_id) {
        $buf = pack("c", 38);
        $buf .= pack("V", $pledge_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function BanCharPacket($char_id, $hours ) {
        $buf = pack("c", 39);
        $buf .= pack("V", $char_id);
        $buf .= pack("V", $hours);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SendHomePacket($char_id) {
        $buf = pack("cV",45,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangePledgeLevelPacket($pledge_id, $new_level) {
        $buf = pack("cVV",46,$pledge_id, $new_level);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function CreatePledgePacket($pledge_name, $leader_id) {
        $buf = pack("c",47);
        $buf .= $this -> tounicode($pledge_name);
        $buf .= pack("V",$leader_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetSkillAllPacket($char_id) {
        $buf = pack("cV",48,$char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function RestoreCharToAccount($char_id,$new_char_name,$account_id,$account_name) {
        $buf = pack("c", 49);
        $buf .= pack("V", $char_id);
        $buf .= $this -> tounicode($new_char_name);
        $buf .= pack("V", $account_id);
        $buf .= $this -> tounicode($account_name);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ManIntAnnouncePacket($text,$id,$interval) {
        $buf = pack("c", 51);
        $buf .= pack("V", 1);
        $buf .= pack("V", $interval);
        $buf .= pack("V", $id);
        $buf .= $this -> tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelItem2Packet($item_uid, $amount) {
        $buf = pack("cVV", 54, $item_uid, $amount);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function AddItem2Packet($char_id, $warehouse, $item_type, $item_amount, $enchant = 0, $eroded = 0, $bless = 0, $wished = 0) {
        $buf = pack("cV", 55, $char_id);
        $buf .= pack("V", $warehouse);
        $buf .= pack("V", $item_type);
        $buf .= pack("V", $item_amount);
        $buf .= pack("V", 0); // Резервное поле
        $buf .= pack("V", $enchant);
        $buf .= pack("V", $eroded);
        $buf .= pack("V", $bless);
        $buf .= pack("V", $wished);
        $buf .= pack("V", 1); // Возможно, еще одно служебное поле
        // Добавляем больше данных (8 дополнительных `0`, как указал rimvydas)
        for ($i = 0; $i < 8; $i++) {
            $buf .= pack("i", 0);
        }
        $buf .= $this->tounicode($this->webadmin);

        error_log("Отправляемый пакет (AddItem2Packet): " . bin2hex($buf));

        return $this->CacheDInteractive($buf);
    }


    function SetSociality($char_id,$social) {
        $buf = pack("c", 69);
        $buf .= pack("i", $char_id);
        $buf .= pack("i", $social);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetInstantAnnouncePacket($text) {
        $buf = pack("c", 70);
        $buf .= $this -> tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function ChangeSubJobPacket($char_id, $subjob_id) {
        $buf = pack("c", 89);
        $buf .= pack ("V", $char_id);
        $buf .= pack ("V", $subjob_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function StopCharPacket($char_id, $minutes) {
        $buf = pack("c", 90);
        $buf .= pack ("V", $char_id);
        $buf .= pack ("V", $minutes);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function DelPledgeEmblemPacket($pledge_id) {
        $buf = pack("c", 98);
        $buf .= pack ("V", $pledge_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SendPrivateAnnouncePacket($char_id, $text) {
        $buf = pack("c", 101);
        $buf .= pack ("V", $char_id);
        $buf .= $this -> tounicode($text);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetNobless($char_id) {
        $buf=pack("cVV",106,$char_id,1).$this->tounicode("admin");
        return $this -> CacheDInteractive($buf);
    }

    function SetHero($char_id) {
        $buf=pack("cVV",107,$char_id,2).$this->tounicode("admin");
        return $this -> CacheDInteractive($buf);
    }


    function EternalBanPacket($char_id) {
        $buf = pack("c", 104);
        $buf .= pack ("V", $char_id);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

    function SetNameColor($search_type,$char,$cl_change_type,$color) {
        $buf = pack("c", 111);
        $buf .= pack("c",$search_type);
        $buf .= pack("c", $cl_change_type);
        if($search_type == 1) {
            $buf .= $this -> tounicode($char);
        }
        else {
            $buf .= pack("V", $char);
        }
        $buf .= pack("V", $color);
        $buf .= $this -> tounicode($this -> webadmin);
        return $this -> CacheDInteractive($buf);
    }

}

class CacheD_chatterbox extends CacheD {
    var $fsockerror = 'Unable to connect with CacheD';
    var $socketerrors=array(
        "1" => "OK.",
        "01" => "Error.",
        "02" => "Arguments need.",
        "03" => "Arguments invalid.",
        "04" => "Char not found.",
        "05" => "Warehouse not found.",
        "06" => "Account not found.",
        "07" => "Char in game.",
        "08" => "Too many chars.",
        "09" => "Char in pledge.",
        "010" => "Char pledge owner.",
        "011" => "Cannot ban.",
        "012" => "Name exist.",
        "013" => "Obsolete.",
        "014" => "Invalid char name.",
        "015" => "Char not in game.",
        "016" => "Same char",
        "017" => "Char not in pledge.",
        "018" => "Char pledge master.",
        "019" => "Server not connected.",
        "020" => "Create pet failed.",
        "021" => "Pledge exist.",
        "022" => "No chars.",
        "023" => "Invalid announce id.",
        "024" => "Pledge not found.",
        "025" => "Castle not found.",
        "026" => "Pet not found.");
}

class CacheD_color_chatterbox extends CacheD {
    var $fsockerror = '<b><font color=red>Unable to connect with CacheD</font></b>';
    var $socketerrors=array(
        "1" => "<b><font color=green> Done.</font></b>",
        "01" => "<b><font color=red> Error.</font></b>",
        "02" => "<b><font color=red> Arguments need.</font></b>",
        "03" => "<b><font color=red> Arguments invalid.</font></b>",
        "04" => "<b><font color=red> Char not found.</font></b>",
        "05" => "<b><font color=red> Warehouse not found.</font></b>",
        "06" => "<b><font color=red> Account not found.</font></b>",
        "07" => "<b><font color=red> Char in game.</font></b>",
        "08" => "<b><font color=red> Too many chars.</font></b>",
        "09" => "<b><font color=red> Char in pledge.</font></b>",
        "010" => "<b><font color=red> Char pledge owner.</font></b>",
        "011" => "<b><font color=red> Cannot ban.</font></b>",
        "012" => "<b><font color=red> Name exist.</font></b>",
        "013" => "<b><font color=red> Obsolete.</font></b>",
        "014" => "<b><font color=red> Invalid char name.</font></b>",
        "015" => "<b><font color=red> Char not in game.</font></b>",
        "016" => "<b><font color=red> Same char</font></b>",
        "017" => "<b><font color=red> Char not in pledge.</font></b>",
        "018" => "<b><font color=red> Char pledge master.</font></b>",
        "019" => "<b><font color=red> Server not connected.</font></b>",
        "020" => "<b><font color=red> Create pet failed.</font></b>",
        "021" => "<b><font color=red> Pledge exist.</font></b>",
        "022" => "<b><font color=red> No chars.</font></b>",
        "023" => "<b><font color=red> Invalid announce id.</font></b>",
        "024" => "<b><font color=red> Pledge not found.</font></b>",
        "025" => "<b><font color=red> Castle not found.</font></b>",
        "026" => "<b><font color=red> Pet not found.</font></b>");
}


?>
