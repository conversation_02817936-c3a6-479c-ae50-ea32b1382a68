<?php
namespace App\Services\Payment;

use App\Models\DonateOrder;
use App\Models\ModelFactory;
use App\Models\Referral;
use App\Models\ReferralBonus;
use App\Models\User;
use App\Models\Warehouse;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentProcessingService
{
    public function processPayment($payment_id)
    {
        try {
            DB::transaction(function () use ($payment_id) {
                $payOrder = DonateOrder::where('payment_id', $payment_id)
                    ->where('status', 0)
                    ->first();

                if (!$payOrder) {
                    throw new \Exception('Order not found or already processed');
                }

                if ($payOrder->server_id) {
                    $this->processCharacterPayment($payOrder->user_id, $payOrder->count, $payOrder->server_id);
                } else {
                    $this->processUserPayment($payOrder->user_id, $payOrder->count);
                }

                $payOrder->update(['status' => 1]);
            });
        } catch (\Exception $e) {
            Log::error("Payment processing failed: {$e->getMessage()}");
            throw $e;
        }
    }

    // для авторизованных юзеров
    private function processUserPayment($user_id, $count)
    {
        $user = User::findOrFail($user_id);

        // если бонус процент от цены включен
        if (config('app.bonus_system_percent_enable')) {
            $bonusAmount = $this->calculateBonusPercent($count); // Вычисляем бонус

            // Добавляем монеты с бонусным процентом персонажу
            $user->increment('balance', $bonusAmount);
        }
        else {
            // Добавляем монеты без бонуса, если бонусная система выключена
            $user->increment('balance', $count);
        }

        // Добавляем итемы если бонусная система итемов включена
//        if(config('app.bonus_item_enable')) {
//            // Логируем начисление бонуса
//            $this->bonusByUserId($user_id, $count);
//        }

        if(config('app.referral_system_enabled')) {
            // Проверяем, является ли пользователь рефералом
            $referral = Referral::where('referred_id', $user_id)->first();
            if ($referral) {
                $referrer = $referral->referrer;

                // Вычисляем бонус и округляем до целого числа
                $referralBonus = intval(round($count * config('app.referral_bonus_percent', 10) / 100));

                if ($referralBonus > 0) { // Проверяем, чтобы не начислять 0
                    $referrer->increment('balance', $referralBonus);

                    // Записываем пополнение реферала в историю платежей
                    ReferralBonus::create([
                        'referrer_id' => $referrer->id,
                        'referred_id' => $user->id,
                        'bonus_amount' => $referralBonus,
                        'status' => 'Success'
                    ]);
                }
            }
        }

        Log::info("User payment processed for user_id: {$user_id}");
    }

    // пополнение сразу на перса
    private function processCharacterPayment($char_id, $count, $selected_server)
    {
        // если бонус процент от цены включен
        if (config('app.bonus_system_percent_enable')) {
            $bonusAmount = $this->calculateBonusPercent($count); // Вычисляем бонус

            // Добавляем монеты с бонусным процентом персонажу
            ModelFactory::l2jModel(config('app.l2server_version'))->addItem($char_id, $bonusAmount, $selected_server);

        }
        else {
            // Добавляем монеты без бонуса, если бонусная система выключена
            ModelFactory::l2jModel(config('app.l2server_version'))->addItem($char_id, $count, $selected_server);
        }

        // Добавляем итемы если бонусная система итемов включена
//        if(config('app.bonus_item_enable')) {
//            // Логируем начисление бонуса
//            $this->bonusByCharId($char_id, $count, $selected_server);
//        }

        Log::info("Character payment processed for char_id: {$char_id}");
    }

    /**
     * Рассчитывает бонус на основе переданного количества монет.
     */
    private function calculateBonusPercent($count)
    {
        $bonusConfig = config('app.bonus_system_percent');

        // Проверяем, превышает ли количество монет минимальный порог
        if ($count < $bonusConfig['min_coins']) {
            return $count; // Если меньше, возвращаем оригинальное количество
        }

        // Получаем уровни бонусов
        $bonusLevels = $bonusConfig['bonus_levels'];

        // Сортируем уровни бонусов по возрастанию
        ksort($bonusLevels);

        // Определяем подходящий процент
        $bonusPercent = 0;
        foreach ($bonusLevels as $threshold => $bonus) {
            if ($count >= $threshold) {
                $bonusPercent = $bonus['bonus_percent'];
            } else {
                break;
            }
        }

        // Вычисляем итоговое количество с учетом бонуса
        $bonusAmount = floor($count + ($count * ($bonusPercent / 100))); // Округление вниз до целого числа

        return $bonusAmount;
    }

    private function bonusByUserId($user_id, $count) {
        try {
            $bonus_sys = $this->bonusSystem($count);
            $warehouse = new Warehouse;

            foreach ($bonus_sys as $item) {
                // Проверяем, существует ли уже запись для данного пользователя и элемента
                $existingItem = Warehouse::where('user_id', $user_id)
                    ->where('item_id', $item['item_id'])
                    ->first();

                if ($existingItem) {
                    // Если запись уже существует, увеличиваем счетчик
                    $existingItem->count += $item['item_count'];
                    $existingItem->save();
                } else {
                    // Если запись не существует, создаем новую
                    $warehouse->addItem($user_id, $item['item_id'], $item['item_count'], 0);
                }
            }
        }
        catch (Exception $e) {
            Log::warning('Ошибка начисления итемов ' . $e->getMessage());
        }
    }

    private function bonusByCharId($char_id, $count, $selected_server) {
        try {
            $bonus_sys = $this->bonusSystem($count);
            foreach ($bonus_sys as $item) {
                ModelFactory::l2jModel(config('app.l2server_version'))->addItemBonus($char_id, $item['item_id'], $item['item_count'], 0, $selected_server);
            }
        }
        catch (Exception $e) {
            Log::warning('Ошибка начисления итемов на перса ' . $e->getMessage());
        }
    }

    public function bonusT() {
        $count = 300;
        $bonusPercentage = $this->bonusPizza($count);
        $bonusPercentage = $bonusPercentage / 100;
        $bonusAmount = $count + ($count * $bonusPercentage); // Добавляем 8% к 100

        // Округляем вверх до ближайшего целого числа
        $bonusAmount = (int) round($bonusAmount);

        dd($bonusAmount);

    }

    public function bonusPizza($count) {
        $config = config('app.bonus_system.items');

        $bonus = null;

        // Проверка условий по бонусам
        foreach ($config as $price => $items) {
            if ($count >= $price) {
                $bonus = $items['bonus_percent'];
            } else {
                break; // Прерываем цикл, как только находим первую цену, превышающую $count
            }
        }

        // Если бонус не найден, вернем бонус с наименьшей ценой
        if ($bonus === null && count($config) > 0) {
            $bonus = reset($config); // Получаем первый элемент массива
        }

        return $bonus;
    }

    public function bonusSystem($count) {
        $config = config('app.bonus_system.items');

        $bonus = null;

        // Проверка условий по бонусам
        foreach ($config as $price => $items) {
            if ($count >= $price) {
                $bonus = $items['item'];
            } else {
                break; // Прерываем цикл, как только находим первую цену, превышающую $count
            }
        }

        // Если бонус не найден, вернем бонус с наименьшей ценой
        if ($bonus === null && count($config) > 0) {
            $bonus = reset($config); // Получаем первый элемент массива
        }

        return $bonus;
    }
}
