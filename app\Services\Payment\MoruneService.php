<?php

namespace App\Services\Payment;

use App\Services\Payment\PaymentProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class MoruneService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;
    }

    public function createPayment($payment_id, $count)
    {
        $currency = config('app.pay_system.freekassa_currency'); // Валюта
        $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
        // Расчет суммы платежа
        $sum = (int)ceil($count * $coin_price);

        $url = 'https://api.morune.com/invoice/create';

        $customFieldsData = ['payment_id' => $payment_id];

        $data = [
            'currency' => $currency,
            'amount' => $sum,
            'order_id' => $payment_id,
            'shop_id' => config('app.pay_system.morune_shop_id'),
            'custom_fields' => json_encode($customFieldsData),
            'success_url' => config('app.pay_system.morune_success_url'),
            'fail_url' => config('app.pay_system.morune_fail_url'),
            'hook_url' => route('callback-morune'),
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => config('app.pay_system.morune_secret_key'),
        ])->post($url, $data);

        if ($response->successful()) {
            $parsedResult = $response->json();
            return $parsedResult['data']['url'] ?? null;
        }

        Log::error('Morune: Error creating payment', ['response' => $response->body()]);
        throw new Exception('Failed to create Morune payment.');
    }

    public function handleWebhook(Request $request)
    {
        try {
            // Проверка IP-адреса откуда идет вебхук
//            if (!$this->isAllowedIp($request->ip())) {
//                Log::warning('FreeKassa: Access denied for IP', ['ip' => $request->ip()]);
//                return response('Access Denied', 403);
//            }

            // Валидация входящих данных
            $request->validate([
                'amount' => 'required',
                'order_id' => 'required',
                'custom_fields' => 'required|array',
                'status' => 'required',
            ]);

            $rawData = $request->all();
            $custom_fields = $rawData['custom_fields'];
            $status = $rawData['status'];

            if ($status !== 'success') {
                Log::warning('Morune: Payment failed or incomplete', $rawData);
                return response('Payment status not successful', 400);
            }

            if (!isset($custom_fields['payment_id'])) {
                Log::warning("Morune: Missing required field: payment_id");
                return response('Missing required fields', 400);
            }

            $payment_id = $custom_fields['payment_id'];

            // Обработка успешного платежа
            $this->paymentProcessingService->processPayment($payment_id);

            return response('OK', 200);
        } catch (Exception $e) {
            Log::error('Morune: Error handling webhook', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }

    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '***************',
        ];

        return in_array($ip, $allowedIps);
    }
}
