<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;
use PDO;
use PDOException;

class VaganthModel extends Model
{
    use HasFactory;

    public function getAccounts($ma_id, $include_chars) {
        $data = [
            'ma_id' => $ma_id
        ];

        $return_data = [];

        $result = DB::connection('loginserver')
            ->table('accounts')
            ->select("login")
            ->where('ma_id', $data)
            ->get();

        if ($include_chars) {
            foreach ($result as $row) {
                $return_data[$row->login] = $this->getCharacters($row->login);
            }
        } else {
            foreach ($result as $row) {
                $return_data[] = $row->login;
            }
        }

        return $return_data;
    }

    public function getGameLoginByUserId($user_id) {
        return DB::connection('lin2db')
            ->table('user_account')
            ->where('ma_id', $user_id)
            ->select('account as login')
            ->first();
    }


    public function getCharacters($account) {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'PL.alliance_id') // Crest для альянса
            ->leftJoin('Pledge_Crest as clanCrest', 'clanCrest.crest_id', '=', 'PL.pledge_id') // Crest для клана
            ->where('ud.account_name', '=', $account)
            ->select(
                'allyCrest.bitmap as allyCrest', // Crest альянса
                'clanCrest.bitmap as clanCrest', // Crest клана
                'ud.char_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_id as obj_Id',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills',
                'ud.PK as pkkills'
            )
            ->orderBy('ud.create_date', 'DESC') // Указан правильный столбец для сортировки
            ->limit(config('app.statistics.top_pvp_limit')) // Указываем TOP
            ->get();
    }

    public function getGameAccounts($ma_id)
    {
        return DB::connection('lin2db')
            ->table('user_account')
            ->where('ma_id', '=', $ma_id)
            ->select('account as login', 'ma_id')
            ->get();
    }

    public function getCountAccounts() {
        return DB::connection('lin2db')
            ->table('user_account')
            ->count();
    }

    public function getCountCharacters() {
        return DB::connection(gameservers())
            ->table('user_data')
            ->count();
    }

    public function getCountNobles() {
        return DB::connection(gameservers())
            ->table('olympiad_nobles')
            ->count();
    }

    public function getCountHeroes() {
        $result = DB::connection(gameservers())
            ->selectOne('SELECT COUNT(*) AS hero_count FROM olympiad_heroes');

        return $result->hero_count;
    }

    public function getCountGameMasters() {
        return DB::connection(gameservers())
            ->table('characters')
            ->where('accesslevel', '>', 0)
            ->count();
    }

    public function getCountClans() {
        return DB::connection(gameservers())
            ->table('Pledge')
            ->count();
    }

    public function getCountAllyance() {
        return DB::connection(gameservers())
            ->table('Alliance')
            ->count();
    }

    public function getSexMan() {
        return DB::connection(gameservers())
            ->table('user_data')
            ->where('gender', '=', 1)
            ->count();
    }

    public function getSexWoman() {
        return DB::connection(gameservers())
            ->table('user_data')
            ->where('gender', '=', 0)
            ->count();
    }

    public function getTopPvp()
    {
        try {
            return DB::connection(gameservers())
                ->table('characters')
                ->select(DB::raw('characters.char_name, characters.sex, characters.pvpkills, characters.pkkills'))
                ->where('characters.pvpkills', '>', 0)
                ->orderBy('characters.pvpkills', 'DESC')
                ->groupBy('characters.pvpkills')
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            Log::error('Ошибка подключения к базе данных в getTopPvp: ' . $e->getMessage());
            abort(403, 'Ошибка подключения к базе данных');
        }
    }

    public function getTopPlayers()
    {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as Crest', 'Crest.crest_id', '=', 'ud.pledge_id')
            ->select(
                'Crest.bitmap as allyCrest',
                'Crest.bitmap as clanCrest',
                'PL.alliance_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills'
            )
            ->orderBy('ud.create_date', 'DESC') // Указан правильный столбец для сортировки
            ->limit(config('app.statistics.top_pvp_limit')) // Указываем TOP
            ->get();
    }


    public function getTopRich() {
        return DB::connection(gameservers())
            ->table('items')
            ->leftJoin('characters', 'characters.obj_Id', '=', 'items.owner_id')
            ->select(DB::raw('characters.char_name as name, items.item_id, items.enchant_level'))
            ->whereIn('item_id', explode(',', config('app.top_item_list')))
            ->orderByDesc('items.enchant_level')
            ->paginate(10);
    }

    public function getPvpPlayers()
    {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as Crest', 'Crest.crest_id', '=', 'ud.pledge_id')
            ->select(
                'Crest.bitmap as allyCrest',
                'Crest.bitmap as clanCrest',
                'PL.alliance_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills',
                'ud.PK as pkkills'
            )
            ->where('ud.Duel', '>', 0)
            ->orderBy('ud.Duel', 'DESC')
            ->limit(config('app.statistics.top_pvp_limit'))
            ->get();
    }

    public function getPkPlayers()
    {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as Crest', 'Crest.crest_id', '=', 'ud.pledge_id')
            ->select(
                'Crest.bitmap as allyCrest',
                'Crest.bitmap as clanCrest',
                'PL.alliance_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills',
                'ud.PK as pkkills'
            )
            ->where('ud.PK', '>', 0)
            ->orderBy('ud.PK', 'DESC')
            ->limit(config('app.statistics.top_pk_limit'))
            ->get();
    }

    public function getPvpPlayersMainPage() {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as Crest', 'Crest.crest_id', '=', 'ud.pledge_id')
            ->select(
                'Crest.bitmap as allyCrest',
                'Crest.bitmap as clanCrest',
                'PL.alliance_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills',
                'ud.PK as pkkills'
            )
            ->where('ud.Duel', '>', 0)
            ->orderBy('ud.Duel', 'DESC')
            ->limit(config('app.statistics.top_pvp_limit'))
            ->get();
    }

    public function getPkPlayersMainPage() {
        return DB::connection(gameservers())
            ->table('user_data as ud')
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'ud.pledge_id')
            ->leftJoin('Pledge_Crest as Crest', 'Crest.crest_id', '=', 'ud.pledge_id')
            ->select(
                'Crest.bitmap as allyCrest',
                'Crest.bitmap as clanCrest',
                'PL.alliance_id as ally_id',
                'ud.char_id as clan_id',
                'ud.char_id as clan_id',
                'PL.name as clan_name',
                'ud.char_name',
                'ud.Lev as level',
                'ud.class as class_id',
                'ud.gender as sex',
                'ud.create_date as onlinetime',
                'ud.Duel as pvpkills',
                'ud.PK as pkkills'
            )
            ->where('ud.PK', '>', 0)
            ->orderBy('ud.PK', 'DESC')
            ->limit(config('app.statistics.top_pk_limit'))
            ->get();
    }

    public function getTopClansMainPage() {
        $clans = DB::connection(gameservers())
            ->table('Pledge as PL')
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'PL.alliance_id') // Crest для альянса
            ->leftJoin('Pledge_Crest as clanCrest', 'clanCrest.crest_id', '=', 'PL.pledge_id') // Crest для клана
            ->leftJoin('user_data as ud', 'ud.pledge_id', '=', 'PL.pledge_id')
            ->leftJoin('pledge_ext as p_ext', 'p_ext.pledge_id', '=', 'PL.pledge_id')
            ->leftJoin('alliance as ally', 'ally.id', '=', 'PL.alliance_id')
            ->select(
                'allyCrest.bitmap as allyCrest', // Crest альянса
                'clanCrest.bitmap as clanCrest', // Crest клана
                'PL.pledge_id as clan_id',
                'PL.alliance_id as ally_id',
                'PL.name as clan_name',
                'PL.agit_id as clanhall_name',
                'PL.skill_level as clan_level',
                'ud.char_name as clan_leader',
                'p_ext.reputation_points as reputation_score',
                'ally.name as ally_name',
                'PL.castle_id',
                DB::raw('(SELECT COUNT(0) FROM user_data WHERE pledge_id = PL.pledge_id) as clan_players')
            )
            ->orderByDesc('p_ext.reputation_points')
            ->limit(config('app.statistics.clan_limit'))
            ->get();

        // Добавляем название замка к результатам через хелпер
        foreach ($clans as $clan) {
            $clan->castle_name = getCastleNameById($clan->castle_id); // Вызываем хелпер для каждого clan
        }

        return $clans;
    }

    public function getCastleMainPage() {
        $castle = DB::connection(gameservers())
            ->table('castle as c3')
            ->select(
                'allyCrest.bitmap as allyCrest', // Crest альянса
                'clanCrest.bitmap as clanCrest', // Crest клана
                'PL.pledge_id as clan_id',
                'PL.alliance_id as ally_id',
                'ud.char_name as leader',
                'PL.name as clan_name',
                'c3.id as castle_id',
                'c3.name as castle_name',
                'c3.tax_rate',
                'c3.tax_rate as treasury',
                'c3.tax_rate as siege_date',
                //'c3.tax_rate as last_siege_date',
                'c3.tax_rate as ownDate',
                'c3.next_war_time',
                'c3.tax_rate as tax_percent'
            )
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'c3.pledge_id') // Добавлен LEFT JOIN для PL
            ->leftJoin('Alliance as ad', 'ad.id', '=', 'PL.alliance_id') // Используем PL для alliance_id
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'PL.alliance_id') // Crest для альянса
            ->leftJoin('Pledge_Crest as clanCrest', 'clanCrest.crest_id', '=', 'PL.pledge_id') // Crest для клана
            ->leftJoin('user_data as ud', 'ud.char_id', '=', 'PL.ruler_id')
            ->get();

        foreach ($castle as $cl) {
            $cl->castle_name = getCastleNameById($cl->castle_id); // Вызываем хелпер для каждого castle

            if (!empty($cl->next_war_time)) {
                try {
                    // Преобразуем UNIX-время в Carbon
                    $war_time = Carbon::createFromTimestamp($cl->next_war_time);

                    // Форматируем дату в нужный формат
                    $cl->next_war_date = $war_time->format('d/m/Y H:i');
                } catch (\Exception $e) {
                    $cl->next_war_date = 'Ошибка преобразования даты';
                }
            } else {
                $cl->next_war_date = 'Нет данных';
            }
        }



        return $castle;
    }

    public function getTopClans()
    {
        $clans = DB::connection(gameservers())
            ->table('Pledge as PL')
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'PL.alliance_id') // Crest для альянса
            ->leftJoin('Pledge_Crest as clanCrest', 'clanCrest.crest_id', '=', 'PL.pledge_id') // Crest для клана
            ->leftJoin('user_data as ud', 'ud.pledge_id', '=', 'PL.pledge_id')
            ->leftJoin('pledge_ext as p_ext', 'p_ext.pledge_id', '=', 'PL.pledge_id')
            ->leftJoin('agit', 'agit.id', '=', 'PL.agit_id')
            ->leftJoin('alliance as ally', 'ally.id', '=', 'PL.alliance_id')
            ->select(
                'allyCrest.bitmap as allyCrest', // Crest альянса
                'clanCrest.bitmap as clanCrest', // Crest клана
                'PL.pledge_id as clan_id',
                'PL.alliance_id as ally_id',
                'PL.name as clan_name',
                'agit.name as clanhall_name',
                'PL.skill_level as clan_level',
                'ud.char_name as clan_leader',
                'p_ext.reputation_points as reputation_score',
                'ally.name as ally_name',
                'PL.castle_id',
                DB::raw('(SELECT COUNT(0) FROM user_data WHERE pledge_id = PL.pledge_id) as clan_players')
            )
            ->orderByDesc('p_ext.reputation_points')
            ->limit(config('app.statistics.clan_limit'))
            ->get();

        // Добавляем название замка к результатам через хелпер
        foreach ($clans as $clan) {
            $clan->castle_name = getCastleNameById($clan->castle_id); // Вызываем хелпер для каждого clan
        }

        return $clans;
    }

    public function getTopAlly()
    {
        return DB::connection(gameservers())
            ->table('Alliance as ally')
            ->leftJoin('Pledge as PL', 'PL.alliance_id', '=', 'ally.id')
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'ally.crest_id') // Crest для альянса
            ->leftJoin('user_data as char', 'char.char_id', '=', 'PL.ruler_id') // Для имени персонажа
            ->select(
                'allyCrest.bitmap as allyCrest',
                'ally.id as ally_id',
                'ally.name as ally_name',
                'char.char_name as char_name', // Имя персонажа
                DB::raw('COUNT(char.pledge_id) as ally_players') // Общее количество персонажей
            )
            ->groupBy(
                'allyCrest.bitmap',
                'ally.id',
                'ally.name',
                'char.char_name' // Добавлено в GROUP BY
            )
            ->limit(config('app.statistics.alliance_limit'))
            ->get();
    }


    public function getCastle() {
        $castle = DB::connection(gameservers())
            ->table('castle as c3')
            ->select(
                'allyCrest.bitmap as allyCrest', // Crest альянса
                'clanCrest.bitmap as clanCrest', // Crest клана
                'PL.pledge_id as clan_id',
                'PL.alliance_id as ally_id',
                'ud.char_name as leader',
                'PL.name as clan_name',
                'c3.id as castle_id',
                'c3.name as castle_name',
                'c3.tax_rate',
                'c3.tax_rate as treasury',
                'c3.tax_rate as siege_date',
                //'c3.tax_rate as last_siege_date',
                'c3.tax_rate as ownDate',
                'c3.next_war_time',
                'c3.tax_rate as tax_percent'
            )
            ->leftJoin('Pledge as PL', 'PL.pledge_id', '=', 'c3.pledge_id') // Добавлен LEFT JOIN для PL
            ->leftJoin('Alliance as ad', 'ad.id', '=', 'PL.alliance_id') // Используем PL для alliance_id
            ->leftJoin('Pledge_Crest as allyCrest', 'allyCrest.crest_id', '=', 'PL.alliance_id') // Crest для альянса
            ->leftJoin('Pledge_Crest as clanCrest', 'clanCrest.crest_id', '=', 'PL.pledge_id') // Crest для клана
            ->leftJoin('user_data as ud', 'ud.char_id', '=', 'PL.ruler_id')
            ->get();

        foreach ($castle as $cl) {
            $cl->castle_name = getCastleNameById($cl->castle_id); // Вызываем хелпер для каждого castle
        }

        return $castle;
    }



    public function getCastle123() {
        return DB::connection(gameservers())
            ->table('castle as c3')
            ->select(
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'cd.clan_id',
                'ad.ally_id',
                'c.char_name as leader',
                'cs.name as clan_name',
                'c3.id as castle_id',
                'c3.name as castle_name',
                'c3.tax_percent',
                'c3.treasury',
                'c3.last_siege_date',
                'c3.own_date as ownDate',
                'c3.siege_date',
                'c3.treasury as reward_count'
            )
            ->leftJoin('clan_data as cd', 'cd.hasCastle', '=', 'c3.id')
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('clan_subpledges as cs', 'cs.clan_id', '=', 'cd.clan_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->where(function ($query) {
                $query->where('cs.type', '=', 0)
                    ->orWhereNull('cs.type');
            })
            ->orderByDesc('cd.reputation_score')
            ->get();
    }

    public function getClanSkills($clan_id) {
        return DB::connection(gameservers())
            ->table('pledge_skill')
            ->where('pledge_id', $clan_id)
            ->select(
                'pledge_id',
                'skill_id',
                'skill_level'
            )
            ->get();
    }

    public function getClanHallById($id) {
        return DB::connection(gameservers())
            ->table('clanhall as ch')
            ->leftJoin('clan_data as cd', 'cd.hasHideout', '=', 'ch.id')
            ->leftJoin('clan_subpledges as cs', function ($join) {
                $join->on('cs.clan_id', '=', 'cd.clan_id')
                    ->where('cs.type', '=', 0);
            })
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->select(
                'ad.ally_id',
                'cd.clan_id',
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'ch.name as clanhall',
                'ch.id',
                'c.char_name as leader',
                'cs.name as clan_name',
                'ch.siege_date'
            )
            ->where('id', $id)
            ->orderBy('ch.own_date', 'DESC')
            ->get();
    }


    public function getClanHall() {
        return DB::connection(gameservers())
            ->table('clanhall as ch')
            ->leftJoin('clan_data as cd', 'cd.hasHideout', '=', 'ch.id')
            ->leftJoin('clan_subpledges as cs', function ($join) {
                $join->on('cs.clan_id', '=', 'cd.clan_id')
                    ->where('cs.type', '=', 0);
            })
            ->leftJoin('ally_data as ad', 'ad.ally_id', '=', 'cd.ally_id')
            ->leftJoin('characters as c', 'c.obj_Id', '=', 'cs.leader_id')
            ->select(
                'ad.ally_id',
                'cd.clan_id',
                'ad.crest as allyCrest',
                'cd.crest as clanCrest',
                'ch.name as clanhall',
                'c.char_name as leader',
                'cs.name as clan_name',
                'ch.siege_date'
            )
            ->orderBy('ch.own_date', 'DESC')
            ->get();
    }

    public function getEpicBosses() {
        return DB::connection(gameservers())
            ->table('epic_boss_spawn')
            ->select(DB::raw('bossId, respawnDate, state'))
            ->whereIn('bossId', [29020, 29068, 29028, 29045])
            ->get();
    }

    public function getBelethStatus() {
        return DB::connection(gameservers())
            ->table('server_variables')
            ->select('name', 'value')
            ->where('name', 'BelethKillTime')
            ->first();
    }







//    public function getTopItems() {
//        $seconds = 3600;
//        Cache::remember('gameserver', $seconds, function () {
//            return DB::connection(gameservers())
//                ->select('SELECT c.char_name as name,i.item_type, i.enchant
//FROM items i
//LEFT JOIN `characters` c ON c.obj_Id = i.owner_id
//WHERE i.item_type IN (6656,6657,6658,6659,6660,6661,6662) LIMIT 20');
//        });
//    }


    public function getTopSkills() {
        return DB::connection(gameservers())
            ->table('character_skills_save')
            ->leftJoin('characters', 'characters.obj_Id', '=', 'character_skills_save.char_obj_id')
            ->select(DB::raw('characters.char_name as name, character_skills_save.skill_id, character_skills_save.skill_level'))
            ->whereIn('character_skills_save.skill_id', explode(',', config('app.top_skill_list')))
            ->paginate(10);
    }

    public function getTopItems() {
        return DB::connection(gameservers())
            ->table('items')
            ->leftJoin('characters', 'characters.obj_Id', '=', 'items.owner_id')
            ->select(DB::raw('characters.char_name as name, items.item_id, items.enchant_level'))
            ->whereIn('item_id', explode(',', config('app.top_item_list')))
            ->orderByDesc('items.enchant_level')
            ->paginate(10);
    }

    public function getBestPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('characters.onlinetime', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getHpPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id,
            ally_data.ally_id, characters.char_name, character_subclasses.curHp, character_subclasses.class_id,
            characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('character_subclasses.curHp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getMpPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest,
            clan_data.clan_id, ally_data.ally_id, characters.char_name, MAX(character_subclasses.curMp) as mp,
            character_subclasses.class_id, characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->groupBy('character_subclasses.curMp')
            ->orderBy('character_subclasses.curMp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getRichPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->leftJoin('items', 'characters.obj_Id', '=', 'items.owner_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest,
            clan_data.clan_id, ally_data.ally_id, characters.char_name, items.count,
            character_subclasses.class_id, characters.sex, clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('items.count', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getHeroPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('oly_heroes', 'characters.obj_Id', 'oly_heroes.char_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id,
            ally_data.ally_id, characters.char_name, character_subclasses.class_id, characters.sex,
            clan_subpledges.name as clan_name'))
            ->where('character_subclasses.isBase', '=', 1)
            ->where('oly_heroes.active', '=', 1)
            ->limit(20)
            ->get();
    }

    public function getBaronPlayers() {
        return DB::connection(gameservers())
            ->table('characters')
            ->leftJoin('character_subclasses', 'characters.obj_Id', '=', 'character_subclasses.char_obj_id')
            ->leftJoin('clan_subpledges', 'characters.clanid', '=', 'clan_subpledges.clan_id')
            ->leftJoin('clan_data', 'clan_data.clan_id', '=', 'characters.clanid')
            ->leftJoin('ally_data', 'ally_data.ally_id', '=', 'clan_data.ally_id')
            ->select(DB::raw('ally_data.crest as allyCrest, clan_data.crest as clanCrest, clan_data.clan_id, ally_data.ally_id, characters.char_name, characters.curHp, character_subclasses.class_id, characters.sex, clan_subpledges.name, characters.onlinetime, characters.pvpkills, characters.pkkills'))
            ->where('character_subclasses.isBase', '=', 1)
            ->orderBy('characters.curHp', 'DESC')
            ->limit(20)
            ->get();
    }

    public function getRaidBosses() {
        return DB::connection(gameservers())
            ->table('raidboss_status')
            ->select(DB::raw('id, respawn_delay'))
            ->whereIn('id', [29001, 29022, 29006, 29014])
            ->get();
    }

    public function getPlayersList($ma_id) {
        $accountNames = DB::connection('lin2db')
        ->table('user_account')
            ->where('ma_id', '=', $ma_id)
            ->pluck('account');

        // Если нет аккаунтов, возвращаем пустую коллекцию
        if ($accountNames->isEmpty()) {
            return collect();
        }

        // Теперь делаем запрос к 'gameserver' для получения персонажей, связанных с этими аккаунтами
        return DB::connection(gameservers())
            ->table('user_data')
            ->select('char_id as char_id', 'char_name')
            ->whereIn('account_name', $accountNames)
            ->get();
    }



    public function sendCoins($owner_id, $item_id, $count) {
        try {
            // Использование соединения с игровым сервером
            return DB::connection(gameservers())
                ->table('ItemDelivery')
                ->insert([
                    'char_id'     => $owner_id,
                    'item_id'     => $item_id,
                    'item_amount'       => $count,
                    'enchant'       => 0,
                ]);
        } catch (\Exception $e) {
            // Здесь можно логировать ошибку или обрабатывать исключение
            Log::error("Error in sendCoins: " . $e->getMessage());
            // Выбрасывание исключения для обработки на более высоком уровне
            throw $e;
        }
    }


    public function getPlayerInventory($char_id) {
        return DB::connection(gameservers())
            ->table('characters as c')
            ->leftJoin('items as i', 'i.owner_id', '=', 'c.obj_Id')
            ->select(DB::raw('item_type, amount, enchant'))
            ->where('i.owner_id', '=', $char_id)
            ->get();
    }

    public function checkPlayer($char_name, $selected_server)
    {
        $player = DB::connection($selected_server)
            ->table('user_data')
            ->where('char_name', $char_name)
            ->first();

        if (!$player) {
            throw new \Exception('Player not found');
        }

        return $player;
    }

    public function addItem($char_id, $count, $selected_server) {
        $add_item = DB::connection($selected_server)
            ->table('ItemDelivery')
            ->insert([
                'char_id'     => $char_id,
                'item_id'     => config('app.pay_system.item_id'),
                'item_amount'       => $count,
            ]);

        if(!$add_item) {
            throw new Exception('Не удалось добавить итем в таблицу items_delayed');
        }

        return $add_item;
    }

    public function addItemBonus($char_id, $item_id, $count, $enchant_level, $selected_server) {
        $add_item = DB::connection($selected_server)
            ->table('ItemDelivery')
            ->insert([
                'char_id'     => $char_id,
                'item_id'     => $item_id,
                'item_amount'       => $count,
                'enchant_level'       => $enchant_level,
                'description' => 'Bonus Item',
            ]);

        if(!$add_item) {
            throw new Exception('Не удалось добавить bonus item в таблицу items_delayed');
        }

        return $add_item;
    }


    public function getCharIdByCharName($char_name, $selected_server)
    {
        // Проверка на корректность входных данных
        if (empty($char_name) || !is_string($char_name)) {
            throw new \InvalidArgumentException('Invalid character name.');
        }

        // Проверка наличия конфигурации соединения
        if (!array_key_exists($selected_server, config('database.connections'))) {
            throw new \Exception('Database connection for selected server not found.');
        }

        // Выполнение запроса
        $char_id = DB::connection($selected_server)
            ->table('user_data')
            ->where('char_name', $char_name)
            ->select('char_id as obj_Id')
            ->first();

        // Проверка результата
        if (!$char_id) {
            throw new \Exception('Character ID not found for the given name.');
        }

        return $char_id;
    }

    public function getCharIdByName($char_name) {
        $char = DB::connection(gameservers())
            ->table('user_data')
            ->where('char_name', $char_name)
            ->select('char_id')
            ->first();

        if(!$char) {
            throw new \Exception('Char Id not found');
        }

        return $char->char_id; // Возвращаем строку, а не объект
    }

    public function getCharNameByCharId($char_id) {
        $char = DB::connection(gameservers())
            ->table('user_data')
            ->where('char_id', $char_id)
            ->select('char_name')
            ->first();

        if(!$char) {
            throw new \Exception('Char Name not found');
        }

        return $char->char_name; // Возвращаем строку, а не объект
    }

    public function getModChar2Packet($char_id) {
        $char = DB::connection(gameservers())
            ->table('user_data')
            ->where('char_id', $char_id)
            ->select('gender', 'race', 'class')
            ->first();

        if(!$char) {
            throw new \Exception('Char not found');
        }

        return $char;
    }

    public function getGameLoginByCharName($char_name) {
        $char = DB::connection(gameservers())
            ->table('user_data')
            ->where('char_name', $char_name)
            ->select('account_name as login')
            ->first();

        if(!$char) {
            throw new \Exception('Game login not found');
        }

        return $char->login; // Возвращаем строку, а не объект
    }

    public function getCharNameByLogin($login)
    {
        $chars = DB::connection(gameservers())
            ->table('user_data')
            ->where('account_name', $login)
            ->select('char_name')
            ->get();

        // Если коллекция пуста, возвращаем пустой массив
        if ($chars->isEmpty()) {
            return [];
        }

        return $chars->pluck('char_name')->toArray(); // Возвращаем массив строк
    }

    public function getCharacterNameByLogin($login) {
        $char = DB::connection(gameservers())
            ->table('user_data')
            ->where('account_name', $login)
            ->select('char_name')
            ->first();

        if(!$char) {
            throw new \Exception('Char Name not found');
        }

        return $char;
    }



    // проверка что ник существует
    public function checkCharNameExist(string $char_name): bool
    {
        $charExists = DB::connection(gameservers())
            ->table('user_data')
            ->where('char_name', $char_name)
            ->exists(); // Используем exists для проверки наличия записи

        if ($charExists) {
            throw new \Exception('Такой ник уже существует');
        }

        return !$charExists; // Возвращаем true, если ник не существует
    }


    public function deleteHwid($char_id) {
        // Находим запись в character_variables по ID и имени hwidlock@
        $char = DB::connection(gameservers())
            ->table('character_variables')
            ->where('obj_Id', $char_id)
            ->where('name', 'hwidlock@')
            ->first();

        if (!$char) {
            return false; // HWID уже отвязан, ничего не удаляем
        }

        // Удаляем запись из character_variables
        DB::connection(gameservers())
            ->table('character_variables')
            ->where('obj_Id', $char_id)
            ->where('name', 'hwidlock@')
            ->delete();

        return true; // Удаление прошло успешно
    }

    public function getDeleteAntiscam($char_id) {
        // Находим запись в character_variables по ID и имени hwidlock@
        $char = DB::connection(gameservers())
            ->table('antiscam_keys')
            ->where('obj_Id', $char_id)
            ->first();

        if (!$char) {
            return false; // HWID уже отвязан, ничего не удаляем
        }

        // Удаляем запись из character_variables
        DB::connection(gameservers())
            ->table('antiscam_keys')
            ->where('obj_Id', $char_id)
            ->delete();

        return true; // Удаление прошло успешно
    }



    public function getLoginByEmail($email) {
        $email = DB::table('users')
            ->where('email', $email)
            ->first();

        if(!$email) {
            throw new \Exception('Email not found');
        }

        return $email->name;
    }

    public function countRaces()
    {
        // Подключение к базе данных gameservers
        $subclasses = DB::connection(gameservers())
            ->table('user_data')
            ->get();

        // Массив для хранения количества персонажей по расам
        $raceCounts = array_fill_keys(['Human', 'Elf', 'Dark elf', 'Orc', 'Dwarf', 'Kamael'], 0);

        // Проход по всем записям из базы данных
        foreach ($subclasses as $subclass) {
            // Получаем данные класса через хелпер class_list
            $classList = class_list();
            $classData = $classList[$subclass->class]; // Получаем данные класса по class_id

            // Получаем имя расы
            $raceName = race_name($classData['race']);

            // Увеличиваем счетчик для этой расы
            if (isset($raceCounts[$raceName])) {
                $raceCounts[$raceName]++;
            }
        }

        // Считаем общее количество персонажей
        $totalCharacters = array_sum($raceCounts);

        // Итоговый массив с процентным соотношением и проверкой на 0
        $finalRaceCounts = [];

        foreach ($raceCounts as $race => $count) {
            if ($count > 0) {
                $percentage = $totalCharacters > 0 ? round(($count / $totalCharacters) * 100, 2) : 0;
                $finalRaceCounts[$race] = [
                    'count' => $count,
                    'percentage' => ceil($percentage),
                ];
            }
        }

        return $finalRaceCounts;
    }

    public function showGamePlayersOnline()
    {
        $players_online = DB::connection(gameservers())
            ->table('characters')
            ->where('online', '=', 1)
            ->select('char_name');

        return $players_online;
    }

    public function checkAccountMaIdExist($login, $ma_id): bool
    {
        return DB::connection('lin2db')
            ->table('user_account')
            ->where('account', $login)
            ->where('ma_id', $ma_id)
            ->exists();
    }

    public function checkAccountExist($login): bool
    {
        return DB::connection('lin2db')
            ->table('user_account')
            ->where('account', $login)
            ->exists();
    }

    public function checkCharacterExist($login)
    {
        return DB::connection(gameservers())
            ->table('user_data')
            ->where('account_name', $login)
            ->select('char_name')
            ->get();
    }

    public function createGameAccount($login, $password, $ma_id)
    {
        // Вставка в user_account
        DB::connection('lin2db')
            ->table('user_account')
            ->insert([
                'account' => $login,
                'pay_stat' => 1,
                'login_flag' => 0,
                'ma_id' => $ma_id,
            ]);

        $fakeQuiz1 = 'quiz1'; // Фейковый вопрос 1
        $fakeQuiz2 = 'quiz2'; // Фейковый вопрос 2
        $fakeAnswer1 = '0x' . bin2hex(random_bytes(16)); // Фейковый ответ 1
        $fakeAnswer2 = '0x' . bin2hex(random_bytes(16)); // Генерируем фейковые данные

        // Вставка в user_auth
        DB::connection('lin2db')
            ->table('user_auth')
            ->insert([
                'account' => $login,
                'password' => DB::raw($password),
                'quiz1' => $fakeQuiz1, // Фейковый вопрос 1
                'quiz2' => $fakeQuiz2, // Фейковый вопрос 2
                'answer1' => DB::raw("CONVERT(binary(32), '{$fakeAnswer1}')"),
                'answer2' => DB::raw("CONVERT(binary(32), '{$fakeAnswer2}')"),
            ]);
    }

    public function changeGamePassword($login, $password): void
    {
        // Обновление пароля в базе данных
        DB::connection('lin2db')
            ->table('user_auth')
            ->where('account', $login)
            ->update([
                'password' => DB::raw($password),
            ]);
    }

    public function unstuck($char_id, $x, $y, $z) {
        return DB::connection(gameservers())
            ->table('user_data')
            ->where('char_id', $char_id)
            ->update([
                'xloc' => $x,
                'yloc' => $y,
                'zloc' => $z,
            ]);
    }

    public function getOnline()
    {
        $servers = config('app.pts_servers'); // Получаем список серверов из конфига
        $onlineData = [];

        foreach ($servers as $key => $server) {
            try {
                $online = DB::connection($key) // Устанавливаем подключение к текущему серверу
                ->table('user_data')
                    ->whereColumn('login', '<', 'logout')
                    ->count();

                $onlineData[$server['server_name']] = $online; // Добавляем в массив результатов
            } catch (\Exception $e) {
                $onlineData[$server['server_name']] = 'Ошибка подключения';
            }
        }

        return $onlineData;
    }
}
