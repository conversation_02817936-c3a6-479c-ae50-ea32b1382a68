<?php

namespace App\Http\Controllers;

use App\Models\ModelFactory;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class WarehouseController extends Controller
{
    public function index() {

        if(!\Auth::check()) {}

        $user_id = auth()->user()->id;

        $warehouse_items = Warehouse::where('user_id', $user_id)->where('status', '=', 0)->get();

        $player_list = ModelFactory::l2jModel(config('app.l2server_version'))->getPlayersList(auth()->user()->id);

        return view('template::dashboard.warehouse', [
            'playerList' => $player_list,
            'warehouse_items' => $warehouse_items
        ]);
    }

    public function sendCoinsInGame(Request $request) {
        if(!\Auth::check()) {}

        if($request->count <= 0) {
            return redirect()->back()->withErrors(['error_balance' => 'Недостаточно средств']);
        }

        $request->validate([
            'char_id' => 'required',
            'count' => 'required|numeric|min:1'
        ]);

        $currentUser = auth()->user();

        if ($request->count > $currentUser->balance) {
            return redirect()->back()->withErrors(['error_balance' => 'Недостаточно средств']);
        }

        // Проверка, что пользователь не заблокирован для отправки монет
        if (Cache::has('send_coins_' . $currentUser->id)) {
            return redirect()->back()->withErrors(['error_balance' => 'Пожалуйста, подождите, пока предыдущая операция завершится.']);
        }

        // Блокировка пользователя на 10 секунд
        Cache::put('send_coins_' . $currentUser->id, true, 10);

        $con = ModelFactory::l2jModel(config('app.l2server_version'));

        $con->sendCoins($request->char_id, config('app.pay_system.item_id'), $request->count);

        // Обновление баланса текущего пользователя
        $currentUser->decrement('balance', $request->count);

        // Разблокировка пользователя
        Cache::forget('send_coins_' . $currentUser->id);

        $coin_name = config('app.custom_config.donate_coin_name');
        $char_name = $con->getCharNameByCharId($request->char_id);

        return redirect()->back()->withErrors([
            'success' => __('success_delivery', [
                'count' => $request->count,
                'coin_name' => $coin_name,
                'char_name' => $char_name
            ])
        ]);
    }

    public function sendItems(Request $request) {
        if (!\Auth::check()) {
            // Обработка случая, когда пользователь не аутентифицирован
            return redirect()->back()->with('error', 'Вы не авторизованы.');
        }

        $char_id = $request->input('char_id');

        // Проверяем наличие выбранных предметов
        if (!$request->has('selected_items')) {
            return redirect()->back()->with('error', 'Выберите предметы для отправки в игру.');
        }

        // Получаем выбранные предметы
        $selectedItems = $request->input('selected_items');

        // Если список предметов пуст, возвращаем ошибку
        if (empty($selectedItems)) {
            return redirect()->back()->with('error', 'Выберите предметы для отправки в игру.');
        }

        // Проверка, что пользователь не заблокирован для отправки монет
        if (Cache::has('send_items_to_game_' . $char_id)) {
            return redirect()->back()->withErrors(['error_balance' => 'Пожалуйста, подождите, пока предыдущая операция завершится.']);
        }

        // Блокировка пользователя на 10 секунд
        Cache::put('send_items_to_game_' . $char_id, true, 10);

        try {
            DB::transaction(function () use ($request, $char_id, $selectedItems) {
                // Проверяем каждый item_id выбранных предметов
                foreach ($selectedItems as $itemId) {
                    // Проверяем, существует ли предмет в базе данных
                    $item = Warehouse::find($itemId);

                    if ($item) {
                        ModelFactory::l2jModel(config('app.l2server_version'))->addItemBonus($char_id, $item->item_id, $item->count, $item->enchant, gameservers());

                        $item->delete();
                    }
                }

                // Предметы были успешно отправлены в игру
                // Возврата нет, так как редирект будет выполнен после завершения транзакции
            });

            // Разблокировка пользователя
            Cache::forget('send_items_to_game_' . $char_id);

            // Редирект после успешной операции
            return redirect()->back()->with('success', __('Items have been successfully delivered to the game.'));
        } catch (\Exception $e) {
            // Обработка ошибок транзакции
            return redirect()->back()->with('error', 'Произошла ошибка при отправке предметов: ' . $e->getMessage());
        }
    }

}
