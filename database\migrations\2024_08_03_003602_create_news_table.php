<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Заголовок новости
            $table->string('image')->nullable(); // Изображение новости
            $table->text('short_desc')->nullable(); // Описание новости
            $table->text('description')->nullable(); // Описание новости
            $table->string('slug')->unique(); // ЧПУ ссылка на новость
            $table->string('forum_link')->nullable(); // Ссылка на форум
            $table->timestamp('published_at')->nullable(); // Дата публикации
            $table->string('lang')->nullable(); // Англ или рус новость?
            $table->enum('status', ['draft', 'published', 'hidden'])->default('draft'); // Статус новости
            $table->enum('show_sector', ['default', 'main', 'other'])->default('default'); // Показы для главной или просто новости
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news');
    }
};
