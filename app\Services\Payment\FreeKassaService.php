<?php

namespace App\Services\Payment;

use App\Services\Payment\PaymentProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class FreeKassaService
{
    protected $paymentProcessingService;

    public function __construct(PaymentProcessingService $paymentProcessingService)
    {
        $this->paymentProcessingService = $paymentProcessingService;
    }

    public function createPayment($payment_id, $count)
    {
        $currency = config('app.pay_system.freekassa_currency'); // Валюта
        $coin_price = config('app.pay_system.coin_price')[$currency]; // Цена за единицу монеты
        // Расчет суммы платежа
        $sum = (int)ceil($count * $coin_price);

        $fk_id = config('app.pay_system.freekassa_project_id'); // ID проекта
        $fk_secret = config('app.pay_system.freekassa_secret_key'); // Секретный ключ

        $oa = $sum;
        $o = config('app.pay_system.freekassa_desc') . $count . ' ' . config('app.custom_config.donate_coin_name');
        $sign = md5($fk_id . ':' . $oa . ':' . $fk_secret . ':' . $currency . ':' . $o);

        return "https://pay.freekassa.com/?m={$fk_id}&oa={$oa}&o={$o}&currency={$currency}&us_payment_id={$payment_id}&s={$sign}";
    }

    public function handleWebhook(Request $request)
    {
        try {
//            // Проверка IP-адреса откуда идет вебхук
//            if (!$this->isAllowedIp($request->ip())) {
//                Log::warning('FreeKassa: Access denied for IP', ['ip' => $request->ip()]);
//                return response('Access Denied', 403);
//            }

            $merchant_id = config('app.pay_system.freekassa_project_id');
            $secret_key_2 = config('app.pay_system.freekassa_secret_key_2');

            $amount = $request->input('AMOUNT');
            $merchant_order_id = $request->input('MERCHANT_ORDER_ID');
            $received_sign = $request->input('SIGN');

            $sign = md5($merchant_id . ':' . $amount . ':' . $secret_key_2 . ':' . $merchant_order_id);

            if ($sign !== $received_sign) {
                Log::error('FreeKassa: Invalid signature', ['order_id' => $merchant_id]);
                return response('Invalid signature', 403);
            }

            $payment_id = $request->input('us_payment_id');

            $this->paymentProcessingService->processPayment($payment_id);

            return response('YES', 200);
        } catch (Exception $e) {
            Log::error('FreeKassa: Error handling webhook', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }
    }

    // разрешенные IP для отправки для отправки вебхука
    private function isAllowedIp(string $ip): bool
    {
        $allowedIps = [
            '***************',
            '**************',
            '**************',
            '*************',
        ];

        return in_array($ip, $allowedIps);
    }
}
