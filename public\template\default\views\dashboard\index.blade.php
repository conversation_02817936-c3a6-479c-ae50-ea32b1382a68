@section('title', __('Personal account'))
@extends('template::layouts.dashboard')

@section('content')
    @if(session('status'))
        <div class="alert alert-success">
            <p>{{ session('status') }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif

    @if(isset($error_message) && $error_message)
        <div class="alert alert-danger">
            <p>{{ $error_message }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif

    @if($errors->has('unstuck'))
        <div class="alert alert-danger">
            <p>{{ $errors->first('unstuck') }}</p>
            <a href="{{ url()->current() }}" class="close"></a>
        </div>
    @endif

    <div class="create_game_account">
        <p>{{ __('Game accounts') }}</p>
        <button class="open-popup btn-primary" data-popup-id="popup1">
            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-plus"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
            <span>{{ __('Create game account') }}</span>
        </button>
    </div>

    <div class="master-account-table">
        @foreach($game_accounts as $ga)
            <div class="master-acc-item">
                <div class="change-game-password-block">
                    <div class="master-name-plus">
                        <p class="plus-toggle active"></p>
                        <p id="game_login">{{ $ga->login }}</p>
                    </div>
                    <button class="open-popup btn-gray-no-border" data-popup-id="change-game-pass-popup"><span>{{ __('Change password') }}</span></button>
                </div>

                @if($ga->player_list->isNotEmpty())
                    <div class="lin2Table active">
                        <table class="omg-table">
                            <thead>
                            <tr>
                                <th>{{ __('Character') }}</th>
                                <th>{{ __('Class') }}</th>
                                <th>{{ __('Level') }}</th>
                                <th>{{ __('Clan') }}</th>
                                <th>PvP / PK</th>
                                <th>{{ __('Teleport') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($ga->player_list as $ga_player)
                                <tr>
                                    <td>{{ $ga_player->char_name }}</td>
                                    <td>{{ $ga_player->class_id }}</td>
                                    <td>{{ $ga_player->level }}</td>
                                    <td>{{ $ga_player->clan_name ?: '-' }}</td>
                                    <td>{{ $ga_player->pvpkills }} / <span class="pk-count">{{ $ga_player->pkkills }}</span></td>
                                    <td>
                                        <form action="{{ route('unstuck.store') }}" method="POST">
                                            @csrf
                                            <input type="hidden" name="char_id" value="{{ $ga_player->obj_Id }}">
                                            <button type="submit" class="btn-gray-no-border">{{ __('Teleport') }}</button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <table class="omg-table error">
                        <tbody>
                        <tr>
                            <td align="center">{{ __('No characters yet. Enter the game.') }}</td>
                        </tr>
                        </tbody>
                    </table>
                @endif

            </div>
        @endforeach
    </div>

    <div class="popup-overlay" id="popup1">
        <div class="popup-content">
            <div class="popup-title-block">
                <div class="popup-title">{{ __('Create game account') }}</div>
                <span class="popup-close">
                <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-x"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12" /><path d="M6 6l12 12" /></svg>
            </span>
            </div>
            <form id="game-account-form">
                @csrf

                <!-- Name -->
                <div class="form-group">
                    <input id="name" class="form-control" type="text" name="name" required autofocus autocomplete="name" placeholder="{{ __('Your game login') }}" />
                    <p class="text-danger" id="name-error"></p>
                </div>

                <!-- Password -->
                <div class="form-group">
                    <div class="password-group">
                        <input id="password" class="form-control" type="password" name="password" required autocomplete="new-password" placeholder="{{ __('Password') }}" />
                        <span class="input-group-text password-toggle" data-target="password" data-state="hidden"></span>
                    </div>
                    <p class="text-danger" id="password-error"></p>
                </div>

                <!-- Password Confirmation -->
                <div class="form-group">
                    <div class="password-group">
                        <input id="password_confirmation" class="form-control" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="{{ __('Confirm Password') }}" />
                        <span class="input-group-text password-toggle" data-target="password_confirmation" data-state="hidden"></span>
                    </div>
                    <p class="text-danger" id="password-confirmation-error"></p>
                </div>

                <!-- Log In Buttons -->
                <div class="form-group login_button">
                    <button type="submit" class="btn-primary">
                        <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-corner-up-right"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 18v-6a3 3 0 0 1 3 -3h10l-4 -4m0 8l4 -4" /></svg>
                        <span>{{ __('Create account') }}</span>
                    </button>
                </div>

                <!-- Success Message -->
                <p class="text-success" id="success-message"></p>
            </form>
        </div>
    </div>

    <div class="popup-overlay" id="change-game-pass-popup">
        <div class="popup-content">
            <div class="popup-title-block">
                <div class="popup-title">
                    {{ __('Change password for') }} <span id="login"></span>
                </div>
                <span class="popup-close">
                <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-x"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12" /><path d="M6 6l12 12" /></svg>
               </span>
            </div>
            <form id="change-password-form" method="POST">
                @csrf

                <input type="hidden" id="popup-login" name="game_login">

                <!-- Password -->
                <div class="form-group">
                    <div class="password-group">
                        <input id="change-password" class="form-control" type="password" name="password" required autocomplete="new-password" placeholder="{{ __('Password') }}" />
                        <span class="input-group-text password-toggle" data-target="change-password" data-state="hidden"></span>
                    </div>
                    <p class="text-danger" id="change-password-error"></p>
                </div>

                <!-- Password Confirmation -->
                <div class="form-group">
                    <div class="password-group">
                        <input id="change-password-confirmation" class="form-control" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="{{ __('Confirm Password') }}" />
                        <span class="input-group-text password-toggle" data-target="change-password-confirmation" data-state="hidden"></span>
                    </div>
                    <p class="text-danger" id="change-password-confirmation-error"></p>
                </div>

                <!-- Log In Buttons -->
                <div class="form-group login_button">
                    <button type="submit" class="btn-primary">
                        <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-key-off"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.17 6.159l2.316 -2.316a2.877 2.877 0 0 1 4.069 0l3.602 3.602a2.877 2.877 0 0 1 0 4.069l-2.33 2.33" /><path d="M14.931 14.948a2.863 2.863 0 0 1 -1.486 -.79l-.301 -.302l-6.558 6.558a2 2 0 0 1 -1.239 .578l-.175 .008h-1.172a1 1 0 0 1 -.993 -.883l-.007 -.117v-1.172a2 2 0 0 1 .467 -1.284l.119 -.13l.414 -.414h2v-2h2v-2l2.144 -2.144l-.301 -.301a2.863 2.863 0 0 1 -.794 -1.504" /><path d="M15 9h.01" /><path d="M3 3l18 18" /></svg>
                        <span>{{ __('Change password') }}</span>
                    </button>
                </div>

                <!-- Success Message -->
                <p class="text-success" id="change-success-message"></p>
            </form>
        </div>
    </div>


    <script>
        const csrfToken = "{{ csrf_token() }}";

        // Function to handle form submission for creating game account
        document.getElementById('game-account-form').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            // Clear previous errors
            document.getElementById('name-error').textContent = '';
            document.getElementById('password-error').textContent = '';
            document.getElementById('password-confirmation-error').textContent = '';
            document.getElementById('success-message').textContent = '';

            let formData = new FormData(this);

            fetch("{{ route('game-account.store') }}", {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": csrfToken,
                    "Accept": "application/json"
                },
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.errors) {
                        if (data.errors.name) {
                            document.getElementById('name-error').textContent = data.errors.name[0];
                        }
                        if (data.errors.password) {
                            document.getElementById('password-error').textContent = data.errors.password[0];
                        }
                        if (data.errors.password_confirmation) {
                            document.getElementById('password-confirmation-error').textContent = data.errors.password_confirmation[0];
                        }
                    } else if (data.message) {
                        document.getElementById('success-message').textContent = data.message;
                        // Optionally close the popup or reset the form
                        document.getElementById('game-account-form').reset();
                        // Reload the page after 1 second
                        setTimeout(() => location.reload(), 1000);
                    }
                })
                .catch(error => console.error('Error:', error));
        });

        // Function to handle form submission for changing game account password
        document.getElementById('change-password-form').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            // Clear previous errors
            document.getElementById('change-password-error').textContent = '';
            document.getElementById('change-password-confirmation-error').textContent = '';
            document.getElementById('change-success-message').textContent = '';

            let formData = new FormData(this);

            fetch("{{ route('game-account.change-password') }}", {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": csrfToken,
                    "Accept": "application/json"
                },
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.errors) {
                        if (data.errors.password) {
                            console.log(data.errors);
                            document.getElementById('change-password-error').textContent = data.errors.password[0];
                        }
                        if (data.errors.password_confirmation) {
                            document.getElementById('change-password-confirmation-error').textContent = data.errors.password_confirmation[0];
                        }
                    } else if (data.message) {
                        document.getElementById('change-success-message').textContent = data.message;
                        // Optionally close the popup or reset the form
                        document.getElementById('change-password-form').reset();
                        // Reload the page after 1 second
                        setTimeout(() => location.reload(), 1000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An unexpected error occurred.');
                });
        });

        document.querySelectorAll('.open-popup').forEach(button => {
            button.addEventListener('click', function() {
                const popupId = this.getAttribute('data-popup-id');
                const popup = document.getElementById(popupId);

                // Проверьте, найден ли элемент popup
                if (popup) {
                    popup.style.display = 'block';

                    // Получаем родительский div и проверяем его наличие
                    const parentDiv = this.closest('.change-game-password-block');
                    if (parentDiv) {
                        const gameLogin = parentDiv.querySelector('#game_login')?.textContent || '';

                        // Устанавливаем значения в соответствующие поля внутри попапа
                        document.getElementById('login').textContent = gameLogin;
                        document.getElementById('popup-login').value = gameLogin;
                    }
                }
            });
        });

        document.querySelectorAll('.popup-close').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.popup-overlay').style.display = 'none';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.change-game-password-block').forEach(function(item) {
                const lin2Table = item.closest('.master-acc-item').querySelector('.lin2Table'); // Ищем таблицу внутри master-acc-item
                const plusToggle = item.querySelector('.plus-toggle'); // Находим плюс-тоггл

                item.addEventListener('click', function() {
                    // Переключаем класс 'active' на таблице
                    lin2Table.classList.toggle('active');

                    // Анимируем плюсик -> минусик
                    plusToggle.classList.toggle('active'); // Убедитесь, что у класса .minus есть стили для отображения минуса
                });
            });
        });


    </script>


@endsection
