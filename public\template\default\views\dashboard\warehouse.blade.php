@section('title', __('My Warehouse'))
@extends('template::layouts.dashboard')
@section('content')
    <section id="send-to-game" class="card">
        @include('template::server_switcher')
        <div class="title">{{ __('Here, you can transfer the coins purchased via donation into the game.') }}</div>
        @if($playerList->isNotEmpty())
            <form action="{{ route('warehouse.send-to-game') }}" method="POST">
                @csrf
                <div class="send-to-game">
                    <div class="form-group">
                        <select name="char_id" class="form-control">
                            @foreach($playerList as $pl)
                                <option value="{{ $pl->char_id }}">{{ $pl->char_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" name="count" placeholder="{{ __('Amount of coins') }}">
                    </div>
                    <div class="form-group login_button">
                        <button type="submit" class="btn-primary"><span>{{ __('Send to game') }}</span></button>
                    </div>
                </div>
                @if($errors->has('success'))
                    <div class="alert alert-success">
                        <p>{{ $errors->first('success') }}</p>
                        <a href="{{ url()->current() }}" class="close"></a>
                    </div>
                @endif
                @if($errors->has('error_balance'))
                    <div class="alert alert-danger">
                        <p>{{ $errors->first('error_balance') }}</p>
                        <a href="{{ url()->current() }}" class="close"></a>
                    </div>
                @endif
            </form>
        @else
            <table class="omg-table error">
                <tbody>
                <tr>
                    <td align="center">{{ __('No characters yet. Enter the game.') }}</td>
                </tr>
                </tbody>
            </table>
        @endif
    </section>

    @if($playerList->isNotEmpty())
        <section id="warehouse" class="card">
            @if(session('success_starterpack'))
                <div class="alert alert-success">
                    <p>{{ session('success_starterpack') }}</p>
                    <a href="{{ url()->current() }}" class="close"></a>
                </div>
            @endif

            <div class="title">
                <p>{{ __('Here, you can transfer the items purchased via donation into the game.') }}</p>
            </div>
            <form id="sendItemsForm" method="post" action="{{ route('send.items') }}">
                <div class="send-to-game">
                    <div class="form-group">
                        <select name="char_id" class="form-control">
                            @foreach($playerList as $pl)
                                <option value="{{ $pl->char_id }}">{{ $pl->char_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group login_button">
                        <button type="submit" class="btn-primary"><span>{{ __('Send to game') }}</span></button>
                    </div>
                </div>

                <div class="warehouse-items-all">
                    @csrf
                    <input type="checkbox" id="select_all" name="select_all">
                    <label for="select_all">{{ __('Select All') }}</label>
                    @foreach($warehouse_items as $item)
                        <div class="warehouse-item">
                            <input type="checkbox" class="item_checkbox" id="item_{{ $item->id }}" name="selected_items[]" value="{{ $item->id }}">
                            <img src="{{ item_icon($item->item_id) }}" alt="{{ itemName($item->item_id) }}">
                            <label for="item_{{ $item->id }}">{{ itemName($item->item_id) }} ({{ $item->count }})</label>
                        </div>
                    @endforeach
                </div>

                @if(session('success'))
                    <div class="alert alert-success">
                        <p>{{ session('success') }}</p>
                        <a href="{{ url()->current() }}" class="close"></a>
                    </div>
                @endif
                @if(session('error'))
                    <div class="alert alert-success">
                        <p>{{ session('error') }}</p>
                        <a href="{{ url()->current() }}" class="close"></a>
                    </div>
                @endif
            </form>
        </section>
    @endif

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('select_all').addEventListener('change', function() {
                var checkboxes = document.querySelectorAll('.item_checkbox');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = document.getElementById('select_all').checked;
                });
            });
        });
    </script>

@endsection
