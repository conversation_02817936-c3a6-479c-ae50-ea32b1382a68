<?php

namespace App\Http\Controllers;

use App\Helpers\CacheDHelper;
use App\Models\ModelFactory;
use Carbon\CarbonInterval;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    public function index()
    {
        $model = ModelFactory::l2jModel(config('app.l2server_version'));

        $game_accounts = $model->getGameAccounts(auth()->user()->id);

        $filtered_game_accounts = [];
        foreach ($game_accounts as $ga) {
            $player_list = $model->getCharNameByLogin($ga->login);
            if (!empty($player_list)) {
                $ga->player_list = $player_list;
                $filtered_game_accounts[] = $ga;
            }
        }

        return view('template::services', [
            'game_accounts' => $filtered_game_accounts,
            'player_list' => $player_list ?? [],
        ]);
    }

    public function changeName(Request $request)
    {
        $request->validate([
            'old_name' => ['required', 'string'],
            'new_name' => ['required', 'string', 'min:3', 'max:16'],
        ]);

        try {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));
            $login = $model->getGameLoginByCharName($request->old_name); // Получаем login по char_name
            $char = $model->getCharacterNameByLogin($login); // Получаем char_name по login
            $model->checkCharNameExist($request->new_name); // проверяем новый Ник Нейм на Существование в таблице
            $char_id = $model->getCharIdByName($request->old_name);

            // OLD NAME & NEW NAME - проверка что ник существует на данном аккаунте
            if ($request->old_name !== $char->char_name) {
                return back()->withErrors(['custom_error' => __('Name Mismatch')], 'changeNameForm');
            }

            $currentUser = auth()->user();
            $change_name_price = config('app.services.change_name_price');

            if ($currentUser->balance < $change_name_price) {
                return back()->withErrors(['custom_error' => __('Insufficient Balance', [
                    'coin_name' => config('app.custom_config.donate_coin_name')
                ])], 'changeNameForm');
            }

            $currentUser->decrement('balance', $change_name_price);

            $cacheDHelper = new CacheDHelper;

            $cacheDHelper->kickCharacter($char_id);
            $response = $cacheDHelper->changeCharacterName($char_id, $request->new_name);

            if ($response) {
                return back()->with('success', __('Change Name Success', [
                    'old_name' => $request->old_name,
                    'new_name' => $request->new_name
                ]));
            }
        } catch (\Exception $e) {
            return back()->withErrors(['custom_error' => __('Change Name Error')], 'changeNameForm');
        }
    }

    public function changeGender(Request $request)
    {
        $request->validate([
            'char_name' => ['required', 'string'],
        ]);

        try {
            $model = ModelFactory::l2jModel(config('app.l2server_version'));
            $login = $model->getGameLoginByCharName($request->char_name); // Получаем login по char_name
            $char = $model->getCharacterNameByLogin($login); // Получаем char_name по login
            $char_id = $model->getCharIdByName($request->char_name);

            $mod_char = $model->getModChar2Packet($char_id);
            $current_gender = (int) $mod_char->gender; // Приводим к числу
            $new_gender = $current_gender === 0 ? 1 : 0; // Меняем пол

            $race = $mod_char->race;
            $class = $mod_char->class;

            // Проверка имени персонажа
            if ($request->char_name !== $char->char_name) {
                return back()->withErrors(['custom_error' => __('Name Mismatch')], 'changeGenderForm');
            }

            $currentUser = auth()->user();
            $change_gender_price = config('app.services.change_gender');

            if ($currentUser->balance < $change_gender_price) {
                return back()->withErrors([
                    'custom_error' => __('Insufficient Balance', [
                        'coin_name' => config('app.custom_config.donate_coin_name'),
                    ])
                ], 'changeGenderForm');
            }

            $currentUser->decrement('balance', $change_gender_price);

            $face = 0;
            $hair_shape = 0;
            $hair_color = 0;

            $cacheDHelper = new CacheDHelper;
            $cacheDHelper->kickCharacter($char_id);

            $response = $cacheDHelper->ModChar2Packet($char_id, $new_gender, $race, $class, $face, $hair_shape, $hair_color);

            if ($response) {
                $old_gender = $current_gender === 0 ? __('Male') : __('Female');
                $new_gender_label = $new_gender === 0 ? __('Male') : __('Female');

                return back()->with('success_change_gender', __('Gender changed successfully from :old_gender to :new_gender.', [
                    'old_gender' => $old_gender,
                    'new_gender' => $new_gender_label,
                ]));
            }

            return back()->withErrors(['custom_error' => __('Failed to change gender')], 'changeGenderForm');
        } catch (\Exception $e) {
            return back()->withErrors(['custom_error' => __('An error occurred while changing gender')], 'changeGenderForm');
        }
    }



}
